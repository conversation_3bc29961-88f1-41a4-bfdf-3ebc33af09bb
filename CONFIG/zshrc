# Path to your oh-my-zsh installation.
export ZSH=~/.oh-my-zsh

# Set name of the theme to load.
# Look in ~/.oh-my-zsh/themes/
# Optionally, if you set this to "random", it'll load a random theme each
# time that oh-my-zsh is loaded.
ZSH_THEME="robbyrussell"

# Uncomment the following line to use case-sensitive completion.
# CASE_SENSITIVE="true"

# Uncomment the following line to use hyphen-insensitive completion. Case
# sensitive completion must be off. _ and - will be interchangeable.
# HYPHEN_INSENSITIVE="true"

# Uncomment the following line to disable bi-weekly auto-update checks.
# DISABLE_AUTO_UPDATE="true"

# Uncomment the following line to change how often to auto-update (in days).
# export UPDATE_ZSH_DAYS=13

# Uncomment the following line to disable colors in ls.
# DISABLE_LS_COLORS="true"

# Uncomment the following line to disable auto-setting terminal title.
# DISABLE_AUTO_TITLE="true"

# Uncomment the following line to enable command auto-correction.
# ENABLE_CORRECTION="true"

# Uncomment the following line to display red dots whilst waiting for completion.
# COMPLETION_WAITING_DOTS="true"

# Uncomment the following line if you want to disable marking untracked files
# under VCS as dirty. This makes repository status check for large repositories
# much, much faster.
# DISABLE_UNTRACKED_FILES_DIRTY="true"

# Uncomment the following line if you want to change the command execution time
# stamp shown in the history command output.
# The optional three formats: "mm/dd/yyyy"|"dd.mm.yyyy"|"yyyy-mm-dd"
# HIST_STAMPS="mm/dd/yyyy"

# Would you like to use another custom folder than $ZSH/custom?
# ZSH_CUSTOM=/path/to/new-custom-folder

# Which plugins would you like to load? (plugins can be found in ~/.oh-my-zsh/plugins/*)
# Custom plugins may be added to ~/.oh-my-zsh/custom/plugins/
# Example format: plugins=(rails git textmate ruby lighthouse)
# Add wisely, as too many plugins slow down shell startup.

plugins=(
    git autojump
    zsh-autosuggestions
    sudo
    copypath
    copyfile
    copybuffer
    dirhistory
    history
)
[[ -s /home/<USER>/.autojump/etc/profile.d/autojump.sh ]] && source /home/<USER>/.autojump/etc/profile.d/autojump.sh
autoload -U compinit && compinit -u


# User configuration
source $ZSH/oh-my-zsh.sh

# You may need to manually set your language environment
export LANG=en_US.UTF-8

# Setting for the new UTF-8 terminal support in Lion
LC_CTYPE=en_US.UTF-8
LC_ALL=en_US.UTF-8


# PATH VARIOS
export PATH=${PATH}:/usr/local/sbin
export PATH=${PATH}:/usr/local/bin
export PATH=${PATH}:/usr/sbin
export PATH=${PATH}:/usr/bin
export PATH=${PATH}:/sbin
export PATH=${PATH}:/bin
export PATH=${PATH}:/usr/games
export PATH=${PATH}:/usr/local/games
export PATH=${PATH}:$HOME/.composer/vendor/bin
export PATH=${PATH}:$HOME/Apps/diff-so-fancy
export PATH=${PATH}:$HOME/.local/bin
export PATH=${PATH}:/bin
export PATH=${PATH}:/snap/bin
export PATH=${PATH}:$HOME/Apps/selenium

# PATH PARA ANDROID, JAVA Y PHONEGAP
export ANDROID_SDK_ROOT=$HOME/Apps/android-sdk-linux
export PATH=${PATH}:$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$ANDROID_SDK_ROOT/cmdline-tools/tools/bin
export PATH=${PATH}:$HOME/Apps/android-sdk-linux/build-tools
export PATH=${PATH}:$HOME/Apps/android-sdk-linux/sources
export PATH=${PATH}:$HOME/Apps/android-sdk-linux/platforms
export JAVA_HOME="/usr/lib/jvm/adoptopenjdk-8-hotspot-amd64"
export USE_GKE_GCLOUD_AUTH_PLUGIN=True
export HISTIGNORE="ls:cd:exit"


# Set personal aliases, overriding those provided by oh-my-zsh libs,
alias services="cd $HOME/www/saasargentina/services"
alias acc="cd $HOME/www/saasargentina/services/acc"
alias api="cd $HOME/www/saasargentina/services/api"
alias saas="cd $HOME/www/saasargentina/services/app"
alias informes="cd $HOME/www/saasargentina/services/informes"
alias login="cd $HOME/www/saasargentina/services/login"
alias scripts="cd $HOME/www/saasargentina/services/scripts"

alias crono="cd $HOME/www/cronometrajeinstantaneo/"
alias admin="cd $HOME/www/cronometrajeinstantaneo/admin"
alias app="cd $HOME/www/cronometrajeinstantaneo/app"
alias wp="cd $HOME/www/cronometrajeinstantaneo/wp"

alias www="cd $HOME/www"
alias desk="cd $HOME/desk"
alias woo="cd $HOME/www/woocommerce/wp-content/plugins"

alias gch="git checkout"
alias gst="git status"
alias gb="git branch"
alias gc="git commit -am"
alias gpo="git push origin"
alias gpl="git pull --rebase origin"
alias gpo="git push origin"
alias gm="git merge"
alias gd="git diff"
alias gdl="git diff HEAD~1 HEAD"
alias gl="git log --stat --pretty=oneline"
alias gfo="git fetch -p origin"

alias prod_saas="ssh ec2-user@saas-prod"
alias prod_saas_bastion="ssh -i ~/.ssh/desarrollo.pem <EMAIL>"
alias prod_hertruethis="ssh -i ~/.ssh/desarrollo.pem ec2-user@saas-hertruethis"
alias prod_crono="ssh <EMAIL>"
alias prod_agente="gcloud compute ssh --zone 'us-central1-c' 'agente' --project 'cronometrajeinstantaneo-151420'"
alias prod_donweb="echo 'Qu9jfvVrU7YW' | xclip -selection c && ssh <EMAIL>"

alias hertruethis=echo\ "Contraseña Super Maestra Hertruethis"\ \|\ xclip\ -sel\ clip
alias files="find . -type f \| wc -l"
alias network="sudo service network-manager restart"
alias art="php artisan"
alias untar="tar -zxvf "
alias consumo="sudo nethogs -v 3"
alias db="sudo mysql -u root -p"

alias agi="sudo apt-get install"
alias aup="sudo apt-get update"
alias l="exa -lah"
alias prueba="code $HOME/www/prueba.php"
alias hacer="code ~/MEGA/HACER.todo"
alias hibernate="sudo systemctl hibernate -i"
alias bluetooth_restart="sudo systemctl restart bluetooth"
alias phpunit="./vendor/bin/phpunit"
alias tail_error="sudo tail /var/log/apache2/error.log"
alias minecraft="java -jar ~/Apps/Minecraft/TLauncher-2.82.jar"
alias find="echo Mejor usa: fd"
alias nano="echo Mejor usa mcedit con e"
alias e="mcedit"
alias sz="source ~/.zshrc"
alias rdiff="rsync -avnc --delete /home/<USER>/MEGA/ <EMAIL>:/home/<USER>/MEGA/"
alias rup="rsync -avz /home/<USER>/MEGA/ <EMAIL>:/home/<USER>/MEGA/"
alias rown="ssh <EMAIL> 'chown -R andresmaiden:www-data /home/<USER>/MEGA/BRAIN'"
alias rdown="rsync -avz <EMAIL>:/home/<USER>/MEGA/ /home/<USER>/MEGA/"
alias transcribir='source ~/Apps/transcribir/venv/bin/activate && python ~/Apps/transcribir/transcribir.py'


alias ai="php -f /home/<USER>/www/andresmisiak/ai/ai.php"
# alias f="fabric"
alias copy='xsel --clipboard --input'
alias paste='xsel --clipboard --output'
# function pattern () { code $HOME/.config/fabric/patterns/$1/system.md; }
# function fe () { echo $2 | fabric -sp $1; }
# rsync -a ~/MEGA/BRAIN/AI/* ~/.config/fabric/patterns

# FUNCIONES VARIAS
function gdiff () { git diff $1 --color | diff-so-fancy }

function gh () {
    if [ -e "hash" ]; then
        commit=$(git rev-parse HEAD)
        echo $commit > hash
        git commit -am "Update hash"
    else
        echo "Error: File 'hash' does not exist in the current directory."
        return 1
    fi
}

function saas_hash () {
    echo "Estoy usando gh de git hash ahora"
}

function migrate () {acc; ./command.php migrate $1 $2 $3 $4 $5; cd -;}
function nuevophp () {acc; ./command.php nuevophp $1; cd -;}
function nuevosql () {acc; ./command.php nuevosql $1; cd -;}
function dl () {
    file=$1

    if [ "$file" != 'api-ml' ] && [ "$file" != 'api-v1' ] && [ "$file" != 'crono' ] && [ "$file" != 'saas' ] && [ "$file" != 'laravel' ];
    then
        echo Incorrect file: api-ml, api-v1, crono, saas, laravel
        return 0
    fi

    if [ -n "$2" ]
    then
        d=$2
    else
        d=$(date "+%Y-%m-%d")
    fi

    if [ "$file" = 'api-ml' ]
    then
        scp ec2-user@saas-prod:/saas/customer/services/acc/empresas/logs/api-ml/$d.log $HOME/SOPORTE/api-ml_$d.log
        code $HOME/SOPORTE/api-ml_$d.log
    fi

    if [ "$file" = 'api-v1' ]
    then
        scp ec2-user@saas-prod:/saas/customer/services/acc/empresas/logs/api-v1/$d.log $HOME/SOPORTE/api-v1_$d.log
        code $HOME/SOPORTE/api-v1_$d.log
    fi

    if [ "$file" = 'crono' ]
    then
        cd $HOME/SOPORTE
        mv cronometrajeinstantaneo.{sql,old}
        scp -i ~/.ssh/andresmaiden2 <EMAIL>:/home/<USER>/backups/backup.sql.gz cronometrajeinstantaneo.sql.gz
        gzip -d cronometrajeinstantaneo.sql.gz
        sudo mysql -u root -p cronometrajeinstantaneo < cronometrajeinstantaneo.sql
    fi

    if [ "$file" = 'saas' ]
    then

        if [ -n "$2" ]
        then
            id=$2
            cd $HOME/SOPORTE
            rm saas_$id.sql
            archivo_saas=$(ssh ec2-user@saas-prod "find /saas/customer/services/acc/backups -type f -name saas_${id}_* -print -quit")
            scp ec2-user@saas-prod:${archivo_saas} .
            archivo_local=$(fd saas_${id})
            mv ${archivo_local} saas_${id}.sql.gz
            gzip -d saas_$id.sql.gz
            sudo mysql -u root -p saas_${id} < saas_${id}.sql
        else
            echo Falta el idempresa
        fi
    fi

    if [ "$file" = 'laravel' ]
    then
        mv $HOME/SOPORTE/laravel.{log,old}
        scp -i ~/.ssh/andresmaiden2 <EMAIL>:/var/www/cronometrajeinstantaneo/admin/storage/logs/laravel.log $HOME/SOPORTE/laravel.log
        code $HOME/SOPORTE/laravel.log
    fi

}

function mkcd () {mkdir $1; cd $1;}
function php7 () {
    sudo a2dismod php8.3
    sudo a2enmod php7.1
    sudo service apache2 restart
    sudo update-alternatives --set php /usr/bin/php7.1
}
function php8 () {
    sudo a2dismod php7.1
    sudo a2enmod php8.3
    sudo service apache2 restart
    sudo update-alternatives --set php /usr/bin/php8.3
}

function open() {
    nohup nautilus -w $1 > /dev/null 2>&1 &
}

function memory_hack {
    php -f ~/www/andresmisiak/tools/memory-hack.php
}

function phonegap_release () {
    if [ -e platforms/android/app/build/outputs/apk/release/$1.keystore ]
    then
        b platforms/android/app/build/outputs/apk/release/key.txt
        phonegap build --release android;
        jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore platforms/android/app/build/outputs/apk/release/$1.keystore platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk $1
        mv platforms/android/app/build/outputs/apk/release/app-release{,-old}.apk
        ~/Apps/android-sdk-linux/build-tools/28.0.3/zipalign -v 4 platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk platforms/android/app/build/outputs/apk/release/app-release.apk
    else
        echo "No existe el archivo platforms/android/app/build/outputs/apk/release/$1.keystore"
    fi
}

function crono_build_apk () {
    cd $HOME/phonegap/cronometrajeinstantaneo;
    phonegap build;
    mv platforms/android/app/build/outputs/apk/debug/app-debug.apk $HOME/CRONO/descargas/cronometrajeinstantaneo-android-beta.apk;
    scp -i ~/.ssh/andresmaiden $HOME/CRONO/descargas/Cronometraje\ Instantaneo.debug.v2.9.8.apk <EMAIL>:/var/www/cronometrajeinstantaneo/www/descargas/cronometrajeinstantaneo.beta.v2.9.8.apk;
    cd -;
}

function crono_build_linux () {
    cd $HOME/www/cronometrajeinstantaneo/desktop;
    npm run package-linux;
    tar -zcvf release-builds/cronometrajeinstantaneo-linux-x64.tar.gz release-builds/cronometrajeinstantaneo-linux-x64
    scp -i ~/.ssh/andresmaiden $HOME/www/cronometrajeinstantaneo/desktop/release-builds/cronometrajeinstantaneo-linux-x64.tar.gz <EMAIL>:$HOME/www/cronometrajeinstantaneo/public/descargas/cronometrajeinstantaneo-linux-x64.tar.gz;
    cd -;
}

function crono_upload_descargas () {
    file=$1
    scp -i ~/.ssh/andresmaiden2 $file <EMAIL>:/var/www/cronometrajeinstantaneo/descargas/$file;
}

function crono_encoded () {
    cd $HOME/www;
    $HOME/Apps/ioncube_encoder5_basic_10.2/ioncube_encoder.sh -72 \
        --replace \
        --copy "@/*/" \
        --encode app/ \
        --encode bootstrap/ \
        --encode config/ \
        --encode old-includes/ \
        --encode public/ \
        --encode routes/ \
        --encode tests/ \
        --ignore .git/ \
        --ignore node_modules/ \
        --ignore database/ \
        cronometrajeinstantaneo/ -o www_encoded/;
    random=$(b /dev/urandom | tr -dc 'a-z' | fold -w 4 | head -n 1);
    tar -zcvf www_encoded_$random.tar.gz www_encoded;
    rm -Rf www_encoded;
    scp -i ~/.ssh/andresmaiden2 www_encoded_$random.tar.gz <EMAIL>:/var/www/cronometrajeinstantaneo/public/descargas/www_encoded_$random.tar.gz;
    # scp www_encoded_$random.tar.gz pi@192.168.0.10:/home/<USER>/www_encoded_$random.tar.gz;
    echo 'Descargar actualización y luego ELIMINAR EL ARCHIVO';
    echo "wget https://cronometrajeinstantaneo.com/descargas/www_encoded_$random.tar.gz"
    echo "tar -zxvf www_encoded_$random.tar.gz"
    echo 'sudo mv /var/www/{,old}www_encoded'
    echo 'sudo mv www_encoded /var/www'
    echo 'chmod -R 775 /var/www/www_encoded/storage'
    echo 'chmod -R 775 /var/www/www_encoded/bootstrap/cache'
    cd -;
}

function crono_logo () {
    scp -i ~/.ssh/andresmaiden2 $1 <EMAIL>:/var/www/cronometrajeinstantaneo/public/images/eventos/$1;
}

function art_clear () {
    art cache:clear
    art config:clear
    art view:clear
}

function somoshinchada {
    cd $HOME/www/mundial2018-vue;
    git pull origin master;
    git push origin master;
    npm run build;
    scp -r -i ~/.ssh/andresmaiden dist/* <EMAIL>:/var/www/somoshinchada/dist;
    cd -;
}

function deploy () {

    env=$1
    branch=$1

    if [ -z $env ]; then
        echo Specify env: prod, beta, alfa, master
        return 0
    fi

    if [ $env != 'prod' ] && [ $env != 'beta' ] && [ $env != 'alfa' ] && [ $env != 'master' ]; then
        echo Incorrect env: prod, beta, alfa, master
        return 0
    fi

    if [ $env = 'prod' ]; then
        random=$(cat /dev/urandom | tr -dc 'a-z' | fold -w 4 | head -n 1)
        read "reply?IS THIS PROD!? ($random): "
        if [[ $reply != $random ]]; then
            echo Canceling...
            return 0
        fi
    fi

    git checkout $branch
    git push origin $branch:$branch

    if [ $env != 'master' ]; then
        git push $env $branch:master;
    fi

    echo 'Ready, remember to migrate!';
}


# EJECUCIONES ESPECIFICAS
memory_hack


# AGREGADOS AUTOMÁTICAMENTE
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

# The next line updates PATH for the Google Cloud SDK.
if [ -f '/home/<USER>/google-cloud-sdk/path.zsh.inc' ]; then . '/home/<USER>/google-cloud-sdk/path.zsh.inc'; fi

# The next line enables shell command completion for gcloud.
if [ -f '/home/<USER>/google-cloud-sdk/completion.zsh.inc' ]; then . '/home/<USER>/google-cloud-sdk/completion.zsh.inc'; fi

LC_CTYPE=es_AR.UTF-8
LC_ALL=es_AR.UTF-8
LANG=es_AR.UTF-8
# if [ -f "/home/<USER>/.config/fabric/fabric-bootstrap.inc" ]; then . "/home/<USER>/.config/fabric/fabric-bootstrap.inc"; fi