@extends('layout')

@section('content')

<form name="inscripcion" method="post" enctype="multipart/form-data">
    <section class="datos">

        @if ($evento['inscripciones_texto'])
            {!! html_entity_decode($evento['inscripciones_texto']) !!}<hr>
        @endif

        <span class="obligatorios"><b>*</b> Los campos marcados con asterísco son obligatorios</span>
        <br><br>

        <h2>Datos del equipo *</h2>
        <label for="equipo">
            <span>Nombre del equipo</span>
            <input type="text" name="nombre" placeholder="" value="{{ $post['nombre'] }}" required="required" />
        </label>
        <hr>

        <label for="sexo">
            <span>Género *</span><br>
            @if ($categoria_sexo['equipofemenino'])
                <input type="radio" name="sexo" value="equipofemenino" required="required" {{ (isset($post['sexo']) && $post['sexo'] == 'equipofemenino') ? 'checked="checked"' : '' }} onchange="selectCategoria()" />
                <label for="equipofemenino">Femenino</label><br>
            @endif
            @if ($categoria_sexo['equipomasculino'])
                <input type="radio" name="sexo" value="equipomasculino" {{ (isset($post['sexo']) && $post['sexo'] == 'equipomasculino') ? 'checked="checked"' : '' }} onchange="selectCategoria()" />
                <label for="equipomasculino">Masculino</label><br>
            @endif
            @if ($categoria_sexo['equipootro'])
                <input type="radio" name="sexo" value="equipootro" {{ (isset($post['sexo']) && $post['sexo'] == 'equipootro') ? 'checked="checked"' : '' }} onchange="selectCategoria()" />
                <label for="equipootro">Otro</label><br>
            @endif
            @if ($categoria_sexo['equipomixto'])
                <input type="radio" name="sexo" value="equipomixto" {{ (isset($post['sexo']) && $post['sexo'] == 'equipomixto') ? 'checked="checked"' : '' }} onchange="selectCategoria()" />
                <label for="equipomixto">Mixto (ambos sexos)</label><br />
            @endif
        </label>

        <label for="idcarrera">
            <span>Carrera *</span><br>
            @foreach ($carreras as $carrera)
                <input type="radio" name="idcarrera" value="{{ $carrera['idcarrera'] }}" {{ !$carrera['inscripciones'] ? 'disabled="disabled"' : '' }} {{ (isset($post['idcarrera']) && $post['idcarrera'] == $carrera['idcarrera']) ? 'checked="checked"' : '' }} onchange="selectCategoria()" />
                <label for="{{ $carrera['idcarrera'] }}">{{ $carrera['nombre'] }}
                    @if (!$carrera['inscripciones'])
                        <span style="color:red">SIN CUPO</span>
                    @endif
                </label><br>
            @endforeach
        </label>

        <label for="idcategoria">
            <span>Categoría</span>
            <select name="idcategoria" id="categorias" class="">
                <option value="">Seleccione el sexo y participación para ver las categorías correspondientes</option>
            </select>
        </label>
        <input type="hidden" id="idcategoria_post" name="idcategoria_post" value="{{ $post['idcategoria'] }}">

        <div id="agregarCategoria">
            <label for="agregarCategoria">
                <input type="hidden" id="idcategorias" name="idcategorias" value="{{ $post['idcategorias'] }}">
                <input type="button" value="Agregar Carrera y Categoría" onclick="agregarCategoria()" />
                <span class="categoriasSeleccionadas">Categorías seleccionadas:</span>
                <ul id="listaCategorias"></ul>
            </label>
        </div>

        <div class="form-group">
            <label for="codigo_descuento">Código de descuento</label>
            <input type="text" name="codigo_descuento" id="codigo_descuento" value="{{ old('codigo_descuento', isset($post['codigo_descuento']) ? $post['codigo_descuento'] : '') }}" />
            @if(isset($mensajes['descuento']))
                <div class="error">{{ $mensajes['descuento'] }}</div>
            @endif
        </div>

    </section>
    <hr>

    @for ($i = 1; $i <= $participantesxequipo; $i++)
        @php
            $required = $i < 4 ? 'required="required"' : '';
        @endphp
        <section class="datos">
            <h2>Datos del participante {{ $i }} <span class="etapa_participante">{{ ($participantesxequipo > 1 && isset($etapas[$i-1])) ? '('.$etapas[$i-1].')' : '' }}</span></h2>
            @if ($evento['tipo_nombre'] == 'libre')
                <label for="nombre{{ $i }}">
                    <span>Nombre/s y apellido/s *</span>
                    <input type="text" name="nombre{{ $i }}" placeholder="" value="{{ $post['nombre'.$i] }}" {{ $required }} />
                </label>
            @else
                <label for="nombre{{ $i }}">
                    <span>Nombre/s *</span>
                    <input type="text" name="nombre{{ $i }}" placeholder="" value="{{ $post['nombre'.$i] }}" {{ $required }} />
                </label>
                <label for="apellido{{ $i }}">
                    <span>Apellido/s *</span>
                    <input type="text" name="apellido{{ $i }}" placeholder="" value="{{ $post['apellido'.$i] }}" {{ $required }} />
                </label>
            @endif

            <label for="mail{{ $i }}">
                <span>Correo electrónico *</span>
                <input type="email" name="mail{{ $i }}" placeholder="" value="{{ $post['mail'.$i] }}" {{ $i == 1 ? $required : '' }} />
            </label>

            @if ($evento['nombre_localidad'])
            <label for="localidad{{ $i }}">
                <span>{{ $evento['nombre_localidad'] }}</span>
                <input type="text" name="localidad{{ $i }}" placeholder="" value="{{ $post['localidad'.$i] }}" />
            </label>
            @endif

            @foreach ($datosxeventos as $datoxevento)
                @php
                    $iddato = $datoxevento['iddato'].$i;
                    $post[$iddato] = $post[$iddato] ?? '';
                    $required = ($i < 4 && $datoxevento['obligatorio']) ? 'required="required" ' : '';
                @endphp
                <label for="{{ $iddato }}">
                    <span>{{ $datoxevento['nombre'] }} {{ $datoxevento['obligatorio'] ? '*' : '' }}
                        <i>{{ $datoxevento['iddato'] == $evento['iddato_unico'] ? '(Dato único entre todos los participantes)' : '' }}</i>
                    </span>

                    @switch($datoxevento['tipo'])
                        @case('text')
                        @case('number')
                            <input type="{{ $datoxevento['tipo'] }}" name="{{ $iddato }}" placeholder="{{ $datoxevento['observacion'] ? $datoxevento['observacion'] : $datoxevento['datos_observacion'] }}" value="{{ $post[$iddato] }}" {{ $required }} />
                            @break

                        @case('date')
                            @if ($datoxevento['observacion'] || $datoxevento['datos_observacion'])
                                <br><span class="label_observacion">{{ $datoxevento['observacion'] ? $datoxevento['observacion'] : $datoxevento['datos_observacion'] }}</span><br>
                            @endif
                            <input type="{{ $datoxevento['tipo'] }}" name="{{ $iddato }}" value="{{ $post[$iddato] }}" {{ $required }} />
                            @break

                        @case('pregunta')
                            <br />
                            <input type="radio" name="{{ $iddato }}" value="SI" {{ $required }} {{ $post[$iddato] == 'SI' ? 'checked="checked"' : '' }} /><label for="SI">SI</label>&nbsp;&nbsp;&nbsp;
                            <input type="radio" name="{{ $iddato }}" value="NO" {{ $post[$iddato] == 'NO' ? 'checked="checked"' : '' }} /><label for="NO">NO</label><br />
                            <input type="text" name="{{ $iddato }}_texto" placeholder="{{ $datoxevento['observacion'] ? $datoxevento['observacion'] : $datoxevento['datos_observacion'] }}" value="{{ $post[$iddato.'_texto'] }}" />
                            <br>
                            @break

                        @case('select')
                            <br />
                            <select name="{{ $iddato }}" {{ $required }}>
                                <option value="">Seleccione una opción</option>
                                @php
                                    $opciones = explode('|', ($datoxevento['observacion'] ? $datoxevento['observacion'] : $datoxevento['datos_observacion']));
                                @endphp
                                @foreach ($opciones as $opcion)
                                    <option value="{{ $opcion }}" {{ $post[$iddato] == $opcion ? 'selected="selected"' : '' }}>{{ $opcion }}</option>
                                @endforeach
                            </select>
                            @break

                        @case('acepta')
                            <br />
                            <input type="radio" name="{{ $iddato }}" value="SI" {!! $required !!} {{ $post[$iddato] == 'SI' ? 'checked="checked"' : '' }} /><label for="SI">SI</label>&nbsp;&nbsp;&nbsp;
                            <p class="reglamento">{{ $datoxevento['observacion'] }}</p>
                            <br>
                            @break

                        @case('file')
                            @if ($datoxevento['observacion'] || $datoxevento['datos_observacion'])
                                <br><span class="label_observacion">{{ $datoxevento['observacion'] ? $datoxevento['observacion'] : $datoxevento['datos_observacion'] }}</span><br>
                            @endif
                            <input type="{{ $datoxevento['tipo'] }}" name="{{ $iddato }}" value="{{ $post[$iddato] }}" {{ $required }} accept="image/jpeg, image/png, image/x-png, application/pdf, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/msword, application/vnd.ms-excel"/>
                            @break

                        @default
                            Tipo de datoxevento incorrecto: {{ $datoxevento['tipo'] }}
                            @break
                    @endswitch
                </label>
            @endforeach

            <label for="observacion{{ $i }}">
                <span>Observación</span>
                <textarea name="observacion{{ $i }}" placeholder="Puede escribir cualquier observación que crea necesaria...">{{ $post['observacion'.$i] }}</textarea>
            </label>
        </section>
        <hr>
    @endfor

    <section id="botones">
        <input type="submit" name="boton" value="Inscribirme" id="inscribirme" onclick="return validar()" />
        <input type="submit" name="boton" value="Inscribiendo ..." id="inscribiendo" disabled="disabled" style="display: none" />
        <br style="clear:both;">
    </section>
</form>

@endsection