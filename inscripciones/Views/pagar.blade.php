@extends('layout')

@section('content')

<div class="form">
    <section class="datos">

        @if ($precio['plataforma'] == 'mercadopago' && isset($pago->id) && $pago->id)

            <div id="wallet_container"></div>

            <script src="https://sdk.mercadopago.com/js/v2"></script>

            <script>
                const mp = new MercadoPago("{{ $precio['key'] }}");
                const bricksBuilder = mp.bricks();

                mp.bricks().create("wallet", "wallet_container", {
                    initialization: {
                        preferenceId: "{{ $pago->id }}"
                    }
                });
            </script>

        @elseif ($precio['plataforma'] == 'paypal')

        @endif

        @if(isset($mensaje_descuento) && $mensaje_descuento)
            <div class="error">{{ $mensaje_descuento }}</div>
        @endif

        @if(isset($precio['precio_original']) && isset($precio['descuento']))
            <div class="precio">
                <div>Precio original: <b>${{ number_format($precio['precio_original'], 2, ',', '.') }}</b></div>
                <div>Descuento: <b>{{ $precio['descuento'] }}%</b></div>
                <div>Monto descontado: <b>${{ number_format($precio['precio_original'] - $precio['precio'], 2, ',', '.') }}</b></div>
                <div>Precio final: <b>${{ number_format($precio['precio'], 2, ',', '.') }}</b></div>
            </div>
        @else
            <div class="precio">
                <div>Precio: <b>${{ number_format($precio['precio'], 2, ',', '.') }}</b></div>
            </div>
        @endif

    </section>
</div>

@endsection