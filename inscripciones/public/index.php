<?php

require __DIR__.'/../../cronometrajeinstantaneo.env';
require __DIR__.'/../../funciones.php';
require __DIR__.'/../../vendor/autoload.php';

use Inscripciones\Route;
use Inscripciones\Evento;
use Inscripciones\Inscripcion;
use Inscripciones\Pago;

use eftec\bladeone\BladeOne;

if (ESTADO == 'produccion')
    \Sentry\init([
        'dsn' => SENTRY_DNS,
        'traces_sample_rate' => SENTRY_TRACES_SAMPLE_RATE,
        'profiles_sample_rate' => SENTRY_PROFILES_SAMPLE_RATE,
    ]);


$route = new Route();
if (!$route->codigo())
    $route->errorUrl('Enlace incorrecto');

$evento = new Evento($route);
if (is_null($evento->idevento))
    $route->errorUrl('No existe el evento');

if ($route->method() == 'POST') {
    $route->sanitizePost($evento);
}


$blade = new BladeOne(
    __DIR__ . '/../Views',
    __DIR__ . '/../cache',
    ESTADO == 'desarrollo' ? BladeOne::MODE_DEBUG : BladeOne::MODE_AUTO);

// Inscripciones cerradas
if ($evento->inscripcionesCerradas()) {
    echo $blade->run("cerradas", $evento->blade([
        'razon' => $evento->inscripcionesCerradas(),
    ]));

// Nueva inscripción
} else if ($route->method() == 'GET' && !$route->idinscripcion()) {

    echo $blade->run(
        $route->participantesxequipo() == 1 ? 'individual' : 'equipo',
        $evento->blade([
            'categorias' => $evento->categorias(),
            'categoria_sexo' => $evento->categoria_sexo(),
            'carreras' => $evento->carreras(),
            'datosxeventos' => $evento->datosxevento(),
            'post' => [],
            'iddato_unico' => false,
            'participantesxequipo' => $route->participantesxequipo(),
            'etapas' => $evento->etapas(),
        ]));

// Recibo nueva inscripción por post
} else if ($route->method() == 'POST' && !$route->idinscripcion()) {

    $inscripcion = new Inscripcion($evento, $route);
    if ($inscripcion->save()) {

        cache($route->codigo());
        $inscripcion->notificar('preinscripto');
        $route->idinscripcion($inscripcion->idinscripcion());
        $route->redirect('preinscripto');

    } else {
        echo $blade->run(
            $route->participantesxequipo() == 1 ? 'individual' : 'equipo',
            $evento->blade([
                'categorias' => $evento->categorias(),
                'categoria_sexo' => $evento->categoria_sexo(),
                'carreras' => $evento->carreras(),
                'datosxeventos' => $evento->datosxevento(),
                'post' => $route->post(),
                'iddato_unico' => false,
                'participantesxequipo' => $route->participantesxequipo(),
                'etapas' => $evento->etapas(),

                'mensajes' => $inscripcion->mensajes(),
            ]));
    }

// Ver inscripción por GET
} else if ($route->method() == 'GET'
    && $route->accion() == 'preinscripto'
    && $route->idinscripcion()) {

        $inscripcion = new Inscripcion($evento, $route);

        if (!$inscripcion->get())
            $route->errorUrl('El enlace es incorrecto o la inscripción ya no existe');

        if (in_array($inscripcion->estado, ['inscripto', 'acreditado']))
            $route->redirect('inscripto');

        // Sino cargar los precios
        $precios = $inscripcion->precios();

        echo $blade->run($route->accion(), $evento->blade([
            'inscripciones_preinscripto' => $inscripcion->prepararTextos($evento->evento()['inscripciones_preinscripto']),
            'precios' => $precios ?? [],
        ]));

// Pagar inscripción por GET
} else if ($route->method() == 'GET'
    && $route->accion() == 'pagar'
    && $route->idinscripcion()) {

    $inscripcion = new Inscripcion($evento, $route);
    $inscripcion->get();
    $precios = $inscripcion->precios();

    if (!$inscripcion->validarPrecio($route->parametro('idprecio')))
        $route->errorUrl('El precio no está disponible en este momento');

    $precio = $precios[$route->parametro('idprecio')];

    $pago = new Pago($precio);
    $pago->setParametros([
        'idevento' => $evento->idevento,
        'idinscripcion' => $route->idinscripcion(),
        'iddescuento' => isset($inscripcion->inscripcion()['iddescuento']) ? $inscripcion->inscripcion()['iddescuento'] : null,
        'inscripto' => $route->generateUri($route->idinscripcion(), 'inscripto').'?idprecio='.$route->parametro('idprecio'),
        'rechazado' => $route->generateUri($route->idinscripcion(), 'rechazado').'?idprecio='.$route->parametro('idprecio'),
    ]);
    if ($precio['fecha_hasta'] && $precio['fecha_hasta'] > date('Y-m-d H:i:s')) {
        $pago->setParametros([
            'fecha_hasta' => $precio['fecha_hasta'],
        ]);
    }
    $pago->get();

    echo $blade->run($route->accion(), $evento->blade([
        'precio' => $precio,
        'pago' => $pago->pago(),
        'mensaje_descuento' => $pago->mensajeDescuento(),
    ]));

// Ver y procesar inscripto y rechazado
} else if ($route->method() == 'GET'
    && ($route->accion() == 'rechazado' || $route->accion() == 'pendiente')
    && $route->idinscripcion()) {

    $inscripcion = new Inscripcion($evento, $route);

    if (!$inscripcion->get())
        $route->errorUrl('El enlace es incorrecto o la inscripción ya no existe');

    // TODO: Guardar los datos del pago rechazado por MP

    echo $blade->run($route->accion(), $evento->blade([
        'inscripciones_rechazado' => $inscripcion->prepararTextos($evento->evento()['inscripciones_rechazado']),
    ]));

// Recibo un pago aprobado para pasarlo a inscripto
} else if ($route->method() == 'GET'
    && $route->accion() == 'inscripto'
    && $route->idinscripcion()) {

    $inscripcion = new Inscripcion($evento, $route);

    if (!$inscripcion->get())
        $route->errorUrl('El enlace es incorrecto o la inscripción ya no existe');

    if ($inscripcion->estado == 'preinscripto') {

        $precios = $inscripcion->precios();

        if (!$inscripcion->validarPrecio($route->parametro('idprecio')))
            $route->errorUrl('El precio no está disponible en este momento');

        $precio = $precios[$route->parametro('idprecio')];
        $pago = new Pago($precio);
        $pago->setParametros([
            'idevento' => $evento->idevento,
            'idinscripcion' => $route->idinscripcion(),
        ]);
        $recibido = $pago->recibir();

        if (!$recibido)
            $route->redirect('rechazado');

        $pago->save();

        $update = [
            'estado' => 'inscripto',
            'estado_pago' => $precio['cuota'] < 100 ? 'parcial' : 'aprobado',
            'fechapago' => 'NOW()',
        ];

        if ($evento->auto_numeracion && !$inscripcion->idparticipante)
            $update['idparticipante'] = $inscripcion->siguienteIdparticipante();

        // Actualizo la inscripcion
        $inscripcion->update($update);

        // Mandar mails si corresponde
        if ($evento->auto_mail && $evento->mail_inscripto)
            $inscripcion->notificar('inscripto');

    }
    // TODO: Si ya estaba aprobado el pago, mostrar hemos registrado su pago el día ...

    echo $blade->run($route->accion(), $evento->blade([
        'inscripciones_'.$route->accion() => $inscripcion->prepararTextos($evento->evento()['inscripciones_'.$route->accion()]),
    ]));

} else {
    $route->errorUrl('Enlace incorrecto');
}
