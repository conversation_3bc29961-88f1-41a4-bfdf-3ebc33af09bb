<?php

namespace Inscripciones;

use PDO;

use Inscripciones\MercadoPago;

class Pago
{
    private $db;
    private $precio;
    private $pago;
    private $parametros = [];
    private $respuesta = [];
    private $iddescuento = null;
    private $porcentaje_descuento = null;
    private $mensaje_descuento = null;

    public function __construct($precio)
    {
        $this->precio = $precio;
        $this->db = Db::db();
    }

    public function setParametros($parametros)
    {
        foreach ($parametros as $key => $value) {
            $this->parametros[$key] = $value;
        }
        // Si viene iddescuento, buscar y validar
        if (isset($parametros['iddescuento']) && $parametros['iddescuento']) {
            $stmt = $this->db->prepare("SELECT * FROM descuentos WHERE iddescuento = :iddescuento LIMIT 1");
            $stmt->bindParam(':iddescuento', $parametros['iddescuento'], PDO::PARAM_INT);
            $stmt->execute();
            $descuento = $stmt->fetch(PDO::FETCH_ASSOC);
            $ahora = date('Y-m-d H:i:s');
            if ($descuento &&
                (!$descuento['valido_desde'] || $descuento['valido_desde'] <= $ahora) &&
                (!$descuento['valido_hasta'] || $descuento['valido_hasta'] >= $ahora) &&
                ($descuento['uso_maximo'] == 0 || $descuento['usos'] < $descuento['uso_maximo'])
            ) {
                $this->iddescuento = $descuento['iddescuento'];
                $this->porcentaje_descuento = $descuento['descuento'];
            } else {
                $this->mensaje_descuento = 'El código de descuento ya no es válido.';
            }
        }
    }

    public function log($logs)
    {
        $linea = date("Y-m-d H:i:s").';';
        foreach ($logs as $log) {
            $linea .= $log.';';
        }
        $linea .= "\n";
        file_put_contents(PATH_LOGS.'/'.date("Y-m-d").'_pagos.csv', $linea, FILE_APPEND);

    }

    public function get()
    {
        // Aplicar descuento si corresponde
        if ($this->iddescuento && $this->porcentaje_descuento) {
            $this->precio['precio_original'] = $this->precio['precio'];
            $this->precio['descuento'] = $this->porcentaje_descuento;
            $this->precio['precio'] = round($this->precio['precio'] * (1 - $this->porcentaje_descuento / 100), 2);
        }
        switch ($this->precio['plataforma']) {

            case 'mercadopago':
                $pago = new MercadoPago($this->precio);
                $pago->authenticate();

                $parametros = [
                    "items" => [[
                        "title" => "Inscripción",
                        "quantity" => 1,
                        "unit_price" => (float) $this->precio['precio'],
                    ]],
                    "back_urls" => [
                        'success' => $this->parametros['inscripto'],
                        'failure' => $this->parametros['rechazado'],
                    ],
                    "external_reference" => $this->parametros['idinscripcion'],
                    "auto_return" => 'approved',
                ];
                if (isset($this->parametros['fecha_hasta'])) {
                    $parametros['fecha_hasta'] = $this->parametros['fecha_hasta'];
                    // TODO: Agregar zona horaria según país
                    $parametros['expiration_date_to'] = date('Y-m-d\TH:i:s.000-03:00', strtotime($this->parametros['fecha_hasta']));
                    $parametros['expires'] = true;
                }
                $this->pago = $pago->createPreferenceRequest($parametros);
                $this->log([
                    $this->parametros['idevento'],
                    $this->parametros['idinscripcion'],
                    'preference',
                    json_encode($parametros),
                    json_encode($this->pago),
                ]);
                break;

            case 'transferencia':
            case 'efectivo':
            default:
                $this->pago = [];
                break;

        }
    }

    public function recibir()
    {
        switch ($this->precio['plataforma']) {
            case 'mercadopago':
                $pago = new MercadoPago($this->precio);
                $this->respuesta = $pago->recibir();
                $this->log([
                    $this->parametros['idevento'],
                    $this->parametros['idinscripcion'],
                    'recibir',
                    $this->respuesta['json'],
                ]);
                return $this->respuesta;
                break;

            default:
                return false;
                break;
        }

        // Ver si agrego validaciones
        return true;
    }

    public function save()
    {
        $stmt = $this->db->prepare(
            "INSERT INTO pagos SET
                idevento = :idevento,
                idinscripcion = :idinscripcion,
                idprecio = :idprecio,
                estado = :estado,
                fecha = :fecha,
                referencia = :referencia,
                json = :json,
                observaciones = :observaciones" .
            ($this->iddescuento ? ", iddescuento = :iddescuento" : "")
        );
        $stmt->bindParam(':idevento', $this->parametros['idevento'], PDO::PARAM_INT);
        $stmt->bindParam(':idinscripcion', $this->parametros['idinscripcion'], PDO::PARAM_INT);
        $stmt->bindParam(':idprecio', $this->precio['idprecio'], PDO::PARAM_INT);
        $stmt->bindParam(':estado', $this->respuesta['estado'], PDO::PARAM_STR);
        $stmt->bindParam(':fecha', $this->respuesta['fecha'], PDO::PARAM_STR);
        $stmt->bindParam(':referencia', $this->respuesta['referencia'], PDO::PARAM_STR);
        $stmt->bindParam(':json', $this->respuesta['json'], PDO::PARAM_STR);
        $stmt->bindParam(':observaciones', $this->respuesta['observaciones'], PDO::PARAM_STR);
        if ($this->iddescuento) {
            $stmt->bindParam(':iddescuento', $this->iddescuento, PDO::PARAM_INT);
        }
        $stmt->execute();
        // Si el pago fue aprobado y se aplicó descuento, incrementar usos
        if ($this->iddescuento && $this->respuesta['estado'] === 'aprobado') {
            $this->db->query("UPDATE descuentos SET usos = usos + 1 WHERE iddescuento = " . intval($this->iddescuento));
        }
    }

    public function pago()
    {
        return $this->pago;
    }

    public function mensajeDescuento()
    {
        return $this->mensaje_descuento;
    }

}
