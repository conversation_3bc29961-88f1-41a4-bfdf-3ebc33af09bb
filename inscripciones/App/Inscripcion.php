<?php

namespace Inscripciones;

use PDO;

use Inscripciones\Evento;
use Inscripciones\Route;

class Inscripcion
{
    private $db;
    private int $idinscripcion;
    private Route $route;
    private Evento $evento;
    private array $inscripcion;
    private array $post;
    private array $mensajes = [];
    private array $precios;

    public function __construct(Evento $evento, Route $route)
    {
        $this->route = $route;
        $this->idinscripcion = $route->idinscripcion();
        $this->post = $route->post();
        $this->evento = $evento;

        $this->db = Db::db();
    }

    public function validar()
    {
        for ($i = 1; $i <= $this->route->participantesxequipo(); $i++) {

            $iddato_unico = $this->route->participantesxequipo() == 1
                ? $this->evento->iddato_unico
                : $this->evento->iddato_unico.$i;

            if (!$this->post['nombre']) {
                $this->mensajes[] = [
                    'tipo' => 'error',
                    'texto' =>'El nombre es obligatorio',
                ];
            }

            // Si es un equipo, validar que no exista un equipo con el mismo nombre
            if ($this->route->participantesxequipo() > 1
                && $this->db->query(
                    "SELECT *
                    FROM participantes
                    WHERE idevento = '{$this->evento->idevento}'
                        AND nombre = '" . htmlspecialchars($this->post['nombre'], ENT_QUOTES) . "'
                        AND estado NOT IN ('anulado', 'eliminado')")
                    ->rowCount()) {

                $this->mensajes[] = [
                    'tipo' => 'error',
                    'texto' =>'Ya existe un equipo con el mismo nombre',
                ];
            }

            if ($this->evento->multi_categoria) {
                $idcategorias = filtrar_input_array_coma($this->post['idcategorias']);
                if (!$idcategorias) {
                    $this->mensajes[] = [
                        'tipo' => 'error',
                        'texto' =>'Debe seleccionar por lo menos una categoría',
                    ];
                }

            } else if (!$this->post['idcategoria']) {
                $this->mensajes[] = [
                    'tipo' => 'error',
                    'texto' =>'La categoría es obligatoria',
                ];
            }

            if ($this->evento->iddato_unico
                && $this->post[$iddato_unico]
                && $this->queryDatoUnico($this->post[$iddato_unico])) {

                $this->mensajes[] = [
                    'tipo' => 'error',
                    'texto' =>'Ya existe un participante con el mismo '.$this->evento->datosxevento()[$this->evento->iddato_unico]['nombre'].': '.$this->post[$iddato_unico]
                ];
            }

            // Validar que los datos obligatorios estén completos
            foreach ($this->evento->datosxevento() as $datoxevento) {
                $iddato = $this->route->participantesxequipo() == 1
                    ? $datoxevento['iddato']
                    : $datoxevento['iddato'].$i;

                if ($datoxevento['obligatorio'] == 1
                    && $datoxevento['tipo'] != 'file'
                    && (!isset($this->post[$iddato])
                        || $this->post[$iddato] == '')) {

                    $this->mensajes[] = [
                        'tipo' => 'error',
                        'texto' => 'El campo '.$datoxevento['nombre'].' es obligatorio',
                    ];

                } else if ($datoxevento['obligatorio'] == 1
                    && $datoxevento['tipo'] == 'file'
                    && (!isset($_FILES[$iddato])
                        || $_FILES[$iddato]['error'] != UPLOAD_ERR_OK
                        || $_FILES[$iddato]['size'] == 0
                        || $_FILES[$iddato]['size'] > 20971520 // Tamaño máximo 20MB
                        || !is_readable($_FILES[$iddato]['tmp_name']))) {

                    $this->mensajes[] = [
                        'tipo' => 'error',
                        'texto' => 'El archivo subido como '.$datoxevento['nombre'].' es superior a 2MB o no es válido',
                    ];
                }
            }
        }

        if (count($this->mensajes))
            return false;

        return true;
    }

    public function save()
    {
        if (!$this->validar()) {
            return false;
        }

        // Validar descuento
        $iddescuento = null;
        if (!empty($this->post['codigo_descuento'])) {
            list($iddescuento, $porcentaje_descuento, $error_descuento) = $this->validarDescuento($this->post['codigo_descuento']);
            if ($error_descuento) {
                $this->mensajes['descuento'] = $error_descuento;
                return false;
            }
        }

        if ($this->route->participantesxequipo() == 1)
            $this->saveParticipante();
        else
            $this->saveEquipo();

        foreach($this->post as $key => $value) {
            $this->{$key} = $value;
        }

        return true;
    }

    private function saveParticipante()
    {
        $this->post['idparticipante'] = in_array($this->evento->estado_predeterminado, ['inscripto', 'acreditado'])
            ? $this->siguienteIdparticipante()
            : 0;
        $this->post['nombre'] = $this->tipoNombre();

        if ($this->evento->multi_categoria) {
            $idcategorias = explode(',', $this->post['idcategorias']);
            $this->post['idcategoria'] = $idcategorias[0] ?? 0;

        } else {
            $idcategorias = [];
        }

        $this->db->beginTransaction();

        $this->db->query(
            "INSERT INTO participantes SET
                idevento = '{$this->evento->idevento}',
                nombre = '" . htmlspecialchars($this->post['nombre'], ENT_QUOTES) . "',
                ".($this->evento->tipo_nombre == 'Nombre y Apellido separados'
                    ? "apellido = '" . htmlspecialchars($this->post['apellido'], ENT_QUOTES) . "'," : "")."
                mail = '{$this->post['mail']}',
                estado = '{$this->evento->estado_predeterminado}',
                idparticipante = '{$this->post['idparticipante']}',
                idcategoria = '{$this->post['idcategoria']}',
                localidad = '" . htmlspecialchars($this->post['localidad'], ENT_QUOTES) . "',
                observacion = '" . htmlspecialchars($this->post['observacion'], ENT_QUOTES) . "'"
        );
        $this->idinscripcion = $this->db->lastInsertId();

        foreach ($idcategorias as $idcategoria) {
            $this->db->query(
                "INSERT INTO categoriasxparticipantes SET
                    idcategoria = '{$idcategoria}',
                    idinscripcion = '{$this->idinscripcion}'"
            );
        }

        $this->saveDatosxParticipantes($this->idinscripcion);

        $this->db->commit();
    }

    public function saveEquipo()
    {
        $this->post['idparticipante'] = in_array($this->evento->estado_predeterminado, ['inscripto', 'acreditado'])
            ? $this->siguienteIdparticipante()
            : 0;

        if ($this->evento->multi_categoria) {
            $idcategorias = explode(',', $this->post['idcategorias']);
            $this->post['idcategoria'] = $idcategorias[0] ?? 0;

        } else {
            $idcategorias = [];
        }

        // Validar descuento
        $iddescuento = null;
        if (!empty($this->post['codigo_descuento'])) {
            list($iddescuento, $porcentaje_descuento, $error_descuento) = $this->validarDescuento($this->post['codigo_descuento']);
            if ($error_descuento) {
                $this->mensajes['descuento'] = $error_descuento;
                return false;
            }
        }

        $this->db->beginTransaction();

        $this->db->query(
            "INSERT INTO participantes SET
                idevento = '".$this->evento->idevento."',
                nombre = '".htmlspecialchars($this->post['nombre'], ENT_QUOTES)."',
                estado = '{$this->evento->estado_predeterminado}',
                idparticipante = '{$this->post['idparticipante']}',
                idcategoria = '".$this->post['idcategoria']."',
                equipo = '".$this->route->equipo()."'"
        );
        $this->idinscripcion = $this->db->lastInsertId();

        foreach ($idcategorias as $idcategoria) {
            $this->db->query(
                "INSERT INTO categoriasxparticipantes SET
                    idcategoria = '{$idcategoria}',
                    idinscripcion = '{$this->idinscripcion}'"
            );
        }

        for ($i = 1; $i <= $this->route->participantesxequipo(); $i++) {

            $this->post['nombre'.$i] = $this->tipoNombre($i);

            $this->db->query(
                "INSERT INTO participantes SET
                    idevento = '".$this->evento->idevento."',
                    idequipo = '".$this->idinscripcion."',
                    nombre = '".htmlspecialchars($this->post['nombre'.$i], ENT_QUOTES)."',
                    ".($this->evento->tipo_nombre == 'Nombre y Apellido separados'
                        ? "apellido = '" . htmlspecialchars($this->post['apellido'.$i], ENT_QUOTES) . "'," : "")."
                    mail = '".$this->post['mail'.$i]."',
                    localidad = '".htmlspecialchars($this->post['localidad'.$i], ENT_QUOTES)."',
                    equipo = 'participante',
                    observacion = '".htmlspecialchars($this->post['observacion'.$i], ENT_QUOTES)."'"
                );
            $idinscripcion = $this->db->lastInsertId();

            $this->saveDatosxParticipantes($idinscripcion, $i);

        }

        $this->db->commit();
    }

    public function get()
    {
        if (!$this->idinscripcion)
            return false;

        if ($this->route->participantesxequipo() == 1)
            return $this->getParticipante();
        else
            return $this->getEquipo();
    }

    private function getParticipante()
    {
        $stmt = $this->db->prepare(
            "SELECT participantes.*,
                (SELECT idcarrera FROM categorias WHERE idcategoria = participantes.idcategoria) AS idcarrera
            FROM participantes
            WHERE idinscripcion = :idinscripcion
                AND idevento = :idevento
                AND estado NOT IN ('anulado', 'eliminado')
                AND equipo = ''");
        $stmt->bindParam(':idinscripcion', $this->idinscripcion, PDO::PARAM_INT);
        $stmt->bindParam(':idevento', $this->evento->idevento, PDO::PARAM_INT);
        $stmt->execute();

        $inscripcion = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$inscripcion)
            return false;
        foreach($inscripcion as $key => $value) {
            $this->{$key} = $value;
        }

        $datos = $this->getDatosxParticipantes($this->idinscripcion);
        foreach($datos as $key => $value) {
            $this->{$value['iddato']} = $value['dato'];
        }

        return true;

    }

    private function getEquipo()
    {
        $stmt = $this->db->prepare(
            "SELECT participantes.*,
                (SELECT idcarrera FROM categorias WHERE idcategoria = participantes.idcategoria) AS idcarrera
            FROM participantes
            WHERE idinscripcion = :idinscripcion
                AND idevento = :idevento
                AND estado NOT IN ('anulado', 'eliminado')
                AND equipo IN ('dupla', 'tria', 'tetra')");
        $stmt->bindParam(':idinscripcion', $this->idinscripcion, PDO::PARAM_INT);
        $stmt->bindParam(':idevento', $this->evento->idevento, PDO::PARAM_INT);
        $stmt->execute();

        $inscripcion = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$inscripcion)
            return false;
        foreach($inscripcion as $key => $value) {
            $this->{$key} = $value;
        }

        for ($i = 1; $i <= $this->route->participantesxequipo(); $i++) {

            $stmt = $this->db->prepare(
                "SELECT idinscripcion, nombre, mail, localidad, observacion
                FROM participantes
                WHERE idequipo = :idequipo
                    AND idevento = :idevento"
            );
            $stmt->bindParam(':idequipo', $this->idinscripcion, PDO::PARAM_INT);
            $stmt->bindParam(':idevento', $this->evento->idevento, PDO::PARAM_INT);
            $stmt->execute();

            $participante = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!$participante)
                return false;
            foreach($participante as $key => $value) {
                if ($key != 'idinscripcion')
                    $this->{$key.$i} = $value;
            }

            $datos = $this->getDatosxParticipantes($participante['idinscripcion']);
            foreach($datos as $key => $value) {
                $this->{$value['iddato'].$i} = $value['dato'];
            }
        }

        return true;
    }

    private function queryDatoUnico($dato)
    {
        return $this->db->query(
            "SELECT *
            FROM datosxparticipantes AS dxp
                LEFT JOIN participantes AS p ON p.idinscripcion = dxp.idinscripcion
            WHERE dxp.idevento = '{$this->evento->idevento}'
                AND dxp.iddato = '{$this->evento->iddato_unico}'
                AND dxp.dato = '{$dato}'
                AND p.estado NOT IN ('anulado', 'eliminado')")
            ->rowCount();
    }

    public function siguienteIdparticipante()
    {
        return $this->evento->auto_numeracion
            ? $this->evento->ultimo_idparticipante + 1
            : 0;
    }

    private function tipoNombre($i = '')
    {
        switch ($this->evento->tipo_nombre) {
            default:
            case 'libre':
            case 'Nombre y Apellido separados':
                return $this->post['nombre'.$i]; break;
            case 'Nombre Apellido': return ucfirst($this->post['nombre'.$i]).' '.ucfirst($this->post['apellido'.$i]); break;
            case 'Apellido, Nombre': return ucfirst($this->post['apellido'.$i]).', '.ucfirst($this->post['nombre'.$i]); break;
            case 'APELLIDO Nombre': return strtoupper($this->post['apellido'.$i]).' '.ucfirst($this->post['nombre'.$i]); break;
        }
    }

    private function saveDatosxParticipantes($idinscripcion, $i = '')
    {
        $iddato = $dato = '';

        $stmt = $this->db->prepare("INSERT INTO datosxparticipantes (iddato, idinscripcion, idevento, dato) VALUES (:iddato, :idinscripcion, :idevento, :dato)");
        $stmt->bindParam(':iddato', $iddato, PDO::PARAM_STR);
        $stmt->bindParam(':idinscripcion', $idinscripcion, PDO::PARAM_STR);
        $stmt->bindParam(':idevento', $this->evento->idevento, PDO::PARAM_STR);
        $stmt->bindParam(':dato', $dato, PDO::PARAM_STR);

        foreach ($this->evento->datosxevento() as $datoxevento) {
            $iddato = $datoxevento['iddato'];

            if ($datoxevento['tipo'] != 'file') {
                $dato = $this->post[$iddato.$i];
            }

            if ($datoxevento['tipo'] == 'pregunta'
                && isset($this->post[$iddato.$i.'_texto'])
                && $this->post[$iddato.$i.'_texto'] != '') {
                $dato.= ' - '.$this->post[$iddato.$i.'_texto'];
            }

            if ($datoxevento['tipo'] == 'file') {
                if (isset($_FILES[$iddato.$i])
                    && $_FILES[$iddato.$i]['error'] === UPLOAD_ERR_OK
                    && $_FILES[$iddato.$i]['size'] > 0
                    && is_readable($_FILES[$iddato.$i]['tmp_name'])) {

                    $archivo = $_FILES[$iddato.$i];
                    $nombre_archivo = $this->evento->idevento .'/'. $idinscripcion .'-'. generar_random(10) .'/'. $archivo['name'];

                    $storage = new \Google\Cloud\Storage\StorageClient([
                        'projectId' => GOOGLE_CLOUD_PROJECT_ID,
                        'keyFilePath' => GOOGLE_CLOUD_KEY_FILE
                    ]);

                    $bucket = $storage->bucket('cronometrajeinstantaneo-datos');
                    $bucket->upload(file_get_contents($archivo['tmp_name']), [
                        'name' => $nombre_archivo
                    ]);

                    $dato = $nombre_archivo;
                    $this->post[$iddato.$i.'_archivo'] = $nombre_archivo;

                } else {
                    $dato = '';
                }
            }
            $stmt->execute();
        }
    }

    private function getDatosxParticipantes($idinscripcion)
    {
        $stmt = $this->db->prepare(
            "SELECT *
            FROM datosxparticipantes
            WHERE idinscripcion = :idinscripcion
                AND idevento = :idevento"
        );
        $stmt->bindParam(':idinscripcion', $idinscripcion, PDO::PARAM_INT);
        $stmt->bindParam(':idevento', $this->evento->idevento, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    }

    public function precios()
    {
        if (isset($this->precios) && is_array($this->precios))
            return $this->precios;

        $stmt = $this->db->prepare(
            "SELECT p.idprecio, p.idevento, p.estado, p.idplataforma, p.idpais, precio, fecha_desde, fecha_hasta, cantidad, cuota, boton, url,
                pl.estado AS estado_plataforma, pl.titulo, pl.plataforma, pl.descripcion, pl.key, pl.secret,
                (SELECT COUNT(*) FROM pagos
                    WHERE pagos.idprecio = p.idprecio
                        AND pagos.estado IN ('aprobado', 'parcial')) AS cantidad_inscriptos
            FROM precios AS p
                JOIN preciosxcarreras AS pxc ON pxc.idprecio = p.idprecio
                JOIN plataformas AS pl ON pl.idplataforma = p.idplataforma
            WHERE p.idevento = :idevento
                AND pxc.idcarrera = :idcarrera
            ORDER BY fecha_desde, precio"
        );
        $stmt->bindParam(':idevento', $this->evento->idevento, PDO::PARAM_INT);
        $stmt->bindParam(':idcarrera', $this->idcarrera, PDO::PARAM_INT);
        $stmt->execute();

        $precios_temp = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $precios = [];
        foreach ($precios_temp as $precio) {

            // PARCHE ACUATLON
            if ($this->evento->idevento == 2193) {
                $stmt2 = $this->db->prepare(
                    "SELECT idcarrera FROM categorias WHERE idcategoria IN
                        (SELECT idcategoria FROM categoriasxparticipantes
                        WHERE idinscripcion = :idinscripcion)"
                );
                $stmt2->bindParam(':idinscripcion', $this->idinscripcion, PDO::PARAM_INT);
                $stmt2->execute();
                $idcarreras = $stmt2->fetchAll(PDO::FETCH_ASSOC);
                $idcarreras = array_column($idcarreras, 'idcarrera');
                if (!in_array($idcarreras[0], [11102, 11107])) {
                    if (count($idcarreras) == 2) {
                        $precio['precio'] = $precio['plataforma'] == 'efectivo' ? 85000 : 95000;
                    } else if (count($idcarreras) == 3) {
                        $precio['precio'] = $precio['plataforma'] == 'efectivo' ? 95000 : 105000;
                    }
                }
            }
            // PARCHE ACUATLON

            $key = $precio['idprecio'];
            $precios[$key] = $precio;
            $precios[$key]['habilitado'] = $precios[$key]['estado'] && $precios[$key]['estado_plataforma'];

            $fecha_inicio_utc = strtotime($precio['fecha_desde']) - $this->evento->timezone * 3600;
            $fecha_fin_utc = strtotime($precio['fecha_hasta']) - $this->evento->timezone * 3600;

            if (($precio['fecha_desde'] && $fecha_inicio_utc > time())
                || ($precio['fecha_hasta'] && $fecha_fin_utc < time()))
                $precios[$key]['habilitado'] = false;

            if ($precio['cantidad'] && $precio['cantidad'] <= $precio['cantidad_inscriptos'])
                $precios[$key]['habilitado'] = false;

            $precios[$key]['precio_formateado'] = number_format($precio['precio'], 2, ',', '.');
            if (!$precios[$key]['url'])
                $precios[$key]['url'] = $this->route->generateUri($this->idinscripcion, 'pagar').'?idprecio='.$precio['idprecio'];
        }

        $this->precios = $precios;
        return $this->precios;
    }

    public function validarPrecio($idprecio)
    {
        return key_exists($idprecio, $this->precios)
            && isset($this->precios[$idprecio]['habilitado'])
            && $this->precios[$idprecio]['habilitado'];
    }

    public function update($datos)
    {
        $this->db->beginTransaction();

        $update = [];
        foreach ($datos as $key => $value) {
            if ($key != 'fechapago') {
                $update[] = $key.' = :'.$key;
            } else {
                $update[] = $key.' = NOW()';
            }
        }
        $stmt = $this->db->prepare(
            "UPDATE participantes
            SET ".implode(' , ', $update)."
            WHERE idinscripcion = :idinscripcion
                AND idevento = :idevento");
        foreach ($datos as $key => $value) {
            if ($key != 'fechapago') {
                $stmt->bindValue(':'.$key, $value, PDO::PARAM_STR);
            }
        }
        $stmt->bindParam(':idinscripcion', $this->idinscripcion, PDO::PARAM_INT);
        $stmt->bindParam(':idevento', $this->evento->idevento, PDO::PARAM_INT);
        $stmt->execute();

        // Si pertenece a un equipo, también actualizar el estado
        if ($this->route->participantesxequipo() > 1) {
            $stmt = $this->db->prepare(
                "UPDATE participantes
                SET estado = :estado
                WHERE idequipo = :idequipo
                    AND idevento = :idevento");
            $stmt->bindParam(':estado', $datos['estado'], PDO::PARAM_STR);
            $stmt->bindParam(':idequipo', $this->idinscripcion, PDO::PARAM_INT);
            $stmt->bindParam(':idevento', $this->evento->idevento, PDO::PARAM_INT);
            $stmt->execute();
        }

        $this->db->commit();

        foreach ($datos as $key => $value) {
            $this->{$key} = $value;
        }

    }

    public function notificar($accion)
    {
        $asunto = $accion == 'inscripto'
            ? 'Inscripción Confirmada en '.$this->evento->nombre
            : 'Pre-inscripción en '.$this->evento->nombre;

        $mail_organizador = $this->mailOrganizador($accion);
        enviar_mail(
            [$this->evento->mail => $this->evento->nombre],
            [MAIL_NO_REPLY => EMPRESA],
            $asunto,
            $mail_organizador
        );

        $destinatarios = $this->destinatariosParticipantes();
        if (count($destinatarios)
            && $this->evento->auto_mail
            && $this->evento->mail_preinscripto) {

            enviar_mail(
                $destinatarios,
                [$this->evento->mail => $this->evento->nombre],
                $asunto,
                $this->prepararTextos($accion == 'inscripto'
                    ? $this->evento->mail_inscripto
                    : $this->evento->mail_preinscripto)
            );
        }

    }

    public function mailOrganizador($accion)
    {
        $equipo = $this->route->participantesxequipo() == 1 ? 'participante' : 'equipo';
        $idcategoria = $accion == 'inscripto'
            ? $this->idcategoria
            : $this->post['idcategoria'];
        $categoria = (is_array($this->evento->categorias())
            && isset($this->evento->categorias()[$idcategoria])
            && isset($this->evento->categorias()[$idcategoria]['nombre']))
            ? $this->evento->categorias()[$idcategoria]['nombre']
            : '';

        if ($accion == 'inscripto') {

            $mail_organizador = '<p align="justify"><b>Inscripción Confirmada en cronometrajeinstantaneo.com con los siguientes datos</b>:<br /><br />
                <a href="'.URL_ADMIN.$equipo.'?id='.$this->idinscripcion.'">Ver inscripción</a><br /><br />
                Nombre '.$equipo.': '.$this->nombre.'<br />
                Categoria: '.$categoria.'<br />
                Correo electrónico: '.$this->mail.'<br />
            </p>';

        } else if ($accion == 'preinscripto' && $equipo == 'participante') {

            $mail_organizador = '<p align="justify"><b>Pre-inscripción individual en cronometrajeinstantaneo.com con los siguientes datos</b>:<br /><br />
                <a href="'.URL_ADMIN.$equipo.'?id='.$this->idinscripcion.'">Ver inscripción</a><br /><br />
                Nombre participante: '.$this->post['nombre'].'<br />
                Categoria: '.$categoria.'<br />
                Correo electrónico: '.$this->post['mail'].'<br />
                '.$this->evento->nombre_localidad.': '.$this->post['localidad'].'<br />';
            foreach ($this->evento->datosxevento() as $datoxevento) {
                switch ($datoxevento['tipo']) {
                    case 'file':
                        if (isset($this->post[$datoxevento['iddato'].'_archivo'])) {
                            $mail_organizador.= $datoxevento['nombre'].': '
                                .'<a href="https://storage.googleapis.com/cronometrajeinstantaneo-datos/'
                                .$this->post[$datoxevento['iddato'].'_archivo'].'">Ver archivo</a>';
                        }
                        break;
                    case 'pregunta':
                        $mail_organizador.= $datoxevento['nombre'].': '.$this->post[$datoxevento['iddato']];
                        if (isset($this->post[$datoxevento['iddato'].'_texto'])
                            && $this->post[$datoxevento['iddato'].'_texto'] != '') {
                            $mail_organizador.= ' - '.$this->post[$datoxevento['iddato'].'_texto'];
                        }
                        break;
                    default:
                        $mail_organizador.= $datoxevento['nombre'].': '.$this->post[$datoxevento['iddato']];
                        break;
                }
                $mail_organizador.= '<br />';
            }
            if ($this->post['observacion'] != '')
                $mail_organizador.= 'Observación: '.$this->post['observacion'].'<br />';
            $mail_organizador.= '</p>';

        } else if ($accion == 'preinscripto' && $equipo == 'equipo') {

            $mail_organizador = '<p align="justify"><b>Pre-inscripción de un equipo en cronometrajeinstantaneo.com con los siguientes datos</b>:<br /><br />
                <a href="'.URL_ADMIN.$equipo.'?id='.$this->idinscripcion.'">Ver inscripción</a><br /><br />
                Nombre equipo: '.$this->post['nombre'].'<br />
                Categoria: '.$categoria.'<br /><br />';

            for ($i = 1; $i <= $this->route->participantesxequipo(); $i++) {
                $mail_organizador.= '<b>Datos del participante '.$i.'</b><br />
                    Nombre: '.$this->post['nombre'.$i].'<br />
                    Correo electrónico: '.$this->post['mail'.$i].'<br />
                    '.$this->evento->nombre_localidad.': '.$this->post['localidad'.$i].'<br />';
                foreach ($this->evento->datosxevento() as $datoxevento) {
                    switch ($datoxevento['tipo']) {
                        case 'file':
                            if (isset($this->post[$datoxevento['iddato'].$i.'_archivo'])) {
                                $mail_organizador.= $datoxevento['nombre'].': '
                                    .'<a href="https://storage.googleapis.com/cronometrajeinstantaneo-datos/'
                                    .$this->post[$datoxevento['iddato'].$i.'_archivo'].'">Ver archivo</a>';
                            }
                            break;
                        case 'pregunta':
                            $mail_organizador.= $datoxevento['nombre'].': '.$this->post[$datoxevento['iddato'].$i];
                            if (isset($this->post[$datoxevento['iddato'].$i.'_texto'])
                                && $this->post[$datoxevento['iddato'].$i.'_texto'] != '') {
                                $mail_organizador.= ' - '.$this->post[$datoxevento['iddato'].$i.'_texto'];
                            }
                            break;
                        default:
                            $mail_organizador.= $datoxevento['nombre'].': '.$this->post[$datoxevento['iddato'].$i];
                            break;
                    }
                    $mail_organizador.= '<br />';
                }
                if ($this->post['observacion'.$i] != '')
                    $mail_organizador.= 'Observación: '.$this->post['observacion'.$i].'<br />';
                $mail_organizador.= '<br />';
            }
            $mail_organizador.= '</p>';
        }

        return $mail_organizador;
    }

    public function destinatariosParticipantes()
    {
        $destinatarios = array();
        if ($this->route->participantesxequipo() == 1) {
            if (isset($this->post['mail']) &&filter_var($this->post['mail'], FILTER_VALIDATE_EMAIL))
                $destinatarios[$this->post['mail']] = $this->post['nombre'];
            else if (isset($this->mail) &&filter_var($this->mail, FILTER_VALIDATE_EMAIL))
                $destinatarios[$this->mail] = $this->nombre;

        } else {
            for ($i = 1; $i <= $this->route->participantesxequipo(); $i++) {
                if (isset($this->post['mail'.$i]) && filter_var($this->post['mail'.$i], FILTER_VALIDATE_EMAIL))
                    $destinatarios[$this->post['mail'.$i]] = $this->post['nombre'.$i];
                else if (isset($this->{'mail'.$i}) && filter_var($this->{'mail'.$i}, FILTER_VALIDATE_EMAIL))
                    $destinatarios[$this->{'mail'.$i}] = $this->{'nombre'.$i};
            }
        }

        return $destinatarios;
    }

    public function prepararTextos($texto)
    {
        $categoria = (is_array($this->evento->categorias())
            && isset($this->evento->categorias()[$this->idcategoria])
            && isset($this->evento->categorias()[$this->idcategoria]['nombre']))
            ? $this->evento->categorias()[$this->idcategoria]['nombre']
            : '';

        $idcarrera = (is_array($this->evento->categorias())
            && isset($this->evento->categorias()[$this->idcategoria])
            && isset($this->evento->categorias()[$this->idcategoria]['idcarrera']))
            ? $this->evento->categorias()[$this->idcategoria]['idcarrera']
            : '';

        $carrera = (is_array($this->evento->carreras())
            && isset($this->evento->carreras()[$idcarrera]))
            ? $this->evento->carreras()[$idcarrera]
            : [];

        return str_replace(
            array(
                '{{nombre}}',
                '{{categoria}}',
                '{{carrera}}',
                '{{ticket}}',
                '{{participante}}',

                '{{resultados}}',
                '{{resultados-carrera}}',

                '{{preinscripto}}',
                '{{pagar}}',
                '{{inscripto}}',
                '{{rechazado}}',

                '{{inscripciones_texto-carrera}}',
                '{{inscripciones_preinscripto-carrera}}',
                '{{mail_preinscripto-carrera}}',
                // '{{enlace_pago}}',
                // '{{qr}}'
            ),
            array(
                $this->nombre,
                $categoria,
                $carrera['nombre'] ?? '',
                $this->route->url_resultados().$this->evento->codigo
                    .'/ticket?idinscripcion='.$this->idinscripcion,
                $this->idparticipante,

                $this->route->url_resultados().$this->evento->codigo,
                $this->route->url_resultados().$this->evento->codigo
                    .'/?idcarrera='.$idcarrera,

                $this->route->generateUri($this->idinscripcion, 'preinscripto'),
                $this->route->generateUri($this->idinscripcion, 'pagar'),
                $this->route->generateUri($this->idinscripcion, 'inscripto'),
                $this->route->generateUri($this->idinscripcion, 'rechazado'),

                $carrera['inscripciones_texto'] ?? '',
                $carrera['inscripciones_preinscripto'] ?? '',
                $carrera['mail_preinscripto'] ?? '',
                // $enlace_pago,
                // $qr_png
            ),
            html_entity_decode($texto)
        );
    }

    private function generarQR()
    {
        /* Deshabilitado por el momento
        if (strpos($evento['inscripciones_preinscripto'], '{{qr}}') !== false && $url_inscripto) {
            $barcode = new \Com\Tecnick\Barcode\Barcode();
            $bobj = $barcode->getBarcodeObj(
                'QRCODE,H',                     // barcode type and additional comma-separated parameters
                $url_inscripto,          // data string to encode
                -4,                             // bar width (use absolute or negative value as multiplication factor)
                -4,                             // bar height (use absolute or negative value as multiplication factor)
                'black',                        // foreground color
                array(-2, -2, -2, -2)           // padding (use absolute or negative values as multiplication factors)
                )->setBackgroundColor('white'); // background color

            // output the barcode as HTML div (see other output formats in the documentation and examples)
            $qr_div = $bobj->getHtmlDiv();

            $storage = new \Google\Cloud\Storage\StorageClient([
                'projectId' => GOOGLE_CLOUD_PROJECT_ID,
                'keyFilePath' => GOOGLE_CLOUD_KEY_FILE,
            ]);
            $bucket = $storage->bucket('cronometrajeinstantaneo-qrs');
            $bucket->upload(
                $bobj->getPngData(),
                [
                    'predefinedAcl' => 'publicRead',
                    'name' => $codigo.'-'.$idinscripcion.'.png',
                ]
            );
            $qr_png = 'https://storage.googleapis.com/cronometrajeinstantaneo-qrs/'.$codigo.'-'.$idinscripcion.'.png';
        };
        */
    }

    public function inscripcion()
    {
        return $this->inscripcion;
    }

    public function idinscripcion()
    {
        return $this->idinscripcion;
    }

    public function mensajes()
    {
        return $this->mensajes;
    }

    private function validarDescuento($codigo_descuento) {
        if (!$codigo_descuento) return [null, null, null];
        $stmt = $this->db->prepare("SELECT * FROM descuentos WHERE idevento = :idevento AND codigo = :codigo LIMIT 1");
        $stmt->bindParam(':idevento', $this->evento->idevento, PDO::PARAM_INT);
        $stmt->bindParam(':codigo', $codigo_descuento, PDO::PARAM_STR);
        $stmt->execute();
        $descuento = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$descuento) {
            return [null, null, 'El código de descuento no es válido'];
        }
        $ahora = date('Y-m-d H:i:s');
        if (($descuento['valido_desde'] && $descuento['valido_desde'] > $ahora) ||
            ($descuento['valido_hasta'] && $descuento['valido_hasta'] < $ahora)) {
            return [null, null, 'El código de descuento no está vigente'];
        }
        if ($descuento['uso_maximo'] > 0 && $descuento['usos'] >= $descuento['uso_maximo']) {
            return [null, null, 'El código de descuento ya alcanzó el máximo de usos'];
        }
        if ($descuento['uso_maximo'] == 0) {
            return [null, null, 'El código de descuento está desactivado'];
        }
        return [$descuento['iddescuento'], $descuento['descuento'], null];
    }

}
