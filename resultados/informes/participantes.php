<?php

$evento['equipos'] = campo_sql(consulta_sql(
    "SELECT idcategoria FROM categorias WHERE equipo != '' AND idcarrera IN
        (SELECT idcarrera FROM carreras WHERE idevento = $idevento) LIMIT 1"
), 0, 'idcategoria');

$tiene_tags = $evento['tags_descartables']
    ? 1
    : campo_sql(consulta_sql(
        "SELECT EXISTS(SELECT 1 FROM tags WHERE idevento = $idevento) AS tiene"
    ), 0, 'tiene');

$evento['estado_carrera'] = contar_sql(consulta_sql(
    "SELECT idinscripcion FROM participantes WHERE idevento = $idevento
        AND (estado = 'acreditado' OR estado = 'inscripto')
        AND estado_carrera != 'inrace'"));

$estado = (isset($_GET['estado']) && in_array($_GET['estado'], array('acreditado', 'inscripto', 'preinscripto', 'acreditados', 'inscriptos', 'preinscriptos')))
    ? "participantes.estado = '".filter_input(INPUT_GET, 'estado', FILTER_SANITIZE_SPECIAL_CHARS)."'"
    : "participantes.estado NOT IN ('eliminado', 'anulado')";
$estado = str_replace(['acreditados', 'inscriptos', 'preinscriptos'], ['acreditado', 'inscripto', 'preinscripto'], $estado);

switch ($orden) {
    default:
    case 'id':
        $sql_orden = "CASE
            WHEN participantes.estado = 'acreditado' THEN 1
            WHEN participantes.estado = 'inscripto' THEN 2
            WHEN participantes.estado = 'preinscripto' THEN 3
            ELSE 4
        END,
        idparticipante, idinscripcion";
        break;

    case 'categoria':
        $sql_orden = "CASE
            WHEN participantes.estado = 'acreditado' THEN 1
            WHEN participantes.estado = 'inscripto' THEN 2
            WHEN participantes.estado = 'preinscripto' THEN 3
            ELSE 4
        END,
        carreras.orden, categorias.orden, categorias.idcarrera, categorias.nombre, idparticipante, participantes.nombre, idinscripcion";
        break;

    case 'categoria-iddesc':
        $sql_orden = "CASE
            WHEN participantes.estado = 'acreditado' THEN 1
            WHEN participantes.estado = 'inscripto' THEN 2
            WHEN participantes.estado = 'preinscripto' THEN 3
            ELSE 4
        END,
        carreras.orden, categorias.orden, categorias.idcarrera, categorias.nombre, idparticipante DESC, participantes.nombre, idinscripcion";
        break;

    case 'nombre':
        $sql_orden = "CASE
            WHEN participantes.estado = 'acreditado' THEN 1
            WHEN participantes.estado = 'inscripto' THEN 2
            WHEN participantes.estado = 'preinscripto' THEN 3
            ELSE 4
        END,
        participantes.nombre, idparticipante, idinscripcion";
        break;
}

$datosxeventos = array_all_sql(consulta_sql(
    "SELECT datos.iddato, nombre
    FROM datos
        JOIN datosxeventos ON datosxeventos.iddato = datos.iddato
    WHERE idevento = '$idevento' AND resultados = 1 ORDER BY orden"));

// Construir campos de datos extras de forma más eficiente
$campos_datos = '';
$joins_datos = '';
foreach ($datosxeventos as $i => $datoxevento) {
    $alias = "dxp{$i}";
    $campos_datos .= "COALESCE({$alias}.dato, '') AS dato_{$datoxevento['iddato']}, ";
    $joins_datos .= "LEFT JOIN datosxparticipantes {$alias} ON {$alias}.idevento = $idevento 
        AND {$alias}.iddato = '{$datoxevento['iddato']}' 
        AND {$alias}.idinscripcion = participantes.idinscripcion ";
}

// Precarga de datos para equipos para evitar consultas en bucle
$participantes_equipos = [];
if ($evento['equipos']) {
    // Verificar qué columnas existen en la tabla participantes
    $columnas_participantes = consulta_sql("SHOW COLUMNS FROM participantes");
    $columnas_existentes = [];
    while ($columna = array_sql($columnas_participantes)) {
        $columnas_existentes[] = $columna['Field'];
    }
    
    // Construir SELECT dinámicamente basado en columnas existentes
    $campos_select = "p.idequipo, p.idinscripcion, p.nombre";
    if (in_array('apellido', $columnas_existentes)) {
        $campos_select .= ", p.apellido";
    }
    if (in_array('localidad', $columnas_existentes)) {
        $campos_select .= ", p.localidad";
    }
    if (in_array('nacionalidad', $columnas_existentes)) {
        $campos_select .= ", p.nacionalidad";
    }
    if (in_array('marca_logo', $columnas_existentes)) {
        $campos_select .= ", p.marca_logo";
    }
    
    $equipos_sql = consulta_sql(
        "SELECT $campos_select,
                GROUP_CONCAT(CONCAT(dxp.iddato, ':', COALESCE(dxp.dato, '')) SEPARATOR '|') as datos_extras
        FROM participantes p
        LEFT JOIN datosxparticipantes dxp ON dxp.idevento = $idevento AND dxp.idinscripcion = p.idinscripcion
        WHERE p.idevento = '$idevento' AND p.equipo = 'participante'
        GROUP BY p.idinscripcion
        ORDER BY p.idparticipante, p.idinscripcion");
    
    while ($participante_equipo = array_sql($equipos_sql)) {
        if (!isset($participantes_equipos[$participante_equipo['idequipo']])) {
            $participantes_equipos[$participante_equipo['idequipo']] = [];
        }
        
        // Procesar datos extras
        $datos_procesados = [];
        if ($participante_equipo['datos_extras']) {
            foreach (explode('|', $participante_equipo['datos_extras']) as $dato) {
                if (strpos($dato, ':') !== false) {
                    list($iddato, $valor) = explode(':', $dato, 2);
                    $datos_procesados[$iddato] = $valor;
                }
            }
        }
        $participante_equipo['datos_procesados'] = $datos_procesados;
        
        $participantes_equipos[$participante_equipo['idequipo']][] = $participante_equipo;
    }
}

$resultado_sql = consulta_sql(
    "SELECT participantes.*, $campos_datos
        ".(($tiene_tags && !$evento['tags_descartables']) ? "GROUP_CONCAT(DISTINCT tags.codigo SEPARATOR ',') AS codigo," : "")."
        ".($evento['multi_categoria'] ?
            "(SELECT GROUP_CONCAT(DISTINCT categorias.nombre SEPARATOR ', ') FROM categorias WHERE idcategoria IN (SELECT idcategoria FROM categoriasxparticipantes WHERE idinscripcion = participantes.idinscripcion)) AS categorias,
            (SELECT GROUP_CONCAT(DISTINCT carreras.nombre SEPARATOR ', ') FROM categorias LEFT JOIN carreras ON carreras.idcarrera = categorias.idcarrera WHERE idcategoria IN (SELECT idcategoria FROM categoriasxparticipantes WHERE idinscripcion = participantes.idinscripcion)) AS carreras, " : "")."
        categorias.nombre AS categoria,
        categorias.sexo AS sexo,
        carreras.nombre AS carrera
    FROM participantes
        LEFT JOIN categorias ON participantes.idcategoria = categorias.idcategoria
        LEFT JOIN carreras ON categorias.idcarrera = carreras.idcarrera
        $joins_datos
        ".(($tiene_tags && !$evento['tags_descartables']) ? "LEFT JOIN tags ON participantes.idinscripcion = tags.idinscripcion" : "")."
    WHERE participantes.idevento = '$idevento'
        AND participantes.equipo != 'participante'
        AND $estado
    ".(($tiene_tags && !$evento['tags_descartables']) ? "GROUP BY participantes.idinscripcion" : "")."
    ORDER BY ".$sql_orden);

echo '
                <table class="tabla_interna">
                    <thead>
                    <tr>
                        <th colspan="'.(15 + count($datosxeventos) + ($evento['equipos'] ? 2 : 0)).'">Listado de inscriptos</th>
                    </tr>
                    <tr>
                        <th class="orden">Orden</th>
                        <th class="numero">N°</th>
                        <th class="estado">Estado</th>';
if ($evento['estado_carrera'])
    echo '
                        <th class="estado_carrera">Estado Carrera</th>';
if ($tiene_tags)
    echo '
                        <th class="tag">Tag/Chip</th>';
if ($evento['equipos'])
    echo '
                        <th class="equipo">Equipo</th>';
echo '
                        <th class="carrera">Carrera</th>
                        <th class="sexo">Género</th>
                        <th class="categoria">Categoría</th>';
                        if ($evento['tipo_nombre'] == 'Nombre y Apellido separados')
                            echo '
                        <th class="nombre">Nombre</th>
                        <th class="apellido">Apellido</th>';
                        else if ($evento['tipo_nombre'] == 'libre')
                            echo '
                        <th class="nombre">Nombre y Apellido</th>';
                        else
                            echo '
                        <th class="nombre">'.$evento['tipo_nombre'].'</th>';
echo '
                        <th class="localidad">'.$evento['nombre_localidad'].'</th>';
                        foreach ($datosxeventos as $datoxevento) {
                            echo '
                                <th class="'.$datoxevento['iddato'].'">'.$datoxevento['nombre'].'</th>';
                        }
                        echo '
                        </tr>
                    </thead>
                    <tbody>';

$pos = 0;
while ($participante = array_sql($resultado_sql))
{
    $pos++;
    if ($evento['estado_carrera']) {
        switch ($participante['estado_carrera']) {
            default: $estado_carrera = $participante['estado_carrera']; break; // inrace o hooked
            case 'inrace': $estado_carrera = 'En carrera'; break;
            case 'hooked': $estado_carrera = 'Enganchado'; break;
            case 'lap': $estado_carrera = 'LAP'.$participante['estado_carrera_otro']; break;
            case 'dnf': case 'dsq': case 'dns': $estado_carrera = strtoupper($participante['estado_carrera']); break;
            case 'otro': $estado_carrera = $participante['estado_carrera_otro']; break;
        }
    }
    $sexo = match($participante['sexo']) {
        'masculino' => 'M',
        'femenino' => 'F',
        'otro' => 'O',
        'mixto' => 'Mixto',
        'equipomasculino' => 'Equipo<br>M',
        'equipofemenino' => 'Equipo<br>F',
        'equipootro' => 'Equipo<br>O',
        'equipomixto' => 'Equipo<br>Mixto',
        default => $participante['sexo'],
    };

    if (isset($participante['nacionalidad']) && $participante['nacionalidad'])
        $participante['nacionalidad'] = '<img src="'.URL_ADMIN.'img/banderas/'.$participante['nacionalidad'].'.png" title="'.$participante['nacionalidad'].'">';

    if (!$exportar && isset($participante['marca_logo']) && $participante['marca_logo'])
        $participante['marca_logo'] = '<img src="'.URL_ADMIN.'img/logos/'.strtolower($participante['marca_logo']).'.png" title="'.$participante['marca_logo'].'">';

    if (!$participante['equipo']) {
        echo '
                    <tr>
                        <td class="orden" align="center">'.$pos.'</td>
                        <td class="numero" align="center">'.$participante['idparticipante'].'</td>
                        <td class="estado">'.ucfirst($participante['estado']).'</td>';
        if ($evento['estado_carrera'])
            echo '
                        <td class="estado_carrera">'.$estado_carrera.'</td>';
        if ($tiene_tags && !$evento['tags_descartables'])
            echo '
                        <td class="tag">'.$participante['codigo'].'</td>';
        if ($tiene_tags && $evento['tags_descartables'])
            echo '
                        <td class="tag">'.$participante['tag'].'</td>';
        if ($evento['equipos'])
            echo '
                        <td class="equipos individual">Individual</td>';
        echo '
                        <td class="carrera">'.($evento['multi_categoria'] ? $participante['carreras'] : $participante['carrera']).'</td>
                        <td class="sexo">'.$sexo.'</td>
                        <td class="categoria">'.($evento['multi_categoria'] ? $participante['categorias'] : $participante['categoria']).'</td>
                        <td class="nombre">'.$participante['nombre'].'</td>';
        if ($evento['tipo_nombre'] == 'Nombre y Apellido separados')
            echo '
                        <td class="apellido">'.$participante['apellido'].'</td>';
        echo '
                        <td class="localidad">'.$participante['localidad'].'</td>';
                        foreach ($datosxeventos as $datoxevento) {
                            $valor_dato = $participante["dato_{$datoxevento['iddato']}"];
                            echo '
                        <td class="'.$datoxevento['iddato'].'">'.$valor_dato.'</td>';
                        }
            echo '
                    </tr>';

    } else {
        // Usar datos precargados en lugar de hacer consulta
        $participantesxequipo = isset($participantes_equipos[$participante['idinscripcion']])
            ? $participantes_equipos[$participante['idinscripcion']]
            : [];
        $participantesxequipo_count = count($participantesxequipo) ?: 1;

        $equipoxlinea = isset($_GET['equipoxlinea'])
            ? ''
            : 'rowspan="'.$participantesxequipo_count.'"';

        echo '
                    <tr>
                        <td class="orden" '.$equipoxlinea.' align="center">'.$pos.'</td>
                        <td class="numero" '.$equipoxlinea.' align="center">'.$participante['idparticipante'].'</td>
                        <td class="estado" '.$equipoxlinea.'>'.ucfirst($participante['estado']).'</td>';
        if ($evento['estado_carrera'])
            echo '
                        <td class="estado_carrera" '.$equipoxlinea.'>'.ucfirst($participante['estado_carrera']).'</td>';
        if ($tiene_tags && !$evento['tags_descartables'])
            echo '
                        <td class="tag" '.$equipoxlinea.'>'.$participante['codigo'].'</td>';
        if ($tiene_tags && $evento['tags_descartables'])
            echo '
                        <td class="tag" '.$equipoxlinea.'>'.$participante['tag'].'</td>';
        echo '
                        <td class="nombre" '.$equipoxlinea.'>'.$participante['nombre'].'</td>
                        <td class="carrera" '.$equipoxlinea.'>'.($evento['multi_categoria'] ? $participante['carreras'] : $participante['carrera']).'</td>
                        <td class="sexo" '.$equipoxlinea.'>'.$sexo.'</td>
                        <td class="categoria" '.$equipoxlinea.'>'.($evento['multi_categoria'] ? $participante['categorias'] : $participante['categoria']).'</td>';
        
        foreach ($participantesxequipo as $participantexequipo) {
            if (isset($participantexequipo['nacionalidad']) && $participantexequipo['nacionalidad'])
                $participantexequipo['nacionalidad'] = '<img src="'.URL_ADMIN.'img/banderas/'.$participantexequipo['nacionalidad'].'.png" title="'.$participantexequipo['nacionalidad'].'">';

            echo '
                        <td class="nombre">'.$participantexequipo['nombre'].'</td>';
            if ($evento['tipo_nombre'] == 'Nombre y Apellido separados')
                echo '
                        <td class="apellido">'.($participantexequipo['apellido'] ?? '').'</td>';
            echo '
                        <td class="localidad">'.($participantexequipo['localidad'] ?? '').'</td>';
                        foreach ($datosxeventos as $datoxevento) {
                            $valor_dato = isset($participantexequipo['datos_procesados'][$datoxevento['iddato']])
                                ? $participantexequipo['datos_procesados'][$datoxevento['iddato']]
                                : '';
                            echo '
                        <td class="'.$datoxevento['iddato'].'">'.$valor_dato.'</td>';
                        }
            if ($equipoxlinea)
                echo '
                </tr>';
        }
        if (!$equipoxlinea)
            echo '
                </tr>';

    }
}
echo '
                    </tbody>
                </table>';
