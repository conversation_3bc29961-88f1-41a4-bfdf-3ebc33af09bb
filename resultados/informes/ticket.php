<?php

if (!$idparticipante && !$idinscripcion && !$buscar)
    exit('ERROR: no se especificó el n° del participante');

if ($buscar) {
    $participante_sql = consulta_sql(
        "SELECT idparticipante, p.nombre, p.apellido, localidad, estado, estado_carrera, estado_carrera_otro, c.nombre AS categoria
        FROM participantes AS p
            LEFT JOIN categorias AS c ON p.idcategoria = c.idcategoria
            LEFT JOIN datosxparticipantes AS dxp ON p.idinscripcion = dxp.idinscripcion
                AND dxp.iddato =
                    (SELECT iddato FROM datosxeventos WHERE datosxeventos.idevento = '$idevento' AND apps = 1 ORDER BY orden LIMIT 1)
        WHERE p.idevento = '$idevento'
            AND p.estado != 'eliminado'
            AND p.equipo = ''
            AND (p.nombre LIKE '%$buscar%' OR dato LIKE '%$buscar%')
        ORDER BY
        CASE
            WHEN p.estado = 'acreditado' THEN 1
            WHEN p.estado = 'inscripto' THEN 2
            WHEN p.estado = 'preinscripto' THEN 3
            ELSE 4
        END, p.nombre, idparticipante
        LIMIT 1");

} else {
    $participante_sql = consulta_sql(
        "SELECT idparticipante, p.nombre, p.apellido, localidad, estado, estado_carrera, estado_carrera_otro, c.nombre AS categoria
        FROM participantes AS p
            LEFT JOIN categorias AS c ON p.idcategoria = c.idcategoria
        WHERE p.idevento = '$idevento'
            AND p.estado !='eliminado'"
            .($idparticipante ? " AND p.idparticipante = '$idparticipante'" : "")
            .($idinscripcion ? " AND p.idinscripcion = '$idinscripcion'" : ""));
}

if (!contar_sql($participante_sql))
    exit('ERROR: no se encontró un participante');

$participante = array_sql($participante_sql);
$idparticipante = $participante['idparticipante'];


if (is_readable(PATH_CACHE.'/resultados/'.$codigo.'-tickets.json')) {
    $tickets = json_decode(file_get_contents(PATH_CACHE.'/resultados/'.$codigo.'-tickets.json'), true);
    $ticket = isset($tickets[$idparticipante]) ? $tickets[$idparticipante] : null;
} else {
    $ticket = null;
}

if (is_null($ticket)) {
    $numero = $participante['idparticipante'] ? $participante['idparticipante'] : 'Sin asignar';
    $nombre = $evento['tipo_nombre'] != 'Nombre y Apellido separados'
        ? $participante['nombre'] : $participante['nombre'].' '.$participante['apellido'];
    $localidad = $participante['localidad'];
    $categoria = $participante['categoria'];

} else {
    $numero = $ticket['idparticipante'];
    $nombre = $evento['tipo_nombre'] != 'Nombre y Apellido separados'
        ? $ticket['nombre'] : $ticket['nombre'].' '.($ticket['apellido'] ?? '');
    $localidad = $ticket['localidad'];
    $categoria = $ticket['categoria'];


    switch ($participante['estado_carrera']) {
        default: $estado_carrera = $participante['estado_carrera']; break; // inrace o hooked
        case 'lap': $estado_carrera = 'LAP'.$participante['estado_carrera_otro']; break;
        case 'dnf': case 'dsq': case 'dns': $estado_carrera = strtoupper($participante['estado_carrera']); break;
        case 'otro': $estado_carrera = $participante['estado_carrera_otro']; break;
    }

    if (isset($ticket['tiempos']) && is_array($ticket['tiempos']) && count($ticket['tiempos'])) {
        foreach ($ticket['tiempos'] as $idetapa => $etapa) {
            $ticket['tiempos'][$idetapa]['nombre'] = campo_sql(consulta_sql(
                "SELECT nombre FROM etapas WHERE idetapa = '$idetapa'"
            ), 0, 'nombre');
            $ticket['tiempos'][$idetapa]['tiempo'] = isset($etapa['tiempoensegundos']) ? convertir_final($etapa['tiempoensegundos']) : 0;
            if (!isset($etapa['lista_penas']) || !count($etapa['lista_penas'])) {
                $ticket['tiempos'][$idetapa]['lista_penas'] = [];
            } else if ($evento['penas_en_minutos']) {
                foreach ($etapa['lista_penas'] as $key => $pena)
                    $ticket['tiempos'][$idetapa]['lista_penas'][$key]['tiempo'] = $pena['tiempo'];
            }
        }
    } else {
        $ticket['tiempos'] = [];
    }

    if ($evento['parciales']) {
        $pcs = array_all_sql(consulta_sql(
            "SELECT l.tiempo, c.nombre AS nombre_pc, e.idetapa
            FROM lecturas AS l
                LEFT JOIN controles AS c ON l.idcontrol = c.idcontrol
                LEFT JOIN etapas AS e ON c.idetapa = e.idetapa
            WHERE l.idevento = '$idevento'
                AND l.idparticipante = '$idparticipante'
                AND l.estado IN ('ok', 'repetido')
            GROUP BY c.idcontrol
            ORDER BY tiempo"
        ));
    }

    $videos = array_all_sql(consulta_sql(
        "SELECT l.tiempo, v.nombre AS nombre, v.inicio, v.tipo, v.url
        FROM lecturas AS l
            LEFT JOIN controles AS c ON l.idcontrol = c.idcontrol
            LEFT JOIN videos AS v ON c.idcontrol = v.idcontrol
        WHERE l.idevento = '$idevento'
            AND l.idparticipante = '$idparticipante'
            AND l.estado IN ('ok', 'repetido')
            AND l.tiempo > v.inicio
            AND l.tiempo < v.fin
        GROUP BY c.idcontrol
        ORDER BY tiempo"
    ));

    foreach ($videos as $key => $video) {
        $videos[$key]['diferencia'] = strtotime($video['tiempo']) - strtotime($video['inicio']);
        switch ($video['tipo']) {
            case 'youtube':
                $videos[$key]['url'] = str_replace('watch?v=', 'embed/', $video['url'])
                    . '?start='.($videos[$key]['diferencia'] - 15)
                    . '&end='.($videos[$key]['diferencia'] + 15);
                $videos[$key]['iframe'] = '<iframe width="560" height="315" src="'.$videos[$key]['url'].'" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe><br><br>';
                break;
        }
    }
}

?>
<div class="contenedor_ticket">
<center>
<div class="ticket_logo"><?php foreach ($logos as $logo) echo $logo; ?></div>
<div class="ticket">
    <div class="ticket_content">

        <span class="numero">N° <?=$numero?></span><br />
        <span class="nombre"><?=$nombre?></span><br />
        <hr>
<?php if (!is_null($ticket)): ?>
    <?php if ($evento['no_oficial']): ?>
        <span class="no-oficial">Tiempos Extra Oficiales</span><br />
    <?php endif ?>
    <?php if ($participante['estado_carrera'] != 'inrace'): ?>
        <span class="estado_carrera"><?=$estado_carrera?></span><br /><br />
    <?php endif ?>
        <span class="tiempo">Tiempo: <span class="tiempo_nro"><?=$ticket['tiempo']?></span></span><br />
        <span class="pos_general">Pos. general: <span class="pos_general_nro"><?=$ticket['pos']?></span></span><br />
        <span class="pos_sexo">Pos. género: <span class="pos_sexo_nro"><?=$ticket['pos_sexo']?></span></span><br />
        <span class="pos_categoria">Pos. categoría: <span class="pos_categoria_nro"><?=$ticket['pos_categoria']?></span></span><br />
<?php else: ?>
        <span class="tiempo"><span class="tiempo_nro"><?=$participante['estado']?></span></span><br />
<?php endif ?>
        <hr>
        <span class="localidad"><?=$localidad?></span><br />
        <span class="categoria"><?=$categoria?></span><br />
    </div>

<?php if (!is_null($ticket) && count($ticket['tiempos'])): ?>
    <br>
    <div class="ticket_etapas">
        <hr>
    <?php foreach ($ticket['tiempos'] as $etapa): ?>
        <span class="nombre_etapa"><?=$etapa['nombre']?></span><br />
        <?php if (isset($etapa['nombre_parcial'])): ?>
            <span class="nombre_parcial"><?=$etapa['nombre_parcial']?></span>
        <?php endif ?>
        <span class="tiempo_etapa">Tiempo: <span class="tiempo_nro"><?=$etapa['tiempo']?></span></span><br />
            <?php foreach ($etapa['lista_penas'] as $pena): ?>
                <span class="penas_etapa"><?= ($pena['tiempo'] > 0 ? 'Pena por ' : 'Bonus por ') ?> <?=$pena['observacion']?>: <span class="pena_nro"><?=($evento['penas_en_minutos'] ? convertir_final($pena['tiempo']) : $pena['tiempo']) ?></span></span><br />
            <?php endforeach ?>
    <?php endforeach ?>
    </div>
<?php endif ?>

<?php if (!is_null($ticket) && $evento['parciales']): ?>
    <br>
    <div class="ticket_pcs">
        <hr>
    <?php foreach ($pcs as $pc): ?>
        <?php list($pc['dia'], $pc['hora']) = explode(' ', $pc['tiempo']); ?>
        <?php list($pc['horas'], $pc['minutos'], $pc['segundos']) = explode(':', $pc['hora']); ?>
        <?php list($pc['segundos'], $pc['milisegundos']) = explode('.', $pc['segundos']); ?>

        <div class="<?= url_amigable($pc['nombre_pc']) ?>">
            <span class="nombre_pc"><?=$pc['nombre_pc']?></span><br />
            <span class="nombre_hora_pc">Hora: <span class="hora_pc"><?=$pc['hora']?></span></span>
            <span class="nombre_horas_pc">Horas: <span class="horas_pc"><?=$pc['horas']?></span></span>
            <span class="nombre_minutos_pc">Minutos: <span class="minutos_pc"><?=$pc['minutos']?></span></span>
            <span class="nombre_segundos_pc">Segundos: <span class="segundos_pc"><?=$pc['segundos']?></span></span>
            <span class="nombre_milisegundos_pc">Milisegundos: <span class="milisegundos_pc"><?=$pc['milisegundos']?></span></span>
        </div>
    <?php endforeach ?>
    </div>
<?php endif ?>

<?php if (!is_null($ticket) && count($videos)): ?>
    <br>
    <div class="ticket_videos">
        <hr>
    <?php foreach ($videos as $video): ?>
        <span class="nombre_video"><?=$video['nombre']?></span><br />
        <?php echo $video['iframe']; ?>
    <?php endforeach ?>
        <br>
    </div>
<?php endif ?>

    <br>
</div>
</center>
</div>
