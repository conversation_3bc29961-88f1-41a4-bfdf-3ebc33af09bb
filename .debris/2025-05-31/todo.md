# START 🏁

- [Hoy va a ser un buen día](TODO/buendia.md)


*******************************************************************************

# DOING 📆

- [x] Framework @next
- [x] Ordenar index

- [x] Deployar Eventos de verdad
  - [EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46)

---

@saas
- [x] ElastiCache service update
  - https://sa-east-1.console.aws.amazon.com/elasticache/home?region=sa-east-1#/service-updates

@crono
- [x] Responder a Andres de Colombia y audios sobre ayer
- [x] Configurar nuevo router
- [x] Arreglar Chromecast
- [x] Reunión Comodoro
- [x] Acomodar cuentas y mails
- [x] Cotización para José de chile
- [x] Cambios en Travesía de los Cerros

---

@saas
- [x] ANTIAFIP
- [x] Ver consulta Fernando
- [x] Responder mails SaaS

@crono
- [x] Contar los chips usados en Añelo y sacar deuda final
- [x] Imprimir y firmar papel para Silvio ok
- [x] Recibir chips de Damian
- [x] Cambios palabras Paraguay
- [x] Actualizar mensaje fotocélulas y de participantes

@varios
- [x] Pedir turnos para mi espalda (pagar reserva)
- [x] Configurar router a Mati
- [x] Botones Pulsador (Recibir, probar, despachar y grabar vídeo)

---

- [ ] Tapas de vinilos e impresiones
- [ ] Ordenar todo el equipo (preparar para Hybrid)


*******************************************************************************

# FLOW

**API V2**

OBJETIVO: Definir framework para poder contar con esos archivos en API ARCA y empezar a avanzar con MCP e integraciones con una base sólida

- [ ] [API V2](https://gitlab.com/saasargentina/app/-/issues/2037) Tener un framework completo pero sólo Hola Mundo mostrando un producto
- [ ] [API V2 Productos](https://gitlab.com/saasargentina/app/-/issues/2091) Tener todos los recursos de productos

---

**AGENTE MCP Consultas**
_Hasta tener un sincronizador y re ordenar proyecto_

**CRONO Definir Framework Grabaciones**
_Hasta terminar crudos para mkt y poder pasar los cursos a video_
- Grabar material de inscripciones y sports para ahora
- Grabar todo para páginas de deportes
- Dejar ordenado próximas grabaciones
- Grabar los vídeos de deportes
- https://pocket.co/share/c992bc5b-2275-4004-a2bd-5e1e495f9108
- Terminar los vídeos de novedades
- Actualizar novedades y newsletter


*******************************************************************************

# LATER

## CRONO

- [ ] Armar totem con monitor y caja estanco (que se pueda usar para embudo de salida)
- [ ] Armar chapas atrás de las antenas sobre trípodes

## SOPORTE

- [ ] Armar presupuesto Rafting Aluminé
- [ ] Pasar logo a Juli para mates
- [ ] Configurar pistola Javi y re-enviar
- [ ] Coordinar para compatibilizar fotocélulas con Alejandro Malnis

## MOBILE

- [ ] Arreglar puerta del auto
- [ ] Coordinar mis 2 sponsors para el tría
- [ ] Ver como podemos poner el tilde azul en nuestros Whatsapp
- [ ] Comprar sillas más chicas para crono
- [ ] Configurar Streamings con Meli+


## PLAY

- [ ] Imprimir calco para red wifi
- [ ] Imprimir juego https://www.poppularshop.com/morbopoly
- [ ] Imprimir tapa rocola
- [ ] Imprimir entradas
- [ ] Imprimir calco para valija crono y antena 9db

## OFFLINE

- [ ] Colgar macetas en el patio
- [ ] Amurar escritorio Mati y sillón

## BRAIN

- [ ] Ordenar lecturas en Raindrop y acceder a MEGA en tablet
- [ ] Estudiar AI (vídeo de Platzi, estadística y ver que más, armar lista)


*******************************************************************************

## WAITING

- [ ] Recibir pago por chips de Toti
- [ ] Recibir y jugar con mi nuevo teclado (https://andreani.com/envio/360002611905310)