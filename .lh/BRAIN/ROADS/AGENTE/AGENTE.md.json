{"sourceFile": "BRAIN/ROADS/AGENTE/AGENTE.md", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1742849431505, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1742899281604, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,3 +1,12 @@\n \n+## SELENIUM EN AGENTE.AR\n \n\\ No newline at end of file\n-- [ ] Que se pueda obtener el listado\n+- [x] Instalar selenium y correr un test\n+- [x] Crear repositorio git\n+- [x] Desarrollar un index principal\n+- [x] Logearse en local\n+- [ ] Deploy con GitLab CD\n+- [ ] Prisci con git y deploy\n+- [ ] Que se pueda obtener el listado\n+- [ ] Logs y outputs\n+- [ ] Conexión con n8n\n"}, {"date": 1743545571381, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,11 @@\n - [x] Instalar selenium y correr un test\n - [x] Crear repositorio git\n - [x] Desarrollar un index principal\n - [x] Logearse en local\n+- [x] Prisci con git y deploy\n - [ ] Deploy con GitLab CD\n-- [ ] Prisci con git y deploy\n+- [ ] Logs y outputs\n - [ ] Que se pueda obtener el listado\n\\ No newline at end of file\n-- [ ] Logs y outputs\n-- [ ] Conexión con n8n\n+- [ ] Ejecutar con n8n\n+- [ ] Leer la devolución\n+- [ ] Analizar si se puede guardar sesión\n"}, {"date": 1743545581220, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,4 +11,5 @@\n - [ ] Que se pueda obtener el listado\n - [ ] Ejecutar con n8n\n - [ ] Leer la devolución\n - [ ] Ana<PERSON>zar si se puede guardar sesión\n+- [ ] Pasar la pelota\n"}, {"date": 1748276001386, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,11 +5,26 @@\n - [x] Crear repositorio git\n - [x] Desarrollar un index principal\n - [x] Logearse en local\n - [x] Prisci con git y deploy\n-- [ ] Deploy con GitLab CD\n-- [ ] Logs y outputs\n+- [x] Deploy con GitLab CD\n+- [x] Logs y outputs\n - [ ] Que se pueda obtener el listado\n - [ ] Ejecutar con n8n\n - [ ] Leer la devolución\n\\ No newline at end of file\n - [ ] Analizar si se puede guardar sesión\n-- [ ] Pasar la pelota\n+- [ ] Pasar la pelota\n+\n+\n+Esta idea la estoy trabajando en [AIC.md](./AIC.md)\n+\n+## TOKENS\n+\n+En el archivo CloudAPI.postman_environments.json están los datos\n+\n+**Agente Pri**\n+\n+EAAVk1XncURIBO9xXcdmWkQcYAYkzY7KXZC6AZA8I6DuV11SNsyrtulJzkmVySdgKgLBYw7fkZBPgg5I1vF2ZA9Io8uTZAwOSMX4agDOAEnSJbxs9kzAoirT9DjBfQ4YVAdIRPfHODnmoFuoFRPZAJbU1b1DgSAPemEf9SKlmj3Pq9t2hZArTa6cVk1dJVpFSVprOAZDZD\n+\n+**Agente Crono**\n+\n+EAAHC6N82t8MBO0uAmmIGM6ZAP51IWqocxkTfH4Ed6fXKwBMyViWLUciNjq2Ol4Da7UDmscBivCQjZC4f1gAbKOt7hMRbKbgNmKflbS49XHEDDHNNYZCPTXyMUUOK1tqQAp3ZAZBd36NMZAIqEjEPBRijvpoJhwh6ZCmKCpvIvUzHiB6vHqtdRZCDjG5pFCDfxBy1ZCAZDZD\n\\ No newline at end of file\n"}], "date": 1742849431505, "name": "Commit-0", "content": "\n\n- [ ] Que se pueda obtener el listado"}]}