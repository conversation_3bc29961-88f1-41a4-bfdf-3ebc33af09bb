{"sourceFile": "BRAIN/ROADS/SAAS/GROWTH.md", "activeCommit": 0, "commits": [{"activePatchIndex": 13, "patches": [{"date": 1726863778293, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1728656027505, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,8 +17,9 @@\n - Planes de expansión offline, probar primero en Neuquén\n - Todo tiene que ser medido para saber el ROI y cuánto nos sale cada registración, y la permanencia de cada campaña. Preparar un informe automático con esta información\n - Generar una encuesta con las funcionalidades más solicitadas\n - Hacer Webinars con usuarios para conversar sobre las funcionalidades y cómo las usan\n+- Mandar Newsletters: Multimoneda, consejos, mejor ayuda, integraciones\n \n ## CONTENIDOS\n \n TODO: Generar un Framework como el de CRONO para los contenidos de SAAS.\n"}, {"date": 1742397652317, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,25 +1,48 @@\n # 📦 ROADS > SAAS > GROWTH\n -------------------------------------------------------------------------------\n \n-## MKT\n+Considerando que llevamos bastante tiempo sin crecimiento de usuarios y que tenemos todo para lograrlo, en este año me gustaría desarrollar algunas estrategias para lograrlo.\n \n-TODO: Definir los milestones de MKT para el sitio web y las redes, para luego convertirlas en tareas a Todoist.\n+Principalmente se me ocurren las siguiente:\n \n-## IDEAS\n+- Plan SEO para AI: mejorando nuestro posicionamiento orgánico orientado a AI\n+- Plan de micro nicho, generando landings y quizás opciones del sistema específicas para algunos rubros\n+- Plan de expansión offline, probar primero en Neuquén con marketing old-school (folletos, radios, carteles, etc.)\n+- Plan de integraciones, apalancándonos con usuarios de otros sistemas\n \n-- Lograr mejorar el posicionamiento orgánico de la web publicando las ayuda\n+Para todos estos planes quiero escribir un resúmen de objetivos, métricas y tareas, para luego a fin de año evaluarlos y reforzarlos en el futuro.\n+- Todo tiene que ser medido para saber el ROI y cuánto nos sale cada registración, y la permanencia de cada campaña. Preparar un informe automático con esta información\n+\n+## MEJORAR AYUDA\n+\n+- Pedir whatsapp a los usuarios y que Gil pueda buscar por ahí\n+- RAG (en sistema y por whatsapp)\n+- Sabias que\n+\n+## PLAN SEO PARA AI\n+\n+- Generar una encuesta con las funcionalidades más solicitadas\n+https://schema.org/FAQPage\n+\n+AYUDA Ideas para actualizarla\n+fecha_creacion y fecha_actualizacion.\n+Desarrolla una API (REST o GraphQL) para acceder a los datos de la tabla de ayuda. Esto facilitará:\n+Script que genera las ayuda en html\n+Re-direccionamiento en la web (sería ideal que sea con apache-reditect)\n+Considera la creación de un sitemap.xml para ayudar a los motores de búsqueda a indexar la página web.\n+Es importante que el sitemap se actualice automáticamente cada vez que se agregue o modifique contenido en la página web.\n+https://schema.org/FAQPage\n - Lograr que el usuario final chico pueda ver distintas funcionalidades que tiene el software y que beneficios le puede traer\n-- Página de Integraciones\n-- Página de Automatizaciones\n+\n+\n+## PLAN DE MICRO NICHO\n+\n - Páginas para cada uno de los casos de uso, mercados, industrias, etc., como la landings\n   - Por ej https://gestioo.com/producto/software-para-talleres\n - Pensar en pedir la industria y enviar mails y mostrar sugerencias de funcionalidades para esa industria\n-- Planes de expansión offline, probar primero en Neuquén\n-- Todo tiene que ser medido para saber el ROI y cuánto nos sale cada registración, y la permanencia de cada campaña. Preparar un informe automático con esta información\n-- Generar una encuesta con las funcionalidades más solicitadas\n-- Hacer Webinars con usuarios para conversar sobre las funcionalidades y cómo las usan\n-- Mandar Newsletters: Multimoneda, consejos, mejor ayuda, integraciones\n+- Pedir rubro en la configuración\n \n-## CONTENIDOS\n+## PLAN DE INTEGRACIONES\n \n-TODO: Generar un Framework como el de CRONO para los contenidos de SAAS.\n+## PLAN DE EXPANSIÓN OFFLINE\n+\n"}, {"date": 1742397752104, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -32,8 +32,9 @@\n Considera la creación de un sitemap.xml para ayudar a los motores de búsqueda a indexar la página web.\n Es importante que el sitemap se actualice automáticamente cada vez que se agregue o modifique contenido en la página web.\n https://schema.org/FAQPage\n - Lograr que el usuario final chico pueda ver distintas funcionalidades que tiene el software y que beneficios le puede traer\n+- Subir imágenes y vídeos de youtube\n \n \n ## PLAN DE MICRO NICHO\n \n"}, {"date": 1742398451485, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -22,8 +22,11 @@\n ## PLAN SEO PARA AI\n \n - Generar una encuesta con las funcionalidades más solicitadas\n https://schema.org/FAQPage\n+https://schema.org/SoftwareApplication\n+JSON-LD 1.1 conventions\n+https://validator.schema.org/\n \n AYUDA Ideas para actualizarla\n fecha_creacion y fecha_actualizacion.\n Desarrolla una API (REST o GraphQL) para acceder a los datos de la tabla de ayuda. Esto facilitará:\n@@ -33,10 +36,10 @@\n Es importante que el sitemap se actualice automáticamente cada vez que se agregue o modifique contenido en la página web.\n https://schema.org/FAQPage\n - Lograr que el usuario final chico pueda ver distintas funcionalidades que tiene el software y que beneficios le puede traer\n - Subir imágenes y vídeos de youtube\n+https://neilpatel.com/blog/seo-checklist/\n \n-\n ## PLAN DE MICRO NICHO\n \n - Páginas para cada uno de los casos de uso, mercados, industrias, etc., como la landings\n   - Por ej https://gestioo.com/producto/software-para-talleres\n"}, {"date": 1742410009457, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,27 +1,22 @@\n # 📦 ROADS > SAAS > GROWTH\n -------------------------------------------------------------------------------\n \n-Considerando que llevamos bastante tiempo sin crecimiento de usuarios y que tenemos todo para lograrlo, en este año me gustaría desarrollar algunas estrategias para lograrlo.\n+Considerando que llevamos bastante tiempo sin crecimiento de usuarios y que tenemos todo para lograrlo, en este año me gustaría desarrollar algunas estrategias de growth marketing.\n \n Principalmente se me ocurren las siguiente:\n \n - Plan SEO para AI: mejorando nuestro posicionamiento orgánico orientado a AI\n - Plan de micro nicho, generando landings y quizás opciones del sistema específicas para algunos rubros\n - Plan de expansión offline, probar primero en Neuquén con marketing old-school (folletos, radios, carteles, etc.)\n - Plan de integraciones, apalancándonos con usuarios de otros sistemas\n \n-Para todos estos planes quiero escribir un resúmen de objetivos, métricas y tareas, para luego a fin de año evaluarlos y reforzarlos en el futuro.\n-- Todo tiene que ser medido para saber el ROI y cuánto nos sale cada registración, y la permanencia de cada campaña. Preparar un informe automático con esta información\n+Para estos planes van un posible escribir un resúmen de objetivos, métricas y tareas.futuro. Para tener una comparativa, también hay que plasmar algunas estadísticas de hoy. Sería ideal preparar un informe automático con esta información.\n \n-## MEJORAR AYUDA\n \n-- Pedir whatsapp a los usuarios y que Gil pueda buscar por ahí\n-- RAG (en sistema y por whatsapp)\n-- Sabias que\n-\n ## PLAN SEO PARA AI\n \n+La búsqueda está cambiando de sólo Google a otras plataformas que tienen AI: Bing, ChatGPT, Perplexity, etc. Según estuve investigando, todas usan un\n - Generar una encuesta con las funcionalidades más solicitadas\n https://schema.org/FAQPage\n https://schema.org/SoftwareApplication\n JSON-LD 1.1 conventions\n@@ -49,4 +44,11 @@\n ## PLAN DE INTEGRACIONES\n \n ## PLAN DE EXPANSIÓN OFFLINE\n \n+\n+\n+## MEJORAR AYUDA\n+\n+- Pedir whatsapp a los usuarios y que Gil pueda buscar por ahí\n+- RAG (en sistema y por whatsapp)\n+- Sabias que\n"}, {"date": 1742414001300, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -14,41 +14,85 @@\n \n \n ## PLAN SEO PARA AI\n \n-La búsqueda está cambiando de sólo Google a otras plataformas que tienen AI: Bing, ChatGPT, Perplexity, etc. Según estuve investigando, todas usan un\n-- Generar una encuesta con las funcionalidades más solicitadas\n-https://schema.org/FAQPage\n-https://schema.org/SoftwareApplication\n-JSON-LD 1.1 conventions\n-https://validator.schema.org/\n+La búsqueda está cambiando de sólo Google a otras plataformas que tienen AI: Bing, ChatGPT, Perplexity, etc. Según estuve investigando, todas usan un sistema parecido al de Google para indexar la información, pero hay un interés particular en lo que es preguntas y respuestas. Es por esto que se le llama *Optimización de Motores de Respuestas*, o AEO (Answer Engine Optimization). Nosotros tenemos un listado de FAQs que además de para poder mejorar la ayuda, nos puede servir para este propósito.\n \n-AYUDA Ideas para actualizarla\n-fecha_creacion y fecha_actualizacion.\n-Desarrolla una API (REST o GraphQL) para acceder a los datos de la tabla de ayuda. Esto facilitará:\n-Script que genera las ayuda en html\n-Re-direccionamiento en la web (sería ideal que sea con apache-reditect)\n-Considera la creación de un sitemap.xml para ayudar a los motores de búsqueda a indexar la página web.\n-Es importante que el sitemap se actualice automáticamente cada vez que se agregue o modifique contenido en la página web.\n-https://schema.org/FAQPage\n+*Puntualmente los objetivos son:*\n+\n+- Tener una herramienta completa y cómoda para poder gestionar esta información dentro del sistema.\n+- Poder generar una web con esa información para que los usuarios puedan ver esa ayuda de forma ordenada.\n+- Que Gilda pueda compartir enlaces por whatsapp a una pregunta específica de esta ayuda.\n+- Poder mostrar esa información en ventanas flotantes de nuestro sistema.\n+- Poder utilizar toda esa información para posicionamiento orgánico en SEO/AEO.\n+- Poder utilizar esa información para entrenar un Agente de AI RAG para atención a usuarios.\n+\n+*Tareas a realizar:*\n+\n+- Detallo en el issue [AYUDA Ideas para actualizarla](https://gitlab.com/saasargentina/app/-/issues/1162) como empezar a trabajar en esto.\n+- Detallo en el issue [AYUDA Web pública](https://gitlab.com/saasargentina/app/-/issues/2104) como generar una web pública con la ayuda.\n+- Agregar enlace al formulario de encuesta con funcionalidades solicitadas.\n+- Es importante que el sitemap se actualice automáticamente cada vez que se agregue o modifique contenido en la página web.\n - Lograr que el usuario final chico pueda ver distintas funcionalidades que tiene el software y que beneficios le puede traer\n-- Subir imágenes y vídeos de youtube\n-https://neilpatel.com/blog/seo-checklist/\n+- Hacer un checkeo de que tengamos bien configurado todo lo de SEO: https://neilpatel.com/blog/seo-checklist/\n+- En la ayuda agregar un botón de \"Simplifica tu gestión con SaaS\" llevando a una landing. La idea es que si entran desde algún enlace de AI de ver más información y se registren, podemos medirlo.\n \n+*Métricas:*\n+\n+- La principal métrica va a ser que a Gilda le ayude con el soporte, lo que ella sienta.\n+- Al usar una landing podemos medir quienes se registraron desde ver una ayuda.\n+- Luego hay que encontrar una forma de medir nuestro posicionamiento antes y después del cambio.\n+\n+\n ## PLAN DE MICRO NICHO\n \n-- Páginas para cada uno de los casos de uso, mercados, industrias, etc., como la landings\n-  - Por ej https://gestioo.com/producto/software-para-talleres\n-- Pensar en pedir la industria y enviar mails y mostrar sugerencias de funcionalidades para esa industria\n-- Pedir rubro en la configuración\n+La idea es poder apuntar en campañas de redes sociales a micro nichos. Vamos a probar contratando a la empresa Beside, pero más allá de eso, podemos hacer un replanteamiento de nuestra propuesta.\n \n+*Tareas a realizar:*\n+\n+Antes de comenzar, vamos a analizar los siguientes datos:\n+\n+- Cuántos hay en FE y cuánto emiten\n+- Cuántos FE pasaron a Full y viceversa\n+- Estadísticas de logs completos\n+\n+Luego la idea es generar distintas versiones del producto, detallado en el issue [VERSIONES Generar primeras](https://gitlab.com/saasargentina/app/-/issues/2105)\n+\n+- Versión reducida para emprendedores que están comenzando (en realidad es para los ratas o los que les está yendo mal)\n+- Versión de comercio online: ventajas de integraciones varias, todo online, 24hs, etc.\n+- Versión para informática\n+- Versión para talleres y autopartes\n+- Versión para ferreterías\n+\n+\n+Hay que hacer páginas para cada uno de los casos de uso primero como landings y luego como subproductos en un menú y con su propia página (Por ej https://gestioo.com/producto/software-para-talleres ). Detallo este trabajo en el issue [VERSIONES Landings](https://gitlab.com/saasargentina/app/-/issues/2106)\n+\n+Si empieza a funcionar, podemos filtrar entre nuestros clientes y pasarlos. También podemos enviar mails específicos.\n+\n+*Métricas:*\n+\n+- Hay que hacer un informe o board para poder ver fácilmente cuales de las versiones están funcionando mejor.\n+\n+\n ## PLAN DE INTEGRACIONES\n \n-## PLAN DE EXPANSIÓN OFFLINE\n+Las integraciones nos ayudan porque son clientes que vienen recomendados de otra plataforma, sin que hagamos inversión en publicidad y con una alta probabilidad de que sigan como clientes. Si bien tenemos una fuerte inversión en desarrollo para que esto suceda, creo que vale la pena.\n \n+*Tareas a realizar:*\n \n+- Tener reuniones con cada una de las integraciones\n+- Establecer un acuerdo para que entre ambas empresas podamos recomendar a la otra\n+- Actualizar algunos cambios en nuestro sistema de landings y probarlo bien. Lo detalle en el issue [LANDINGS Actualizar](https://gitlab.com/saasargentina/app/-/issues/1965)\n+- Generar landings para cada integración. La de VentasxMayor hay que revisarla. La de Más Pedidos hay que hacerla en el issue [LANDINGS Más Pedidos](https://gitlab.com/saasargentina/app/-/issues/2107)\n+- Hacer en nuestra página una sección de integraciones explicando bien que son, para que sirven y que nos sirva de posicionamiento.\n+- Evaluar agregar en el sistema una configuración de Tienda con las opciones, para que lo usuarios sepan que existen todas esas opciones.\n+- Enviar notificación y/o mail avisando de todas las nuevas integraciones.\n+- Buscar alguna integración con algún sistema de logística de entregas o envíos.\n \n-## MEJORAR AYUDA\n+*Métricas:*\n \n-- Pedir whatsapp a los usuarios y que Gil pueda buscar por ahí\n-- RAG (en sistema y por whatsapp)\n-- Sabias que\n+- Tenemos que hacer una métrica sobre todas las landings en general, que nos va a servir para esta y todas las estrategias.\n+\n+\n+## PLAN DE EXPANSIÓN OFFLINE\n+\n+La idea es probar con publicidad en medios tradicionales de Neuquén. Podemos averiguar en https://primamultimedios.com que es uno de los medios más grandes de la zona\n"}, {"date": 1747307930197, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,21 @@\n # 📦 ROADS > SAAS > GROWTH\n -------------------------------------------------------------------------------\n \n+## ORDENAR\n+\n+- Empezar proceso de MKT en Neuquén\n+  - [PRIMA Multimedios](https://primamultimedios.com/)\n+  - Visitar Centro Pyme\n+  - Repartir folletos\n+  - Averiguar por radios y cartelería\n+- Métricas automáticas en Board\n+- Empezar con Google Ads\n+- Empezar MKT \"Pasate a SaaS\" para traer usuarios de otros sistemas\n+\n+\n+## INTRODUCCIÓN\n+\n Considerando que llevamos bastante tiempo sin crecimiento de usuarios y que tenemos todo para lograrlo, en este año me gustaría desarrollar algunas estrategias de growth marketing.\n \n Principalmente se me ocurren las siguiente:\n \n"}, {"date": 1747319859296, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,5 @@\n # 📦 ROADS > SAAS > GROWTH\n--------------------------------------------------------------------------------\n \n ## ORDENAR\n \n - Empezar proceso de MKT en Neuquén\n"}, {"date": 1748471616080, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,12 +17,12 @@\n Considerando que llevamos bastante tiempo sin crecimiento de usuarios y que tenemos todo para lograrlo, en este año me gustaría desarrollar algunas estrategias de growth marketing.\n \n Principalmente se me ocurren las siguiente:\n \n-- Plan SEO para AI: mejorando nuestro posicionamiento orgánico orientado a AI\n+- Plan de integraciones, apalancándonos con usuarios de otros sistemas\n+- Plan SEO para AI desde FAQs: mejorando nuestro posicionamiento orgánico orientado a AI\n - Plan de micro nicho, generando landings y quizás opciones del sistema específicas para algunos rubros\n - Plan de expansión offline, probar primero en Neuquén con marketing old-school (folletos, radios, carteles, etc.)\n-- Plan de integraciones, apalancándonos con usuarios de otros sistemas\n \n Para estos planes van un posible escribir un resúmen de objetivos, métricas y tareas.futuro. Para tener una comparativa, también hay que plasmar algunas estadísticas de hoy. Sería ideal preparar un informe automático con esta información.\n \n \n"}, {"date": 1749135324197, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,12 +17,12 @@\n Considerando que llevamos bastante tiempo sin crecimiento de usuarios y que tenemos todo para lograrlo, en este año me gustaría desarrollar algunas estrategias de growth marketing.\n \n Principalmente se me ocurren las siguiente:\n \n-- Plan de integraciones, apalancándonos con usuarios de otros sistemas\n-- Plan SEO para AI desde FAQs: mejorando nuestro posicionamiento orgánico orientado a AI\n-- Plan de micro nicho, generando landings y quizás opciones del sistema específicas para algunos rubros\n-- Plan de expansión offline, probar primero en Neuquén con marketing old-school (folletos, radios, carteles, etc.)\n+- *Plan de integraciones*, apalancándonos con usuarios de otros sistemas\n+- *Plan SEO para AI desde FAQs*: mejorando nuestro posicionamiento orgánico orientado a AI\n+- *Plan de micro nicho*: generando landings y quizás opciones del sistema específicas para algunos rubros\n+- *Plan de expansión offline*: probar primero en Neuquén con marketing old-school (folletos, radios, carteles, etc.)\n \n Para estos planes van un posible escribir un resúmen de objetivos, métricas y tareas.futuro. Para tener una comparativa, también hay que plasmar algunas estadísticas de hoy. Sería ideal preparar un informe automático con esta información.\n \n \n"}, {"date": 1749140522464, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -27,24 +27,26 @@\n \n \n ## PLAN SEO PARA AI\n \n-La búsqueda está cambiando de sólo Google a otras plataformas que tienen AI: Bing, ChatGPT, Perplexity, etc. Según estuve investigando, todas usan un sistema parecido al de Google para indexar la información, pero hay un interés particular en lo que es preguntas y respuestas. Es por esto que se le llama *Optimización de Motores de Respuestas*, o AEO (Answer Engine Optimization). Nosotros tenemos un listado de FAQs que además de para poder mejorar la ayuda, nos puede servir para este propósito.\n+La búsqueda está cambiando de sólo Google a otras plataformas que tienen AI: Bing, ChatGPT, Perplexity, etc. Seg<PERSON> estuve investigando, todas usan un sistema parecido al de Google para indexar la información de sitios web durante el entrenamiento del LLM, pero hay un interés particular en lo que es preguntas y respuestas. Es por esto que se le llama *Optimización de Motores de Respuestas*, o AEO (Answer Engine Optimization). Nosotros tenemos un listado de FAQs que además de para poder mejorar la ayuda, nos puede servir para este propósito.\n \n+Además si tenemos la información ordenada de esta forma, es mucho más fácil para entrenar un agente que haga de soporte con un RAG.\n+\n *Puntualmente los objetivos son:*\n \n-- Tener una herramienta completa y cómoda para poder gestionar esta información dentro del sistema.\n+- Tener una herramienta completa y cómoda para poder gestionar esta información dentro del sistema sin programar.\n - Poder generar una web con esa información para que los usuarios puedan ver esa ayuda de forma ordenada.\n - Que Gilda pueda compartir enlaces por whatsapp a una pregunta específica de esta ayuda.\n - Poder mostrar esa información en ventanas flotantes de nuestro sistema.\n - Poder utilizar toda esa información para posicionamiento orgánico en SEO/AEO.\n - Poder utilizar esa información para entrenar un Agente de AI RAG para atención a usuarios.\n \n *Tareas a realizar:*\n \n-- Detallo en el issue [AYUDA Ideas para actualizarla](https://gitlab.com/saasargentina/app/-/issues/1162) como empezar a trabajar en esto.\n-- Detallo en el issue [AYUDA Web pública](https://gitlab.com/saasargentina/app/-/issues/2104) como generar una web pública con la ayuda.\n-- Agregar enlace al formulario de encuesta con funcionalidades solicitadas.\n+- Agregar campos a nuestra tabla de preguntas frecuentes en el issue [FAQS Agregar campos](https://gitlab.com/saasargentina/app/-/issues/2103)\n+- Agregar mejoras en la ayuda dentro del sistema en el issue [AYUDA Ideas para actualizarla](https://gitlab.com/saasargentina/app/-/issues/1162)\n+- Agregar un sector en la web con el listado de ayuda ordenado [AYUDA Web pública](https://gitlab.com/saasargentina/app/-/issues/2104)\n - Es importante que el sitemap se actualice automáticamente cada vez que se agregue o modifique contenido en la página web.\n - Lograr que el usuario final chico pueda ver distintas funcionalidades que tiene el software y que beneficios le puede traer\n - Hacer un checkeo de que tengamos bien configurado todo lo de SEO: https://neilpatel.com/blog/seo-checklist/\n - En la ayuda agregar un botón de \"Simplifica tu gestión con SaaS\" llevando a una landing. La idea es que si entran desde algún enlace de AI de ver más información y se registren, podemos medirlo.\n@@ -91,16 +93,16 @@\n Las integraciones nos ayudan porque son clientes que vienen recomendados de otra plataforma, sin que hagamos inversión en publicidad y con una alta probabilidad de que sigan como clientes. Si bien tenemos una fuerte inversión en desarrollo para que esto suceda, creo que vale la pena.\n \n *Tareas a realizar:*\n \n-- Tener reuniones con cada una de las integraciones\n-- Establecer un acuerdo para que entre ambas empresas podamos recomendar a la otra\n-- Actualizar algunos cambios en nuestro sistema de landings y probarlo bien. Lo detalle en el issue [LANDINGS Actualizar](https://gitlab.com/saasargentina/app/-/issues/1965)\n-- Generar landings para cada integración. La de VentasxMayor hay que revisarla. La de Más Pedidos hay que hacerla en el issue [LANDINGS Más Pedidos](https://gitlab.com/saasargentina/app/-/issues/2107)\n-- Hacer en nuestra página una sección de integraciones explicando bien que son, para que sirven y que nos sirva de posicionamiento.\n-- Evaluar agregar en el sistema una configuración de Tienda con las opciones, para que lo usuarios sepan que existen todas esas opciones.\n-- Enviar notificación y/o mail avisando de todas las nuevas integraciones.\n-- Buscar alguna integración con algún sistema de logística de entregas o envíos.\n+- [x] Tener reuniones con cada una de las integraciones\n+- [ ] Establecer un acuerdo para que entre ambas empresas podamos recomendar a la otra\n+- [ ] Actualizar algunos cambios en nuestro sistema de landings y probarlo bien. Lo detalle en el issue [LANDINGS Actualizar](https://gitlab.com/saasargentina/app/-/issues/1965)\n+- [ ] Generar landings para cada integración. La de VentasxMayor hay que revisarla. La de Más Pedidos hay que hacerla en el issue [LANDINGS Más Pedidos](https://gitlab.com/saasargentina/app/-/issues/2107)\n+- [ ] Hacer en nuestra página una sección de integraciones explicando bien que son, para que sirven y que nos sirva de posicionamiento.\n+- [ ] Evaluar agregar en el sistema una configuración de Tienda con las opciones, para que lo usuarios sepan que existen todas esas opciones.\n+- [ ] Enviar notificación y/o mail avisando de todas las nuevas integraciones.\n+- [ ] Buscar alguna integración con algún sistema de logística de entregas o envíos.\n \n *Métricas:*\n \n - Tenemos que hacer una métrica sobre todas las landings en general, que nos va a servir para esta y todas las estrategias.\n"}, {"date": 1749154573348, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,11 +3,9 @@\n ## ORDENAR\n \n - Empezar proceso de MKT en Neuquén\n   - [PRIMA Multimedios](https://primamultimedios.com/)\n-  - Visitar Centro Pyme\n-  - Repartir folletos\n-  - Averiguar por radios y cartelería\n+  -\n - Métricas automáticas en Board\n - Empezar con Google Ads\n - Empezar MKT \"Pasate a SaaS\" para traer usuarios de otros sistemas\n \n@@ -93,21 +91,21 @@\n Las integraciones nos ayudan porque son clientes que vienen recomendados de otra plataforma, sin que hagamos inversión en publicidad y con una alta probabilidad de que sigan como clientes. Si bien tenemos una fuerte inversión en desarrollo para que esto suceda, creo que vale la pena.\n \n *Tareas a realizar:*\n \n-- [x] Tener reuniones con cada una de las integraciones\n-- [ ] Establecer un acuerdo para que entre ambas empresas podamos recomendar a la otra\n-- [ ] Actualizar algunos cambios en nuestro sistema de landings y probarlo bien. Lo detalle en el issue [LANDINGS Actualizar](https://gitlab.com/saasargentina/app/-/issues/1965)\n-- [ ] Generar landings para cada integración. La de VentasxMayor hay que revisarla. La de Más Pedidos hay que hacerla en el issue [LANDINGS Más Pedidos](https://gitlab.com/saasargentina/app/-/issues/2107)\n-- [ ] Hacer en nuestra página una sección de integraciones explicando bien que son, para que sirven y que nos sirva de posicionamiento.\n-- [ ] Evaluar agregar en el sistema una configuración de Tienda con las opciones, para que lo usuarios sepan que existen todas esas opciones.\n-- [ ] Enviar notificación y/o mail avisando de todas las nuevas integraciones.\n-- [ ] Buscar alguna integración con algún sistema de logística de entregas o envíos.\n+- Tener reuniones con cada una de las integraciones\n+- Establecer un acuerdo para que entre ambas empresas podamos recomendar a la otra\n+- Actualizar algunos cambios en nuestro sistema de landings y probarlo bien. Lo detalle en el issue [LANDINGS Actualizar](https://gitlab.com/saasargentina/app/-/issues/1965)\n+- Generar landings para cada integración. La de VentasxMayor hay que revisarla. La de Más Pedidos hay que hacerla en el issue [LANDINGS Más Pedidos](https://gitlab.com/saasargentina/app/-/issues/2107)\n+- Hacer en nuestra página una sección de integraciones explicando bien que son, para que sirven y que nos sirva de posicionamiento.\n+- Evaluar agregar en el sistema una configuración de Tienda con las opciones, para que lo usuarios sepan que existen todas esas opciones.\n+- Enviar notificación y/o mail avisando de todas las nuevas integraciones.\n+- Buscar alguna integración con algún sistema de logística de entregas o envíos.\n \n *Métricas:*\n \n - Tenemos que hacer una métrica sobre todas las landings en general, que nos va a servir para esta y todas las estrategias.\n \n \n ## PLAN DE EXPANSIÓN OFFLINE\n \n-La idea es probar con publicidad en medios tradicionales de Neuquén. Podemos averiguar en https://primamultimedios.com que es uno de los medios más grandes de la zona\n+La idea es probar con publicidad en medios tradicionales de Neuquén. Además podemos Visitar Centro Pyme, Repartir folletos, Averiguar por radios y cartelería. Podemos empezar por averiguar en https://primamultimedios.com que es uno de los medios más grandes de la zona. Si funciona lo seguimos en otras ciudades.\n"}, {"date": 1749154582925, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,16 +1,6 @@\n # 📦 ROADS > SAAS > GROWTH\n \n-## ORDENAR\n-\n-- Empezar proceso de MKT en Neuquén\n-  - [PRIMA Multimedios](https://primamultimedios.com/)\n-  -\n-- Métricas automáticas en Board\n-- Em<PERSON>zar con Google Ads\n-- Empezar MKT \"Pasate a SaaS\" para traer usuarios de otros sistemas\n-\n-\n ## INTRODUCCIÓN\n \n Considerando que llevamos bastante tiempo sin crecimiento de usuarios y que tenemos todo para lograrlo, en este año me gustaría desarrollar algunas estrategias de growth marketing.\n \n"}], "date": 1726863778293, "name": "Commit-0", "content": "# 📦 ROADS > SAAS > GROWTH\n-------------------------------------------------------------------------------\n\n## MKT\n\nTODO: Definir los milestones de MKT para el sitio web y las redes, para luego convertirlas en tareas a Todoist.\n\n## IDEAS\n\n- Lograr mejorar el posicionamiento orgánico de la web publicando las ayuda\n- Lograr que el usuario final chico pueda ver distintas funcionalidades que tiene el software y que beneficios le puede traer\n- Página de Integraciones\n- Página de Automatizaciones\n- Páginas para cada uno de los casos de uso, mercados, industrias, etc., como la landings\n  - Por ej https://gestioo.com/producto/software-para-talleres\n- Pensar en pedir la industria y enviar mails y mostrar sugerencias de funcionalidades para esa industria\n- Planes de expansión offline, probar primero en Neuquén\n- Todo tiene que ser medido para saber el ROI y cuánto nos sale cada registración, y la permanencia de cada campaña. Preparar un informe automático con esta información\n- Generar una encuesta con las funcionalidades más solicitadas\n- Hacer Webinars con usuarios para conversar sobre las funcionalidades y cómo las usan\n\n## CONTENIDOS\n\nTODO: Generar un Framework como el de CRONO para los contenidos de SAAS.\n"}]}