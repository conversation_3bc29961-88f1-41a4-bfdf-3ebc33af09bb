{"sourceFile": "BRAIN/ROADS/SAAS/SAAS.md", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1735965272776, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1748287053396, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,12 +16,9 @@\n - MKT de micro nicho\n - Integraciones: nueva API, Logs, ML, etc\n \n \n-\n ## 🏄‍♂️ BOARDS\n \n-| Dev                                                                | Growth                                                        | Soporte                                                        |\n-| ------------------------------------------------------------------ | ------------------------------------------------------------- | -------------------------------------------------------------- |\n-| [DEV](./DEV.md)                                                    | [GROWTH](./GROWTH.md)                                         | [SOPORTE](./SOPORTE.md)                                        |\n-| [BOARD](https://gitlab.com/saasargentina/app/-/boards) | [TODO](https://app.todoist.com/app/project/growth-2318317139) | [TODO](https://app.todoist.com/app/project/soporte-2317711574) |\n-| [TODO](https://app.todoist.com/app/project/dev-2317711540)         |                                                               |                                                                |\n+[DEV](./DEV.md)\n+[GROWTH](./GROWTH.md)\n+[SOPORTE](./SOPORTE.md)\n"}, {"date": 1748287063893, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,6 +19,8 @@\n \n ## 🏄‍♂️ BOARDS\n \n [DEV](./DEV.md)\n+\n [GROWTH](./GROWTH.md)\n+\n [SOPORTE](./SOPORTE.md)\n"}, {"date": 1748615911742, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,9 +18,9 @@\n \n \n ## 🏄‍♂️ BOARDS\n \n-[DEV](./DEV.md)\n+[DEV](./DEV.md#milestones-dev)\n \n [GROWTH](./GROWTH.md)\n \n [SOPORTE](./SOPORTE.md)\n"}], "date": 1735965272776, "name": "Commit-0", "content": "# 📦 ROADS > SAAS\n-------------------------------------------------------------------------------\n\n## 📊 PLAN\n\n[PLAN](./PLAN.md)\n\n[KPIs](./KPIs.md)\n\n\n## PRÓXIMOS MILESTONES\n\nLas 3 prioridades van a ser\n\n- Automatizaciones\n- MKT de micro nicho\n- Integraciones: nueva API, Logs, ML, etc\n\n\n\n## 🏄‍♂️ BOARDS\n\n| Dev                                                                | Growth                                                        | Soporte                                                        |\n| ------------------------------------------------------------------ | ------------------------------------------------------------- | -------------------------------------------------------------- |\n| [DEV](./DEV.md)                                                    | [GROWTH](./GROWTH.md)                                         | [SOPORTE](./SOPORTE.md)                                        |\n| [BOARD](https://gitlab.com/saasargentina/app/-/boards) | [TODO](https://app.todoist.com/app/project/growth-2318317139) | [TODO](https://app.todoist.com/app/project/soporte-2317711574) |\n| [TODO](https://app.todoist.com/app/project/dev-2317711540)         |                                                               |                                                                |\n"}]}