{"sourceFile": "BRAIN/ROADS/SAAS/SOPORTE.md", "activeCommit": 0, "commits": [{"activePatchIndex": 50, "patches": [{"date": 1725281405250, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1725283937038, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,14 +20,16 @@\n rm saas_6149_clientes.sql.gz\n rm saas_6149_ventas.sql.gz\n rm saas_6149_ivasxventas.sql.gz\n rm saas_6149_tributosxventas.sql.gz\n+rm saas_6149_ventas_ml.sql.gz\n rm saas_6149_datosxextras.sql.gz\n \n mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 clientes | gzip -9 > saas_6149_clientes.sql.gz\n mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas --where=\"fecha > '2024-08-01'\" | gzip -9 > saas_6149_ventas.sql.gz\n mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ivasxventas | gzip -9 > saas_6149_ivasxventas.sql.gz\n mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 tributosxventas | gzip -9 > saas_6149_tributosxventas.sql.gz\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas_ml | gzip -9 > saas_6149_ventas_ml.sql.gz\n mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 datosxextras --where=\"idextraxmodulo IN (5, 12)\" | gzip -9 > saas_6149_datosxextras.sql.gz\n \n exit\n cd /home/<USER>/SOPORTE/6149\n@@ -35,32 +37,39 @@\n rm saas_6149_clientes.sql\n rm saas_6149_ventas.sql\n rm saas_6149_ivasxventas.sql\n rm saas_6149_tributosxventas.sql\n+rm saas_6149_ventas_ml.sql\n rm saas_6149_datosxextras.sql\n \n scp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_clientes.sql.gz .\n scp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_ventas.sql.gz .\n scp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_ivasxventas.sql.gz .\n scp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_tributosxventas.sql.gz .\n+scp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_ventas_ml.sql.gz .\n scp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_datosxextras.sql.gz .\n \n gzip -d saas_6149_clientes.sql.gz\n gzip -d saas_6149_ventas.sql.gz\n gzip -d saas_6149_ivasxventas.sql.gz\n gzip -d saas_6149_tributosxventas.sql.gz\n+gzip -d saas_6149_ventas_ml.sql.gz\n gzip -d saas_6149_datosxextras.sql.gz\n \n+sudo mysql -u root -p\n+\n USE saas_6149;\n TRUNCATE clientes;\n TRUNCATE ventas;\n TRUNCATE ivasxventas;\n TRUNCATE tributosxventas;\n+TRUNCATE ventas_ml;\n TRUNCATE datosxextras;\n SOURCE saas_6149_ventas.sql;\n SOURCE saas_6149_clientes.sql;\n SOURCE saas_6149_ivasxventas.sql;\n SOURCE saas_6149_tributosxventas.sql;\n+SOURCE saas_6149_ventas_ml.sql;\n SOURCE saas_6149_datosxextras.sql;\n UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n \n \n"}, {"date": 1725448692479, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,13 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n+### EMPRESAS EN BETA Y ALFA\n+\n+(98, 161, 2629)\n+(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664)\n+\n+\n ### SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n \n SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n FROM ventas AS v\n@@ -145,4 +151,57 @@\n - Pasar la base de datos a BETA\n - Forzar una sincronización completa con productos en updated_at = now()\n - Activar 90 días de prueba\n \n+\n+### CRONTAB\n+\n+Scripts de la empresa Nº 99 (SaaS Argentina):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 no debe ejecutarse hoy.\n+El script Nº 3 no debe ejecutarse hoy.\n+\n+Scripts de la empresa Nº 161 (Cronometraje Instantáneo):\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 182 (Cámara de Comercio, Turismo, Industria y Producción de Villa La Angostura):\n+El script Nº 2 no debe ejecutarse hoy.\n+\n+Scripts de la empresa Nº 1387 (Gezatek SRL):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 2142 (Gezatek SRL):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+---\n+\n+Scripts de la empresa Nº 4052 (Soy Gamer BELGRANO):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8802 (RUTIDEN SUC CIPOLLETTI):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8816 (Almacén del Bioquímico):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8918 (ROMEO BOXES):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 9589 (GAMING CITY ABASTO SHOPPING):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 9746 (GAMING CITY CABALLITO):\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 11360 (Ipanema Distribuciones):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+---\n+\n+Scripts de la empresa Nº 874 (Gaming-City Computacion):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 6149 (Duaitek SRL):\n+El script Nº 1 se ejecutó correctamente.\n"}, {"date": 1726270297906, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,9 +3,9 @@\n \n ### EMPRESAS EN BETA Y ALFA\n \n (98, 161, 2629)\n-(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664)\n+(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664, 12281)\n \n \n ### SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n \n"}, {"date": 1726585745026, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,18 +10,29 @@\n ### SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n \n SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n FROM ventas AS v\n-LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n-LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n-LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\n-LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n-LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n+    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n+    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n+    LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\n+    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n+    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n-AND tipofacturacion = 'electronico'\n+    AND tipofacturacion = 'electronico'\n ORDER BY fecha\n \n+SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n+FROM ventas AS v\n+    JOIN datosxextras AS d ON d.idextraxmodulo = 21 AND v.idventa = d.idrelacion\n+    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n+    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n+    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n+    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n+WHERE fecha > '2024-08-01' AND fecha < '2024-09-01'\n+    AND d.idlistaxextra = 30\n+ORDER BY fecha\n \n+\n prod_saas\n cd 6149\n rm saas_6149_clientes.sql.gz\n rm saas_6149_ventas.sql.gz\n"}, {"date": 1726585788035, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,8 +8,10 @@\n \n \n ### SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n \n+*INFORME VENTAS ANUAL*\n+\n SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n FROM ventas AS v\n     LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n     LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n@@ -19,8 +21,10 @@\n WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n     AND tipofacturacion = 'electronico'\n ORDER BY fecha\n \n+*INFORME VENTAS PRECENCIAL*\n+\n SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n FROM ventas AS v\n     JOIN datosxextras AS d ON d.idextraxmodulo = 21 AND v.idventa = d.idrelacion\n     LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n@@ -30,8 +34,9 @@\n WHERE fecha > '2024-08-01' AND fecha < '2024-09-01'\n     AND d.idlistaxextra = 30\n ORDER BY fecha\n \n+*EXPORTACIÓN PARA INFORMES OFFLINE*\n \n prod_saas\n cd 6149\n rm saas_6149_clientes.sql.gz\n"}, {"date": 1726586614062, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -50,9 +50,9 @@\n mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas --where=\"fecha > '2024-08-01'\" | gzip -9 > saas_6149_ventas.sql.gz\n mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ivasxventas | gzip -9 > saas_6149_ivasxventas.sql.gz\n mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 tributosxventas | gzip -9 > saas_6149_tributosxventas.sql.gz\n mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas_ml | gzip -9 > saas_6149_ventas_ml.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 datosxextras --where=\"idextraxmodulo IN (5, 12)\" | gzip -9 > saas_6149_datosxextras.sql.gz\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 datosxextras --where=\"idextraxmodulo IN (5, 12, 21)\" | gzip -9 > saas_6149_datosxextras.sql.gz\n \n exit\n cd /home/<USER>/SOPORTE/6149\n \n"}, {"date": 1726588471533, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -62,14 +62,14 @@\n rm saas_6149_tributosxventas.sql\n rm saas_6149_ventas_ml.sql\n rm saas_6149_datosxextras.sql\n \n-scp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_clientes.sql.gz .\n-scp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_ventas.sql.gz .\n-scp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_ivasxventas.sql.gz .\n-scp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_tributosxventas.sql.gz .\n-scp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_ventas_ml.sql.gz .\n-scp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_datosxextras.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_clientes.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ventas.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ivasxventas.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_tributosxventas.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ventas_ml.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_datosxextras.sql.gz .\n \n gzip -d saas_6149_clientes.sql.gz\n gzip -d saas_6149_ventas.sql.gz\n gzip -d saas_6149_ivasxventas.sql.gz\n"}, {"date": 1726871683127, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,13 +1,43 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n-### EMPRESAS EN BETA Y ALFA\n \n+\n+-------------------------------------------------------------------\n+\n+## EMPRESAS EN BETA Y ALFA\n+\n (98, 161, 2629)\n (168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664, 12281)\n \n \n+## PROBLEMAS ZUMBANDO\n+\n+### MAIL:\n+\n+Nombre de usuario de IAM: ses-smtp-user.20240702-164504\n+Nombre de usuario de SMTP: ********************\n+Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n+\n+NEW IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n+\n+EX IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n+\n+\n+### LAMDA:\n+\n+Acceso: https://saasargentina.signin.aws.amazon.com/console\n+User: empresa_9589_script_1\n+Password: qZbquV3&\n+Region: San Pablo (sa-east-1)\n+Lambda >> Functions: empresa_9589_script_1\n+\n+\n ### SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n \n *INFORME VENTAS ANUAL*\n \n"}, {"date": 1726871710070, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -36,9 +36,9 @@\n Region: San Pablo (sa-east-1)\n Lambda >> Functions: empresa_9589_script_1\n \n \n-### SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n+## SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n \n *INFORME VENTAS ANUAL*\n \n SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n@@ -51,21 +51,8 @@\n WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n     AND tipofacturacion = 'electronico'\n ORDER BY fecha\n \n-*INFORME VENTAS PRECENCIAL*\n-\n-SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n-FROM ventas AS v\n-    JOIN datosxextras AS d ON d.idextraxmodulo = 21 AND v.idventa = d.idrelacion\n-    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n-    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n-    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n-    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n-WHERE fecha > '2024-08-01' AND fecha < '2024-09-01'\n-    AND d.idlistaxextra = 30\n-ORDER BY fecha\n-\n *EXPORTACIÓN PARA INFORMES OFFLINE*\n \n prod_saas\n cd 6149\n"}, {"date": 1727276479090, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,240 @@\n+# 📦 ROADS > SAAS > SOPORTE\n+-------------------------------------------------------------------------------\n+\n+\n+\n+-------------------------------------------------------------------\n+\n+## EMPRESAS EN BETA Y ALFA\n+\n+(98, 161, 2629)\n+(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664, 12281)\n+\n+\n+## PROBLEMAS ZUMBANDO\n+\n+### MAIL:\n+\n+Nombre de usuario de IAM: ses-smtp-user.20240702-164504\n+Nombre de usuario de SMTP: ********************\n+Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n+\n+NEW IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n+\n+EX IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n+\n+\n+### LAMDA:\n+\n+Acceso: https://saasargentina.signin.aws.amazon.com/console\n+User: empresa_9589_script_1\n+Password: qZbquV3&\n+Region: San Pablo (sa-east-1)\n+Lambda >> Functions: empresa_9589_script_1\n+\n+\n+## SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n+\n+*INFORME VENTAS ANUAL*\n+\n+SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n+FROM ventas AS v\n+    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n+    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n+    LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\n+    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n+    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n+WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n+    AND tipofacturacion = 'electronico'\n+ORDER BY fecha\n+\n+*EXPORTACIÓN PARA INFORMES OFFLINE*\n+\n+prod_saas\n+cd 6149\n+rm saas_6149_clientes.sql.gz\n+rm saas_6149_ventas.sql.gz\n+rm saas_6149_ivasxventas.sql.gz\n+rm saas_6149_tributosxventas.sql.gz\n+rm saas_6149_ventas_ml.sql.gz\n+rm saas_6149_datosxextras.sql.gz\n+\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 clientes | gzip -9 > saas_6149_clientes.sql.gz\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas --where=\"fecha > '2024-08-01'\" | gzip -9 > saas_6149_ventas.sql.gz\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ivasxventas | gzip -9 > saas_6149_ivasxventas.sql.gz\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 tributosxventas | gzip -9 > saas_6149_tributosxventas.sql.gz\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas_ml | gzip -9 > saas_6149_ventas_ml.sql.gz\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 datosxextras --where=\"idextraxmodulo IN (5, 12, 21)\" | gzip -9 > saas_6149_datosxextras.sql.gz\n+\n+exit\n+cd /home/<USER>/SOPORTE/6149\n+\n+rm saas_6149_clientes.sql\n+rm saas_6149_ventas.sql\n+rm saas_6149_ivasxventas.sql\n+rm saas_6149_tributosxventas.sql\n+rm saas_6149_ventas_ml.sql\n+rm saas_6149_datosxextras.sql\n+\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_clientes.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ventas.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ivasxventas.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_tributosxventas.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ventas_ml.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_datosxextras.sql.gz .\n+\n+gzip -d saas_6149_clientes.sql.gz\n+gzip -d saas_6149_ventas.sql.gz\n+gzip -d saas_6149_ivasxventas.sql.gz\n+gzip -d saas_6149_tributosxventas.sql.gz\n+gzip -d saas_6149_ventas_ml.sql.gz\n+gzip -d saas_6149_datosxextras.sql.gz\n+\n+sudo mysql -u root -p\n+\n+USE saas_6149;\n+TRUNCATE clientes;\n+TRUNCATE ventas;\n+TRUNCATE ivasxventas;\n+TRUNCATE tributosxventas;\n+TRUNCATE ventas_ml;\n+TRUNCATE datosxextras;\n+SOURCE saas_6149_ventas.sql;\n+SOURCE saas_6149_clientes.sql;\n+SOURCE saas_6149_ivasxventas.sql;\n+SOURCE saas_6149_tributosxventas.sql;\n+SOURCE saas_6149_ventas_ml.sql;\n+SOURCE saas_6149_datosxextras.sql;\n+UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n+\n+\n+### PARA VACIAR LOGS:\n+\n+- Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro\n+sudo su\n+du -h / | grep '[0-9\\.]\\+G' > espacio\n+cat espacio\n+\n+> alfa_ssl_access_log\n+> alfa_ssl_error_log\n+> api-alfa_ssl_access_log\n+> api-alfa_ssl_error_log\n+> api-beta_ssl_access_log\n+> api-beta_ssl_error_log\n+> api_ssl_access_log\n+> api_ssl_error_log\n+> app_ssl_access_log\n+> app_ssl_error_log\n+> beta_ssl_access_log\n+> beta_ssl_error_log\n+> informes-alfa_ssl_access_log\n+> informes-alfa_ssl_error_log\n+> informes-beta_ssl_access_log\n+> informes-beta_ssl_error_log\n+> informes_ssl_access_log\n+> informes_ssl_error_log\n+> login_ssl_access_log\n+> login_ssl_error_log\n+> scripts_ssl_access_log\n+> scripts_ssl_error_log\n+> www_ssl_access_log\n+> www_ssl_error_log\n+\n+\n+### LIMPIEZA\n+\n+- Desconectar de ML lo que hay en scripts/crontab/meli_eliminar.txt (sería ideal con un Lamda)\n+- Vaciar los archivos de archivos_eliminar.txt\n+También tengo un archivo con miles de 414\n+mrMpouB6CwYbxYzfX4dM\n+YAMbM5AUUXWbjsXiC3Gz\n+UeO2rCGc6RDvSwQwTj33\n+Gkf3NV2VdsbTMWyO8cHS\n+eegSb4ea6Nkn3HCETMiZ\n+SOZ5XZbrhJpiMZPnS8M2\n+UJ3PBMUghUYcfeatSmsC\n+zDitU7SXxOJ6sgxQXqQ9\n+mEBenx5EYfj8hHkcu84d\n+x4u37Dn9rrEiKRXdeEGJ\n+f2KFQ5DuQSAsiT87zDIi\n+9ry6R98Yh4TPirTF4J80\n+HyDtEy8HDjTxquYQYdbq\n+Abn7K2fmfh7z2SXdZU8M\n+aQjIEVpZNqFaX3NMPoFr\n+FsTyDjT8Dtu6mQIomJjg\n+Xk0mxVTMcPM3pxDwki6U\n+cNcgqiRkH6krEocjaknC\n+DudGFpZ365KNo9Kt5PYa\n+s6mpAAFGd2JibdSmqxKq\n+NVce5qjBo3TaY8Ykksex\n+aMEakHabEhTzIm06Ewnm\n+J0isamvJiwCYShT2u2HE\n+Pkdw5wDfgWG8zXDzS6vC\n+sNVBUxXog4Jz7jukKWre\n+\n+\n+### ALTA SUCURSAL GAMING CITY\n+- Sincronizar rubros manualmente\n+- Agregar la instancia a empresa_874_script_1.php\n+- Agregar la instancia a es_franquiciado\n+- Pasar la base de datos a BETA\n+- Forzar una sincronización completa con productos en updated_at = now()\n+- Activar 90 días de prueba\n+\n+\n+### CRONTAB\n+\n+Scripts de la empresa Nº 99 (SaaS Argentina):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 no debe ejecutarse hoy.\n+El script Nº 3 no debe ejecutarse hoy.\n+\n+Scripts de la empresa Nº 161 (Cronometraje Instantáneo):\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 182 (Cámara de Comercio, Turismo, Industria y Producción de Villa La Angostura):\n+El script Nº 2 no debe ejecutarse hoy.\n+\n+Scripts de la empresa Nº 1387 (Gezatek SRL):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 2142 (Gezatek SRL):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+---\n+\n+Scripts de la empresa Nº 4052 (Soy Gamer BELGRANO):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8802 (RUTIDEN SUC CIPOLLETTI):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8816 (Almacén del Bioquímico):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8918 (ROMEO BOXES):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 9589 (GAMING CITY ABASTO SHOPPING):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 9746 (GAMING CITY CABALLITO):\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 11360 (Ipanema Distribuciones):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+---\n+\n+Scripts de la empresa Nº 874 (Gaming-City Computacion):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 6149 (Duaitek SRL):\n+El script Nº 1 se ejecutó correctamente.\n"}, {"date": 1728421868202, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -93,9 +93,9 @@\n gzip -d saas_6149_tributosxventas.sql.gz\n gzip -d saas_6149_ventas_ml.sql.gz\n gzip -d saas_6149_datosxextras.sql.gz\n \n-sudo mysql -u root -p\n+db\n \n USE saas_6149;\n TRUNCATE clientes;\n TRUNCATE ventas;\n@@ -237,244 +237,4 @@\n El script Nº 2 se ejecutó correctamente.\n \n Scripts de la empresa Nº 6149 (Duaitek SRL):\n El script Nº 1 se ejecutó correctamente.\n-# 📦 ROADS > SAAS > SOPORTE\n--------------------------------------------------------------------------------\n-\n-\n-\n--------------------------------------------------------------------\n-\n-## EMPRESAS EN BETA Y ALFA\n-\n-(98, 161, 2629)\n-(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664, 12281)\n-\n-\n-## PROBLEMAS ZUMBANDO\n-\n-### MAIL:\n-\n-Nombre de usuario de IAM: ses-smtp-user.20240702-164504\n-Nombre de usuario de SMTP: ********************\n-Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n-\n-NEW IAM PARA SES\n-AWS_KEY: ********************\n-AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n-\n-EX IAM PARA SES\n-AWS_KEY: ********************\n-AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n-\n-\n-### LAMDA:\n-\n-Acceso: https://saasargentina.signin.aws.amazon.com/console\n-User: empresa_9589_script_1\n-Password: qZbquV3&\n-Region: San Pablo (sa-east-1)\n-Lambda >> Functions: empresa_9589_script_1\n-\n-\n-## SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n-\n-*INFORME VENTAS ANUAL*\n-\n-SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n-FROM ventas AS v\n-    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n-    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n-    LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\n-    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n-    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n-WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n-    AND tipofacturacion = 'electronico'\n-ORDER BY fecha\n-\n-*EXPORTACIÓN PARA INFORMES OFFLINE*\n-\n-prod_saas\n-cd 6149\n-rm saas_6149_clientes.sql.gz\n-rm saas_6149_ventas.sql.gz\n-rm saas_6149_ivasxventas.sql.gz\n-rm saas_6149_tributosxventas.sql.gz\n-rm saas_6149_ventas_ml.sql.gz\n-rm saas_6149_datosxextras.sql.gz\n-\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 clientes | gzip -9 > saas_6149_clientes.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas --where=\"fecha > '2024-08-01'\" | gzip -9 > saas_6149_ventas.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ivasxventas | gzip -9 > saas_6149_ivasxventas.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 tributosxventas | gzip -9 > saas_6149_tributosxventas.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas_ml | gzip -9 > saas_6149_ventas_ml.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 datosxextras --where=\"idextraxmodulo IN (5, 12, 21)\" | gzip -9 > saas_6149_datosxextras.sql.gz\n-\n-exit\n-cd /home/<USER>/SOPORTE/6149\n-\n-rm saas_6149_clientes.sql\n-rm saas_6149_ventas.sql\n-rm saas_6149_ivasxventas.sql\n-rm saas_6149_tributosxventas.sql\n-rm saas_6149_ventas_ml.sql\n-rm saas_6149_datosxextras.sql\n-\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_clientes.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ventas.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ivasxventas.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_tributosxventas.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ventas_ml.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_datosxextras.sql.gz .\n-\n-gzip -d saas_6149_clientes.sql.gz\n-gzip -d saas_6149_ventas.sql.gz\n-gzip -d saas_6149_ivasxventas.sql.gz\n-gzip -d saas_6149_tributosxventas.sql.gz\n-gzip -d saas_6149_ventas_ml.sql.gz\n-gzip -d saas_6149_datosxextras.sql.gz\n-\n-sudo mysql -u root -p\n-\n-USE saas_6149;\n-TRUNCATE clientes;\n-TRUNCATE ventas;\n-TRUNCATE ivasxventas;\n-TRUNCATE tributosxventas;\n-TRUNCATE ventas_ml;\n-TRUNCATE datosxextras;\n-SOURCE saas_6149_ventas.sql;\n-SOURCE saas_6149_clientes.sql;\n-SOURCE saas_6149_ivasxventas.sql;\n-SOURCE saas_6149_tributosxventas.sql;\n-SOURCE saas_6149_ventas_ml.sql;\n-SOURCE saas_6149_datosxextras.sql;\n-UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n-\n-\n-### PARA VACIAR LOGS:\n-\n-- Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro\n-sudo su\n-du -h / | grep '[0-9\\.]\\+G' > espacio\n-cat espacio\n-\n-> alfa_ssl_access_log\n-> alfa_ssl_error_log\n-> api-alfa_ssl_access_log\n-> api-alfa_ssl_error_log\n-> api-beta_ssl_access_log\n-> api-beta_ssl_error_log\n-> api_ssl_access_log\n-> api_ssl_error_log\n-> app_ssl_access_log\n-> app_ssl_error_log\n-> beta_ssl_access_log\n-> beta_ssl_error_log\n-> informes-alfa_ssl_access_log\n-> informes-alfa_ssl_error_log\n-> informes-beta_ssl_access_log\n-> informes-beta_ssl_error_log\n-> informes_ssl_access_log\n-> informes_ssl_error_log\n-> login_ssl_access_log\n-> login_ssl_error_log\n-> scripts_ssl_access_log\n-> scripts_ssl_error_log\n-> www_ssl_access_log\n-> www_ssl_error_log\n-\n-\n-### LIMPIEZA\n-\n-- Desconectar de ML lo que hay en scripts/crontab/meli_eliminar.txt (sería ideal con un Lamda)\n-- Vaciar los archivos de archivos_eliminar.txt\n-También tengo un archivo con miles de 414\n-mrMpouB6CwYbxYzfX4dM\n-YAMbM5AUUXWbjsXiC3Gz\n-UeO2rCGc6RDvSwQwTj33\n-Gkf3NV2VdsbTMWyO8cHS\n-eegSb4ea6Nkn3HCETMiZ\n-SOZ5XZbrhJpiMZPnS8M2\n-UJ3PBMUghUYcfeatSmsC\n-zDitU7SXxOJ6sgxQXqQ9\n-mEBenx5EYfj8hHkcu84d\n-x4u37Dn9rrEiKRXdeEGJ\n-f2KFQ5DuQSAsiT87zDIi\n-9ry6R98Yh4TPirTF4J80\n-HyDtEy8HDjTxquYQYdbq\n-Abn7K2fmfh7z2SXdZU8M\n-aQjIEVpZNqFaX3NMPoFr\n-FsTyDjT8Dtu6mQIomJjg\n-Xk0mxVTMcPM3pxDwki6U\n-cNcgqiRkH6krEocjaknC\n-DudGFpZ365KNo9Kt5PYa\n-s6mpAAFGd2JibdSmqxKq\n-NVce5qjBo3TaY8Ykksex\n-aMEakHabEhTzIm06Ewnm\n-J0isamvJiwCYShT2u2HE\n-Pkdw5wDfgWG8zXDzS6vC\n-sNVBUxXog4Jz7jukKWre\n-\n-\n-### ALTA SUCURSAL GAMING CITY\n-- Sincronizar rubros manualmente\n-- Agregar la instancia a empresa_874_script_1.php\n-- Agregar la instancia a es_franquiciado\n-- Pasar la base de datos a BETA\n-- Forzar una sincronización completa con productos en updated_at = now()\n-- Activar 90 días de prueba\n-\n-\n-### CRONTAB\n-\n-Scripts de la empresa Nº 99 (SaaS Argentina):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 no debe ejecutarse hoy.\n-El script Nº 3 no debe ejecutarse hoy.\n-\n-Scripts de la empresa Nº 161 (Cronometraje Instantáneo):\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 182 (Cámara de Comercio, Turismo, Industria y Producción de Villa La Angostura):\n-El script Nº 2 no debe ejecutarse hoy.\n-\n-Scripts de la empresa Nº 1387 (Gezatek SRL):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 2142 (Gezatek SRL):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n----\n-\n-Scripts de la empresa Nº 4052 (Soy Gamer BELGRANO):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8802 (RUTIDEN SUC CIPOLLETTI):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8816 (Almacén del Bioquímico):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8918 (ROMEO BOXES):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 9589 (GAMING CITY ABASTO SHOPPING):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 9746 (GAMING CITY CABALLITO):\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 11360 (Ipanema Distribuciones):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n----\n-\n-Scripts de la empresa Nº 874 (Gaming-City Computacion):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 6149 (Duaitek SRL):\n-El script Nº 1 se ejecutó correctamente.\n"}, {"date": 1728752362229, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,9 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n+SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_11576.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 3146);\n \n-\n -------------------------------------------------------------------\n \n ## EMPRESAS EN BETA Y ALFA\n \n"}, {"date": 1728918628616, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,12 @@\n -------------------------------------------------------------------------------\n \n SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_11576.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 3146);\n \n+UPDATE `ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc = 99;\n+UPDATE `ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc = 99;\n+\n+\n -------------------------------------------------------------------\n \n ## EMPRESAS EN BETA Y ALFA\n \n"}, {"date": 1728920061963, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,10 +2,10 @@\n -------------------------------------------------------------------------------\n \n SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_11576.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 3146);\n \n-UPDATE `ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc = 99;\n-UPDATE `ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc = 99;\n+UPDATE saas_7578.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n+UPDATE saas_7578.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n \n \n -------------------------------------------------------------------\n \n"}, {"date": 1728930723203, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,247 @@\n+# 📦 ROADS > SAAS > SOPORTE\n+-------------------------------------------------------------------------------\n+\n+SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_11576.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 3146);\n+\n+UPDATE saas_6149.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n+UPDATE saas_6149.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n+\n+UPDATE saas_6149.`clientes` SET `tipodoc` = '80' WHERE updated_at > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n+UPDATE saas_6149.`clientes` SET `tipodoc` = '96' WHERE updated_at > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n+\n+\n+-------------------------------------------------------------------\n+\n+## EMPRESAS EN BETA Y ALFA\n+\n+(98, 161, 2629)\n+(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664, 12281)\n+\n+\n+## PROBLEMAS ZUMBANDO\n+\n+### MAIL:\n+\n+Nombre de usuario de IAM: ses-smtp-user.20240702-164504\n+Nombre de usuario de SMTP: ********************\n+Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n+\n+NEW IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n+\n+EX IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n+\n+\n+### LAMDA:\n+\n+Acceso: https://saasargentina.signin.aws.amazon.com/console\n+User: empresa_9589_script_1\n+Password: qZbquV3&\n+Region: San Pablo (sa-east-1)\n+Lambda >> Functions: empresa_9589_script_1\n+\n+\n+## SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n+\n+*INFORME VENTAS ANUAL*\n+\n+SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n+FROM ventas AS v\n+    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n+    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n+    LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\n+    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n+    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n+WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n+    AND tipofacturacion = 'electronico'\n+ORDER BY fecha\n+\n+*EXPORTACIÓN PARA INFORMES OFFLINE*\n+\n+prod_saas\n+cd 6149\n+rm saas_6149_clientes.sql.gz\n+rm saas_6149_ventas.sql.gz\n+rm saas_6149_ivasxventas.sql.gz\n+rm saas_6149_tributosxventas.sql.gz\n+rm saas_6149_ventas_ml.sql.gz\n+rm saas_6149_datosxextras.sql.gz\n+\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 clientes | gzip -9 > saas_6149_clientes.sql.gz\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas --where=\"fecha > '2024-08-01'\" | gzip -9 > saas_6149_ventas.sql.gz\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ivasxventas | gzip -9 > saas_6149_ivasxventas.sql.gz\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 tributosxventas | gzip -9 > saas_6149_tributosxventas.sql.gz\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas_ml | gzip -9 > saas_6149_ventas_ml.sql.gz\n+mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 datosxextras --where=\"idextraxmodulo IN (5, 12, 21)\" | gzip -9 > saas_6149_datosxextras.sql.gz\n+\n+exit\n+cd /home/<USER>/SOPORTE/6149\n+\n+rm saas_6149_clientes.sql\n+rm saas_6149_ventas.sql\n+rm saas_6149_ivasxventas.sql\n+rm saas_6149_tributosxventas.sql\n+rm saas_6149_ventas_ml.sql\n+rm saas_6149_datosxextras.sql\n+\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_clientes.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ventas.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ivasxventas.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_tributosxventas.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ventas_ml.sql.gz .\n+scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_datosxextras.sql.gz .\n+\n+gzip -d saas_6149_clientes.sql.gz\n+gzip -d saas_6149_ventas.sql.gz\n+gzip -d saas_6149_ivasxventas.sql.gz\n+gzip -d saas_6149_tributosxventas.sql.gz\n+gzip -d saas_6149_ventas_ml.sql.gz\n+gzip -d saas_6149_datosxextras.sql.gz\n+\n+db\n+\n+USE saas_6149;\n+TRUNCATE clientes;\n+TRUNCATE ventas;\n+TRUNCATE ivasxventas;\n+TRUNCATE tributosxventas;\n+TRUNCATE ventas_ml;\n+TRUNCATE datosxextras;\n+SOURCE saas_6149_ventas.sql;\n+SOURCE saas_6149_clientes.sql;\n+SOURCE saas_6149_ivasxventas.sql;\n+SOURCE saas_6149_tributosxventas.sql;\n+SOURCE saas_6149_ventas_ml.sql;\n+SOURCE saas_6149_datosxextras.sql;\n+UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n+\n+\n+### PARA VACIAR LOGS:\n+\n+- Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro\n+sudo su\n+du -h / | grep '[0-9\\.]\\+G' > espacio\n+cat espacio\n+\n+> alfa_ssl_access_log\n+> alfa_ssl_error_log\n+> api-alfa_ssl_access_log\n+> api-alfa_ssl_error_log\n+> api-beta_ssl_access_log\n+> api-beta_ssl_error_log\n+> api_ssl_access_log\n+> api_ssl_error_log\n+> app_ssl_access_log\n+> app_ssl_error_log\n+> beta_ssl_access_log\n+> beta_ssl_error_log\n+> informes-alfa_ssl_access_log\n+> informes-alfa_ssl_error_log\n+> informes-beta_ssl_access_log\n+> informes-beta_ssl_error_log\n+> informes_ssl_access_log\n+> informes_ssl_error_log\n+> login_ssl_access_log\n+> login_ssl_error_log\n+> scripts_ssl_access_log\n+> scripts_ssl_error_log\n+> www_ssl_access_log\n+> www_ssl_error_log\n+\n+\n+### LIMPIEZA\n+\n+- Desconectar de ML lo que hay en scripts/crontab/meli_eliminar.txt (sería ideal con un Lamda)\n+- Vaciar los archivos de archivos_eliminar.txt\n+También tengo un archivo con miles de 414\n+mrMpouB6CwYbxYzfX4dM\n+YAMbM5AUUXWbjsXiC3Gz\n+UeO2rCGc6RDvSwQwTj33\n+Gkf3NV2VdsbTMWyO8cHS\n+eegSb4ea6Nkn3HCETMiZ\n+SOZ5XZbrhJpiMZPnS8M2\n+UJ3PBMUghUYcfeatSmsC\n+zDitU7SXxOJ6sgxQXqQ9\n+mEBenx5EYfj8hHkcu84d\n+x4u37Dn9rrEiKRXdeEGJ\n+f2KFQ5DuQSAsiT87zDIi\n+9ry6R98Yh4TPirTF4J80\n+HyDtEy8HDjTxquYQYdbq\n+Abn7K2fmfh7z2SXdZU8M\n+aQjIEVpZNqFaX3NMPoFr\n+FsTyDjT8Dtu6mQIomJjg\n+Xk0mxVTMcPM3pxDwki6U\n+cNcgqiRkH6krEocjaknC\n+DudGFpZ365KNo9Kt5PYa\n+s6mpAAFGd2JibdSmqxKq\n+NVce5qjBo3TaY8Ykksex\n+aMEakHabEhTzIm06Ewnm\n+J0isamvJiwCYShT2u2HE\n+Pkdw5wDfgWG8zXDzS6vC\n+sNVBUxXog4Jz7jukKWre\n+\n+\n+### ALTA SUCURSAL GAMING CITY\n+- Sincronizar rubros manualmente\n+- Agregar la instancia a empresa_874_script_1.php\n+- Agregar la instancia a es_franquiciado\n+- Pasar la base de datos a BETA\n+- Forzar una sincronización completa con productos en updated_at = now()\n+- Activar 90 días de prueba\n+\n+\n+### CRONTAB\n+\n+Scripts de la empresa Nº 99 (SaaS Argentina):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 no debe ejecutarse hoy.\n+El script Nº 3 no debe ejecutarse hoy.\n+\n+Scripts de la empresa Nº 161 (Cronometraje Instantáneo):\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 182 (Cámara de Comercio, Turismo, Industria y Producción de Villa La Angostura):\n+El script Nº 2 no debe ejecutarse hoy.\n+\n+Scripts de la empresa Nº 1387 (Gezatek SRL):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 2142 (Gezatek SRL):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+---\n+\n+Scripts de la empresa Nº 4052 (Soy Gamer BELGRANO):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8802 (RUTIDEN SUC CIPOLLETTI):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8816 (Almacén del Bioquímico):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8918 (ROMEO BOXES):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 9589 (GAMING CITY ABASTO SHOPPING):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 9746 (GAMING CITY CABALLITO):\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 11360 (Ipanema Distribuciones):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+---\n+\n+Scripts de la empresa Nº 874 (Gaming-City Computacion):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 6149 (Duaitek SRL):\n+El script Nº 1 se ejecutó correctamente.\n"}, {"date": 1728931112176, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,13 +2,13 @@\n -------------------------------------------------------------------------------\n \n SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_11576.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 3146);\n \n-UPDATE saas_6149.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n-UPDATE saas_6149.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n+UPDATE saas_5672.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n+UPDATE saas_5672.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n \n-UPDATE saas_6149.`clientes` SET `tipodoc` = '80' WHERE updated_at > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n-UPDATE saas_6149.`clientes` SET `tipodoc` = '96' WHERE updated_at > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n+UPDATE saas_5672.`clientes` SET `tipodoc` = '80' WHERE updated_at > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n+UPDATE saas_5672.`clientes` SET `tipodoc` = '96' WHERE updated_at > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n \n \n -------------------------------------------------------------------\n \n@@ -244,248 +244,4 @@\n El script Nº 2 se ejecutó correctamente.\n \n Scripts de la empresa Nº 6149 (Duaitek SRL):\n El script Nº 1 se ejecutó correctamente.\n-# 📦 ROADS > SAAS > SOPORTE\n--------------------------------------------------------------------------------\n-\n-SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_11576.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 3146);\n-\n-UPDATE saas_7578.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n-UPDATE saas_7578.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n-\n-\n--------------------------------------------------------------------\n-\n-## EMPRESAS EN BETA Y ALFA\n-\n-(98, 161, 2629)\n-(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664, 12281)\n-\n-\n-## PROBLEMAS ZUMBANDO\n-\n-### MAIL:\n-\n-Nombre de usuario de IAM: ses-smtp-user.20240702-164504\n-Nombre de usuario de SMTP: ********************\n-Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n-\n-NEW IAM PARA SES\n-AWS_KEY: ********************\n-AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n-\n-EX IAM PARA SES\n-AWS_KEY: ********************\n-AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n-\n-\n-### LAMDA:\n-\n-Acceso: https://saasargentina.signin.aws.amazon.com/console\n-User: empresa_9589_script_1\n-Password: qZbquV3&\n-Region: San Pablo (sa-east-1)\n-Lambda >> Functions: empresa_9589_script_1\n-\n-\n-## SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n-\n-*INFORME VENTAS ANUAL*\n-\n-SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n-FROM ventas AS v\n-    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n-    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n-    LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\n-    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n-    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n-WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n-    AND tipofacturacion = 'electronico'\n-ORDER BY fecha\n-\n-*EXPORTACIÓN PARA INFORMES OFFLINE*\n-\n-prod_saas\n-cd 6149\n-rm saas_6149_clientes.sql.gz\n-rm saas_6149_ventas.sql.gz\n-rm saas_6149_ivasxventas.sql.gz\n-rm saas_6149_tributosxventas.sql.gz\n-rm saas_6149_ventas_ml.sql.gz\n-rm saas_6149_datosxextras.sql.gz\n-\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 clientes | gzip -9 > saas_6149_clientes.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas --where=\"fecha > '2024-08-01'\" | gzip -9 > saas_6149_ventas.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ivasxventas | gzip -9 > saas_6149_ivasxventas.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 tributosxventas | gzip -9 > saas_6149_tributosxventas.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas_ml | gzip -9 > saas_6149_ventas_ml.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 datosxextras --where=\"idextraxmodulo IN (5, 12, 21)\" | gzip -9 > saas_6149_datosxextras.sql.gz\n-\n-exit\n-cd /home/<USER>/SOPORTE/6149\n-\n-rm saas_6149_clientes.sql\n-rm saas_6149_ventas.sql\n-rm saas_6149_ivasxventas.sql\n-rm saas_6149_tributosxventas.sql\n-rm saas_6149_ventas_ml.sql\n-rm saas_6149_datosxextras.sql\n-\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_clientes.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ventas.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ivasxventas.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_tributosxventas.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ventas_ml.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_datosxextras.sql.gz .\n-\n-gzip -d saas_6149_clientes.sql.gz\n-gzip -d saas_6149_ventas.sql.gz\n-gzip -d saas_6149_ivasxventas.sql.gz\n-gzip -d saas_6149_tributosxventas.sql.gz\n-gzip -d saas_6149_ventas_ml.sql.gz\n-gzip -d saas_6149_datosxextras.sql.gz\n-\n-db\n-\n-USE saas_6149;\n-TRUNCATE clientes;\n-TRUNCATE ventas;\n-TRUNCATE ivasxventas;\n-TRUNCATE tributosxventas;\n-TRUNCATE ventas_ml;\n-TRUNCATE datosxextras;\n-SOURCE saas_6149_ventas.sql;\n-SOURCE saas_6149_clientes.sql;\n-SOURCE saas_6149_ivasxventas.sql;\n-SOURCE saas_6149_tributosxventas.sql;\n-SOURCE saas_6149_ventas_ml.sql;\n-SOURCE saas_6149_datosxextras.sql;\n-UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n-\n-\n-### PARA VACIAR LOGS:\n-\n-- Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro\n-sudo su\n-du -h / | grep '[0-9\\.]\\+G' > espacio\n-cat espacio\n-\n-> alfa_ssl_access_log\n-> alfa_ssl_error_log\n-> api-alfa_ssl_access_log\n-> api-alfa_ssl_error_log\n-> api-beta_ssl_access_log\n-> api-beta_ssl_error_log\n-> api_ssl_access_log\n-> api_ssl_error_log\n-> app_ssl_access_log\n-> app_ssl_error_log\n-> beta_ssl_access_log\n-> beta_ssl_error_log\n-> informes-alfa_ssl_access_log\n-> informes-alfa_ssl_error_log\n-> informes-beta_ssl_access_log\n-> informes-beta_ssl_error_log\n-> informes_ssl_access_log\n-> informes_ssl_error_log\n-> login_ssl_access_log\n-> login_ssl_error_log\n-> scripts_ssl_access_log\n-> scripts_ssl_error_log\n-> www_ssl_access_log\n-> www_ssl_error_log\n-\n-\n-### LIMPIEZA\n-\n-- Desconectar de ML lo que hay en scripts/crontab/meli_eliminar.txt (sería ideal con un Lamda)\n-- Vaciar los archivos de archivos_eliminar.txt\n-También tengo un archivo con miles de 414\n-mrMpouB6CwYbxYzfX4dM\n-YAMbM5AUUXWbjsXiC3Gz\n-UeO2rCGc6RDvSwQwTj33\n-Gkf3NV2VdsbTMWyO8cHS\n-eegSb4ea6Nkn3HCETMiZ\n-SOZ5XZbrhJpiMZPnS8M2\n-UJ3PBMUghUYcfeatSmsC\n-zDitU7SXxOJ6sgxQXqQ9\n-mEBenx5EYfj8hHkcu84d\n-x4u37Dn9rrEiKRXdeEGJ\n-f2KFQ5DuQSAsiT87zDIi\n-9ry6R98Yh4TPirTF4J80\n-HyDtEy8HDjTxquYQYdbq\n-Abn7K2fmfh7z2SXdZU8M\n-aQjIEVpZNqFaX3NMPoFr\n-FsTyDjT8Dtu6mQIomJjg\n-Xk0mxVTMcPM3pxDwki6U\n-cNcgqiRkH6krEocjaknC\n-DudGFpZ365KNo9Kt5PYa\n-s6mpAAFGd2JibdSmqxKq\n-NVce5qjBo3TaY8Ykksex\n-aMEakHabEhTzIm06Ewnm\n-J0isamvJiwCYShT2u2HE\n-Pkdw5wDfgWG8zXDzS6vC\n-sNVBUxXog4Jz7jukKWre\n-\n-\n-### ALTA SUCURSAL GAMING CITY\n-- Sincronizar rubros manualmente\n-- Agregar la instancia a empresa_874_script_1.php\n-- Agregar la instancia a es_franquiciado\n-- Pasar la base de datos a BETA\n-- Forzar una sincronización completa con productos en updated_at = now()\n-- Activar 90 días de prueba\n-\n-\n-### CRONTAB\n-\n-Scripts de la empresa Nº 99 (SaaS Argentina):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 no debe ejecutarse hoy.\n-El script Nº 3 no debe ejecutarse hoy.\n-\n-Scripts de la empresa Nº 161 (Cronometraje Instantáneo):\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 182 (Cámara de Comercio, Turismo, Industria y Producción de Villa La Angostura):\n-El script Nº 2 no debe ejecutarse hoy.\n-\n-Scripts de la empresa Nº 1387 (Gezatek SRL):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 2142 (Gezatek SRL):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n----\n-\n-Scripts de la empresa Nº 4052 (Soy Gamer BELGRANO):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8802 (RUTIDEN SUC CIPOLLETTI):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8816 (Almacén del Bioquímico):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8918 (ROMEO BOXES):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 9589 (GAMING CITY ABASTO SHOPPING):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 9746 (GAMING CITY CABALLITO):\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 11360 (Ipanema Distribuciones):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n----\n-\n-Scripts de la empresa Nº 874 (Gaming-City Computacion):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 6149 (Duaitek SRL):\n-El script Nº 1 se ejecutó correctamente.\n"}, {"date": 1729001083330, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,10 +4,8 @@\n SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_11576.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 3146);\n \n UPDATE saas_5672.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n UPDATE saas_5672.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n-\n-UPDATE saas_5672.`clientes` SET `tipodoc` = '80' WHERE updated_at > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n UPDATE saas_5672.`clientes` SET `tipodoc` = '96' WHERE updated_at > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n \n \n -------------------------------------------------------------------\n"}, {"date": 1729294029156, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,9 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n+Ejecutar en PROD en unos días `DROP DATABASE saas_12348`\n+\n SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_11576.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 3146);\n \n UPDATE saas_5672.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n UPDATE saas_5672.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n"}, {"date": 1729437748737, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -185,12 +185,12 @@\n \n \n ### ALTA SUCURSAL GAMING CITY\n - Sincronizar rubros manualmente\n-- Agregar la instancia a empresa_874_script_1.php\n+- Agregar la instancia a empresa_874_script_2.php\n - Agregar la instancia a es_franquiciado\n - Pasar la base de datos a BETA\n-- Forzar una sincronización completa con productos en updated_at = now()\n+- Forzar una sincronización completa con `UPDATE saas_874.productos SET updated_at = NOW();` y `sudo php -f /saas/customer/services/scripts/crontab/crontab.php idempresas 874`\n - Activar 90 días de prueba\n \n \n ### CRONTAB\n"}, {"date": 1729776786895, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,9 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n-Ejecutar en PROD en unos días `DROP DATABASE saas_12348`\n+SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_9589.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 49287);\n \n-SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_11576.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 3146);\n-\n UPDATE saas_5672.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n UPDATE saas_5672.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n UPDATE saas_5672.`clientes` SET `tipodoc` = '96' WHERE updated_at > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n \n"}, {"date": 1729780711063, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,10 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n+SELECT REPLACE(obstienda, '&nbsp;', ' ') AS nueva_obstienda FROM saas_8802.productos;\n+UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp;', ' ');\n+\n SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_9589.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 49287);\n \n UPDATE saas_5672.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n UPDATE saas_5672.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n"}, {"date": 1729813658471, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,10 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n-SELECT REPLACE(obstienda, '&nbsp;', ' ') AS nueva_obstienda FROM saas_8802.productos;\n+SELECT idproducto, obstienda, REPLACE(obstienda, '&nbsp;', ' ') AS nueva_obstienda FROM saas_8802.productos;\n UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp;', ' ');\n+UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp', ' ');\n \n SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_9589.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 49287);\n \n UPDATE saas_5672.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n"}, {"date": 1730065883765, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,10 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n+EN PROD DROP DATABASE saas_12355;\n+\n+\n SELECT idproducto, obstienda, REPLACE(obstienda, '&nbsp;', ' ') AS nueva_obstienda FROM saas_8802.productos;\n UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp;', ' ');\n UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp', ' ');\n \n"}, {"date": 1730124133324, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -189,9 +189,9 @@\n sNVBUxXog4Jz7jukKWre\n \n \n ### ALTA SUCURSAL GAMING CITY\n-- Sincronizar rubros manualmente\n+- Sincronizar rubros y listas de precios manualmente\n - Agregar la instancia a empresa_874_script_2.php\n - Agregar la instancia a es_franquiciado\n - Pasar la base de datos a BETA\n - Forzar una sincronización completa con `UPDATE saas_874.productos SET updated_at = NOW();` y `sudo php -f /saas/customer/services/scripts/crontab/crontab.php idempresas 874`\n"}, {"date": 1730216953922, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,8 +7,13 @@\n SELECT idproducto, obstienda, REPLACE(obstienda, '&nbsp;', ' ') AS nueva_obstienda FROM saas_8802.productos;\n UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp;', ' ');\n UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp', ' ');\n \n+SELECT * FROM `wp_posts` WHERE post_content LIKE '%&nbsp;%'\n+\n+UPDATE wp_posts SET post_content = REPLACE(post_content, '&nbsp;', ' ');\n+UPDATE wp_posts SET post_content = REPLACE(post_content, '&nbsp', ' ');\n+\n SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_9589.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 49287);\n \n UPDATE saas_5672.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n UPDATE saas_5672.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n"}, {"date": 1730292848763, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,9 +12,9 @@\n \n UPDATE wp_posts SET post_content = REPLACE(post_content, '&nbsp;', ' ');\n UPDATE wp_posts SET post_content = REPLACE(post_content, '&nbsp', ' ');\n \n-SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_9589.ventas WHERE idventa IN (SELECT idventa FROM saas_11576.ventasxventas WHERE idrelacion = 49287);\n+SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_10129.ventas WHERE idventa IN (SELECT idventa FROM saas_10129.ventasxventas WHERE idrelacion = 1549);\n \n UPDATE saas_5672.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n UPDATE saas_5672.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n UPDATE saas_5672.`clientes` SET `tipodoc` = '96' WHERE updated_at > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n"}, {"date": 1730292885309, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +1,8 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n-EN PROD DROP DATABASE saas_12355;\n \n-\n SELECT idproducto, obstienda, REPLACE(obstienda, '&nbsp;', ' ') AS nueva_obstienda FROM saas_8802.productos;\n UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp;', ' ');\n UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp', ' ');\n \n"}, {"date": 1730463248386, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,11 +12,11 @@\n UPDATE wp_posts SET post_content = REPLACE(post_content, '&nbsp', ' ');\n \n SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_10129.ventas WHERE idventa IN (SELECT idventa FROM saas_10129.ventasxventas WHERE idrelacion = 1549);\n \n-UPDATE saas_5672.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-10-01' AND cuit != 0 AND tipodoc IN (0);\n-UPDATE saas_5672.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n-UPDATE saas_5672.`clientes` SET `tipodoc` = '96' WHERE updated_at > '2024-10-01' AND dni != 0 AND tipodoc IN (0);\n+UPDATE saas_99.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-11-01' AND cuit != 0 AND tipodoc IN (0, 99);\n+UPDATE saas_99.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-11-01' AND dni != 0 AND tipodoc IN (0, 99);\n+UPDATE saas_99.`clientes` SET `tipodoc` = '96' WHERE updated_at > '2024-11-01' AND dni != 0 AND tipodoc IN (0);\n \n \n -------------------------------------------------------------------\n \n"}, {"date": 1733772024832, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,21 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n+You are receiving this email because your Amazon CloudWatch Alarm \"SES Reputation BounceRate > 5%\" in the South America (Sao Paulo) region has entered the ALARM state, because \"Threshold Crossed: 1 out of the last 1 datapoints [0.10138648180242635 (09/12/24 18:23:00)] was greater than the threshold (0.1) (minimum 1 datapoint for OK -> ALARM transition).\" at \"Monday 09 December, 2024 18:28:10 UTC\".\n \n+View this alarm in the AWS Management Console:\n+https://sa-east-1.console.aws.amazon.com/cloudwatch/deeplink.js?region=sa-east-1#alarmsV2:alarm/SES%20Reputation%20BounceRate%20%3E%205%25\n+\n+Alarm Details:\n+- Name:                       SES Reputation BounceRate > 5%\n+- Description:               \n+- State Change:               INSUFFICIENT_DATA -> ALARM\n+- Reason for State Change:    Threshold Crossed: 1 out of the last 1 datapoints [0.10138648180242635 (09/12/24 18:23:00)] was greater than the threshold (0.1) (minimum 1 datapoint for OK -> ALARM transition).\n+- Timestamp:                  Monday 09 December, 2024 18:28:10 UTC\n+\n+\n+\n SELECT idproducto, obstienda, REPLACE(obstienda, '&nbsp;', ' ') AS nueva_obstienda FROM saas_8802.productos;\n UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp;', ' ');\n UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp', ' ');\n \n"}, {"date": 1733775322551, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,15 +7,21 @@\n https://sa-east-1.console.aws.amazon.com/cloudwatch/deeplink.js?region=sa-east-1#alarmsV2:alarm/SES%20Reputation%20BounceRate%20%3E%205%25\n \n Alarm Details:\n - Name:                       SES Reputation BounceRate > 5%\n-- Description:               \n+- Description:\n - State Change:               INSUFFICIENT_DATA -> ALARM\n - Reason for State Change:    Threshold Crossed: 1 out of the last 1 datapoints [0.10138648180242635 (09/12/24 18:23:00)] was greater than the threshold (0.1) (minimum 1 datapoint for OK -> ALARM transition).\n - Timestamp:                  Monday 09 December, 2024 18:28:10 UTC\n \n+187 1:12:42.759\n+198 por 195\n+dnf\n \n+20 como pos 23 con tiempo 1:36:35.785\n+14 y 21 intercambiar\n \n+\n SELECT idproducto, obstienda, REPLACE(obstienda, '&nbsp;', ' ') AS nueva_obstienda FROM saas_8802.productos;\n UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp;', ' ');\n UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp', ' ');\n \n"}, {"date": 1733961907679, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -88,47 +88,14 @@\n \n *EXPORTACIÓN PARA INFORMES OFFLINE*\n \n prod_saas\n-cd 6149\n-rm saas_6149_clientes.sql.gz\n-rm saas_6149_ventas.sql.gz\n-rm saas_6149_ivasxventas.sql.gz\n-rm saas_6149_tributosxventas.sql.gz\n-rm saas_6149_ventas_ml.sql.gz\n-rm saas_6149_datosxextras.sql.gz\n+./6149.sh\n+exit\n \n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 clientes | gzip -9 > saas_6149_clientes.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas --where=\"fecha > '2024-08-01'\" | gzip -9 > saas_6149_ventas.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ivasxventas | gzip -9 > saas_6149_ivasxventas.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 tributosxventas | gzip -9 > saas_6149_tributosxventas.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas_ml | gzip -9 > saas_6149_ventas_ml.sql.gz\n-mysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 datosxextras --where=\"idextraxmodulo IN (5, 12, 21)\" | gzip -9 > saas_6149_datosxextras.sql.gz\n-\n-exit\n cd /home/<USER>/SOPORTE/6149\n+./6149.sh\n \n-rm saas_6149_clientes.sql\n-rm saas_6149_ventas.sql\n-rm saas_6149_ivasxventas.sql\n-rm saas_6149_tributosxventas.sql\n-rm saas_6149_ventas_ml.sql\n-rm saas_6149_datosxextras.sql\n-\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_clientes.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ventas.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ivasxventas.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_tributosxventas.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_ventas_ml.sql.gz .\n-scp ec2-user@saas-prod:/home/<USER>/6149/saas_6149_datosxextras.sql.gz .\n-\n-gzip -d saas_6149_clientes.sql.gz\n-gzip -d saas_6149_ventas.sql.gz\n-gzip -d saas_6149_ivasxventas.sql.gz\n-gzip -d saas_6149_tributosxventas.sql.gz\n-gzip -d saas_6149_ventas_ml.sql.gz\n-gzip -d saas_6149_datosxextras.sql.gz\n-\n db\n \n USE saas_6149;\n TRUNCATE clientes;\n@@ -142,8 +109,9 @@\n SOURCE saas_6149_ivasxventas.sql;\n SOURCE saas_6149_tributosxventas.sql;\n SOURCE saas_6149_ventas_ml.sql;\n SOURCE saas_6149_datosxextras.sql;\n+SOURCE saas_6149_usuarios.sql;\n UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n \n \n ### PARA VACIAR LOGS:\n"}, {"date": 1734474167028, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,11 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n+UPDATE saas_10798.ventas SET estadocae = 'pendiente', obscae = '', tipodoc = 80 WHERE estadocae = 'rechazado' AND fecha > '2024-12-16' AND cuit > 0;\n+\n+\n+\n You are receiving this email because your Amazon CloudWatch Alarm \"SES Reputation BounceRate > 5%\" in the South America (Sao Paulo) region has entered the ALARM state, because \"Threshold Crossed: 1 out of the last 1 datapoints [0.10138648180242635 (09/12/24 18:23:00)] was greater than the threshold (0.1) (minimum 1 datapoint for OK -> ALARM transition).\" at \"Monday 09 December, 2024 18:28:10 UTC\".\n \n View this alarm in the AWS Management Console:\n https://sa-east-1.console.aws.amazon.com/cloudwatch/deeplink.js?region=sa-east-1#alarmsV2:alarm/SES%20Reputation%20BounceRate%20%3E%205%25\n"}, {"date": 1737409638789, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -124,32 +124,32 @@\n sudo su\n du -h / | grep '[0-9\\.]\\+G' > espacio\n cat espacio\n \n-> alfa_ssl_access_log\n-> alfa_ssl_error_log\n-> api-alfa_ssl_access_log\n-> api-alfa_ssl_error_log\n-> api-beta_ssl_access_log\n-> api-beta_ssl_error_log\n-> api_ssl_access_log\n-> api_ssl_error_log\n-> app_ssl_access_log\n-> app_ssl_error_log\n-> beta_ssl_access_log\n-> beta_ssl_error_log\n-> informes-alfa_ssl_access_log\n-> informes-alfa_ssl_error_log\n-> informes-beta_ssl_access_log\n-> informes-beta_ssl_error_log\n-> informes_ssl_access_log\n-> informes_ssl_error_log\n-> login_ssl_access_log\n-> login_ssl_error_log\n-> scripts_ssl_access_log\n-> scripts_ssl_error_log\n-> www_ssl_access_log\n-> www_ssl_error_log\n+echo '' > alfa_ssl_access_log\n+echo '' > alfa_ssl_error_log\n+echo '' > api-alfa_ssl_access_log\n+echo '' > api-alfa_ssl_error_log\n+echo '' > api-beta_ssl_access_log\n+echo '' > api-beta_ssl_error_log\n+echo '' > api_ssl_access_log\n+echo '' > api_ssl_error_log\n+echo '' > app_ssl_access_log\n+echo '' > app_ssl_error_log\n+echo '' > beta_ssl_access_log\n+echo '' > beta_ssl_error_log\n+echo '' > informes-alfa_ssl_access_log\n+echo '' > informes-alfa_ssl_error_log\n+echo '' > informes-beta_ssl_access_log\n+echo '' > informes-beta_ssl_error_log\n+echo '' > informes_ssl_access_log\n+echo '' > informes_ssl_error_log\n+echo '' > login_ssl_access_log\n+echo '' > login_ssl_error_log\n+echo '' > scripts_ssl_access_log\n+echo '' > scripts_ssl_error_log\n+echo '' > www_ssl_access_log\n+echo '' > www_ssl_error_log\n \n \n ### LIMPIEZA\n \n"}, {"date": 1737409849993, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -124,32 +124,33 @@\n sudo su\n du -h / | grep '[0-9\\.]\\+G' > espacio\n cat espacio\n \n-echo '' > alfa_ssl_access_log\n-echo '' > alfa_ssl_error_log\n-echo '' > api-alfa_ssl_access_log\n-echo '' > api-alfa_ssl_error_log\n-echo '' > api-beta_ssl_access_log\n-echo '' > api-beta_ssl_error_log\n-echo '' > api_ssl_access_log\n-echo '' > api_ssl_error_log\n-echo '' > app_ssl_access_log\n-echo '' > app_ssl_error_log\n-echo '' > beta_ssl_access_log\n-echo '' > beta_ssl_error_log\n-echo '' > informes-alfa_ssl_access_log\n-echo '' > informes-alfa_ssl_error_log\n-echo '' > informes-beta_ssl_access_log\n-echo '' > informes-beta_ssl_error_log\n-echo '' > informes_ssl_access_log\n-echo '' > informes_ssl_error_log\n-echo '' > login_ssl_access_log\n-echo '' > login_ssl_error_log\n-echo '' > scripts_ssl_access_log\n-echo '' > scripts_ssl_error_log\n-echo '' > www_ssl_access_log\n-echo '' > www_ssl_error_log\n+cd /saas/logs\n+> alfa_ssl_access_log\n+> alfa_ssl_error_log\n+> api-alfa_ssl_access_log\n+> api-alfa_ssl_error_log\n+> api-beta_ssl_access_log\n+> api-beta_ssl_error_log\n+> api_ssl_access_log\n+> api_ssl_error_log\n+> app_ssl_access_log\n+> app_ssl_error_log\n+> beta_ssl_access_log\n+> beta_ssl_error_log\n+> informes-alfa_ssl_access_log\n+> informes-alfa_ssl_error_log\n+> informes-beta_ssl_access_log\n+> informes-beta_ssl_error_log\n+> informes_ssl_access_log\n+> informes_ssl_error_log\n+> login_ssl_access_log\n+> login_ssl_error_log\n+> scripts_ssl_access_log\n+> scripts_ssl_error_log\n+> www_ssl_access_log\n+> www_ssl_error_log\n \n \n ### LIMPIEZA\n \n"}, {"date": 1737722245692, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,209 @@\n+# 📦 ROADS > SAAS > SOPORTE\n+-------------------------------------------------------------------------------\n+\n+\n+\n+-------------------------------------------------------------------\n+\n+## EMPRESAS EN BETA Y ALFA\n+\n+(98, 161, 2629)\n+(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664, 12281)\n+\n+\n+## PROBLEMAS ZUMBANDO\n+\n+### MAIL:\n+\n+Nombre de usuario de IAM: ses-smtp-user.20240702-164504\n+Nombre de usuario de SMTP: ********************\n+Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n+\n+NEW IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n+\n+EX IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n+\n+\n+### LAMDA:\n+\n+Acceso: https://saasargentina.signin.aws.amazon.com/console\n+User: empresa_9589_script_1\n+Password: qZbquV3&\n+Region: San Pablo (sa-east-1)\n+Lambda >> Functions: empresa_9589_script_1\n+\n+\n+## SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n+\n+*INFORME VENTAS ANUAL*\n+\n+SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n+FROM ventas AS v\n+    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n+    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n+    LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\n+    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n+    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n+WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n+    AND tipofacturacion = 'electronico'\n+ORDER BY fecha\n+\n+*EXPORTACIÓN PARA INFORMES OFFLINE*\n+\n+prod_saas\n+./6149.sh\n+exit\n+\n+cd /home/<USER>/SOPORTE/6149\n+./6149.sh\n+\n+db\n+\n+USE saas_6149;\n+TRUNCATE clientes;\n+TRUNCATE ventas;\n+TRUNCATE ivasxventas;\n+TRUNCATE tributosxventas;\n+TRUNCATE ventas_ml;\n+TRUNCATE datosxextras;\n+SOURCE saas_6149_ventas.sql;\n+SOURCE saas_6149_clientes.sql;\n+SOURCE saas_6149_ivasxventas.sql;\n+SOURCE saas_6149_tributosxventas.sql;\n+SOURCE saas_6149_ventas_ml.sql;\n+SOURCE saas_6149_datosxextras.sql;\n+SOURCE saas_6149_usuarios.sql;\n+UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n+\n+\n+### PARA VACIAR LOGS:\n+\n+- Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro\n+sudo su\n+du -h / | grep '[0-9\\.]\\+G' > espacio\n+cat espacio\n+\n+cd /saas/logs\n+> alfa_ssl_access_log\n+> alfa_ssl_error_log\n+> api-alfa_ssl_access_log\n+> api-alfa_ssl_error_log\n+> api-beta_ssl_access_log\n+> api-beta_ssl_error_log\n+> api_ssl_access_log\n+> api_ssl_error_log\n+> app_ssl_access_log\n+> app_ssl_error_log\n+> beta_ssl_access_log\n+> beta_ssl_error_log\n+> informes-alfa_ssl_access_log\n+> informes-alfa_ssl_error_log\n+> informes-beta_ssl_access_log\n+> informes-beta_ssl_error_log\n+> informes_ssl_access_log\n+> informes_ssl_error_log\n+> login_ssl_access_log\n+> login_ssl_error_log\n+> scripts_ssl_access_log\n+> scripts_ssl_error_log\n+> www_ssl_access_log\n+> www_ssl_error_log\n+\n+\n+### LIMPIEZA\n+\n+- Desconectar de ML lo que hay en scripts/crontab/meli_eliminar.txt (sería ideal con un Lamda)\n+- Vaciar los archivos de archivos_eliminar.txt\n+También tengo un archivo con miles de 414\n+mrMpouB6CwYbxYzfX4dM\n+YAMbM5AUUXWbjsXiC3Gz\n+UeO2rCGc6RDvSwQwTj33\n+Gkf3NV2VdsbTMWyO8cHS\n+eegSb4ea6Nkn3HCETMiZ\n+SOZ5XZbrhJpiMZPnS8M2\n+UJ3PBMUghUYcfeatSmsC\n+zDitU7SXxOJ6sgxQXqQ9\n+mEBenx5EYfj8hHkcu84d\n+x4u37Dn9rrEiKRXdeEGJ\n+f2KFQ5DuQSAsiT87zDIi\n+9ry6R98Yh4TPirTF4J80\n+HyDtEy8HDjTxquYQYdbq\n+Abn7K2fmfh7z2SXdZU8M\n+aQjIEVpZNqFaX3NMPoFr\n+FsTyDjT8Dtu6mQIomJjg\n+Xk0mxVTMcPM3pxDwki6U\n+cNcgqiRkH6krEocjaknC\n+DudGFpZ365KNo9Kt5PYa\n+s6mpAAFGd2JibdSmqxKq\n+NVce5qjBo3TaY8Ykksex\n+aMEakHabEhTzIm06Ewnm\n+J0isamvJiwCYShT2u2HE\n+Pkdw5wDfgWG8zXDzS6vC\n+sNVBUxXog4Jz7jukKWre\n+\n+\n+### ALTA SUCURSAL GAMING CITY\n+- Sincronizar rubros y listas de precios manualmente\n+- Agregar la instancia a empresa_874_script_2.php\n+- Agregar la instancia a es_franquiciado\n+- Pasar la base de datos a BETA\n+- Forzar una sincronización completa con `UPDATE saas_874.productos SET updated_at = NOW();` y `sudo php -f /saas/customer/services/scripts/crontab/crontab.php idempresas 874`\n+- Activar 90 días de prueba\n+\n+\n+### CRONTAB\n+\n+Scripts de la empresa Nº 99 (SaaS Argentina):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 no debe ejecutarse hoy.\n+El script Nº 3 no debe ejecutarse hoy.\n+\n+Scripts de la empresa Nº 161 (Cronometraje Instantáneo):\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 182 (Cámara de Comercio, Turismo, Industria y Producción de Villa La Angostura):\n+El script Nº 2 no debe ejecutarse hoy.\n+\n+Scripts de la empresa Nº 1387 (Gezatek SRL):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 2142 (Gezatek SRL):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+---\n+\n+Scripts de la empresa Nº 4052 (Soy Gamer BELGRANO):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8802 (RUTIDEN SUC CIPOLLETTI):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8816 (Almacén del Bioquímico):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8918 (ROMEO BOXES):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 9589 (GAMING CITY ABASTO SHOPPING):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 9746 (GAMING CITY CABALLITO):\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 11360 (Ipanema Distribuciones):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+---\n+\n+Scripts de la empresa Nº 874 (Gaming-City Computacion):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 6149 (Duaitek SRL):\n+El script Nº 1 se ejecutó correctamente.\n"}, {"date": 1739385754959, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -51,255 +51,16 @@\n WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n     AND tipofacturacion = 'electronico'\n ORDER BY fecha\n \n-*EXPORTACIÓN PARA INFORMES OFFLINE*\n+*ARCHIVO DE COMBOS*\n \n-prod_saas\n-./6149.sh\n-exit\n+SELECT pxc.idcombo, pc.codigo AS codigo_combo, pc.nombre AS combo, pxc.idproducto, p.codigo, p.nombre AS producto, pxc.cantidad\n+FROM productosxcombos AS pxc\n+    LEFT JOIN productos AS pc ON pxc.idcombo = pc.idproducto\n+    LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto\n+ORDER BY pxc.idcombo\n \n-cd /home/<USER>/SOPORTE/6149\n-./6149.sh\n-\n-db\n-\n-USE saas_6149;\n-TRUNCATE clientes;\n-TRUNCATE ventas;\n-TRUNCATE ivasxventas;\n-TRUNCATE tributosxventas;\n-TRUNCATE ventas_ml;\n-TRUNCATE datosxextras;\n-SOURCE saas_6149_ventas.sql;\n-SOURCE saas_6149_clientes.sql;\n-SOURCE saas_6149_ivasxventas.sql;\n-SOURCE saas_6149_tributosxventas.sql;\n-SOURCE saas_6149_ventas_ml.sql;\n-SOURCE saas_6149_datosxextras.sql;\n-SOURCE saas_6149_usuarios.sql;\n-UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n-\n-\n-### PARA VACIAR LOGS:\n-\n-- Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro\n-sudo su\n-du -h / | grep '[0-9\\.]\\+G' > espacio\n-cat espacio\n-\n-cd /saas/logs\n-> alfa_ssl_access_log\n-> alfa_ssl_error_log\n-> api-alfa_ssl_access_log\n-> api-alfa_ssl_error_log\n-> api-beta_ssl_access_log\n-> api-beta_ssl_error_log\n-> api_ssl_access_log\n-> api_ssl_error_log\n-> app_ssl_access_log\n-> app_ssl_error_log\n-> beta_ssl_access_log\n-> beta_ssl_error_log\n-> informes-alfa_ssl_access_log\n-> informes-alfa_ssl_error_log\n-> informes-beta_ssl_access_log\n-> informes-beta_ssl_error_log\n-> informes_ssl_access_log\n-> informes_ssl_error_log\n-> login_ssl_access_log\n-> login_ssl_error_log\n-> scripts_ssl_access_log\n-> scripts_ssl_error_log\n-> www_ssl_access_log\n-> www_ssl_error_log\n-\n-\n-### LIMPIEZA\n-\n-- Desconectar de ML lo que hay en scripts/crontab/meli_eliminar.txt (sería ideal con un Lamda)\n-- Vaciar los archivos de archivos_eliminar.txt\n-También tengo un archivo con miles de 414\n-mrMpouB6CwYbxYzfX4dM\n-YAMbM5AUUXWbjsXiC3Gz\n-UeO2rCGc6RDvSwQwTj33\n-Gkf3NV2VdsbTMWyO8cHS\n-eegSb4ea6Nkn3HCETMiZ\n-SOZ5XZbrhJpiMZPnS8M2\n-UJ3PBMUghUYcfeatSmsC\n-zDitU7SXxOJ6sgxQXqQ9\n-mEBenx5EYfj8hHkcu84d\n-x4u37Dn9rrEiKRXdeEGJ\n-f2KFQ5DuQSAsiT87zDIi\n-9ry6R98Yh4TPirTF4J80\n-HyDtEy8HDjTxquYQYdbq\n-Abn7K2fmfh7z2SXdZU8M\n-aQjIEVpZNqFaX3NMPoFr\n-FsTyDjT8Dtu6mQIomJjg\n-Xk0mxVTMcPM3pxDwki6U\n-cNcgqiRkH6krEocjaknC\n-DudGFpZ365KNo9Kt5PYa\n-s6mpAAFGd2JibdSmqxKq\n-NVce5qjBo3TaY8Ykksex\n-aMEakHabEhTzIm06Ewnm\n-J0isamvJiwCYShT2u2HE\n-Pkdw5wDfgWG8zXDzS6vC\n-sNVBUxXog4Jz7jukKWre\n-\n-\n-### ALTA SUCURSAL GAMING CITY\n-- Sincronizar rubros y listas de precios manualmente\n-- Agregar la instancia a empresa_874_script_2.php\n-- Agregar la instancia a es_franquiciado\n-- Pasar la base de datos a BETA\n-- Forzar una sincronización completa con `UPDATE saas_874.productos SET updated_at = NOW();` y `sudo php -f /saas/customer/services/scripts/crontab/crontab.php idempresas 874`\n-- Activar 90 días de prueba\n-\n-\n-### CRONTAB\n-\n-Scripts de la empresa Nº 99 (SaaS Argentina):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 no debe ejecutarse hoy.\n-El script Nº 3 no debe ejecutarse hoy.\n-\n-Scripts de la empresa Nº 161 (Cronometraje Instantáneo):\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 182 (Cámara de Comercio, Turismo, Industria y Producción de Villa La Angostura):\n-El script Nº 2 no debe ejecutarse hoy.\n-\n-Scripts de la empresa Nº 1387 (Gezatek SRL):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 2142 (Gezatek SRL):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n----\n-\n-Scripts de la empresa Nº 4052 (Soy Gamer BELGRANO):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8802 (RUTIDEN SUC CIPOLLETTI):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8816 (Almacén del Bioquímico):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8918 (ROMEO BOXES):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 9589 (GAMING CITY ABASTO SHOPPING):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 9746 (GAMING CITY CABALLITO):\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 11360 (Ipanema Distribuciones):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n----\n-\n-Scripts de la empresa Nº 874 (Gaming-City Computacion):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 6149 (Duaitek SRL):\n-El script Nº 1 se ejecutó correctamente.\n-# 📦 ROADS > SAAS > SOPORTE\n--------------------------------------------------------------------------------\n-\n-UPDATE saas_10798.ventas SET estadocae = 'pendiente', obscae = '', tipodoc = 80 WHERE estadocae = 'rechazado' AND fecha > '2024-12-16' AND cuit > 0;\n-\n-\n-\n-You are receiving this email because your Amazon CloudWatch Alarm \"SES Reputation BounceRate > 5%\" in the South America (Sao Paulo) region has entered the ALARM state, because \"Threshold Crossed: 1 out of the last 1 datapoints [0.10138648180242635 (09/12/24 18:23:00)] was greater than the threshold (0.1) (minimum 1 datapoint for OK -> ALARM transition).\" at \"Monday 09 December, 2024 18:28:10 UTC\".\n-\n-View this alarm in the AWS Management Console:\n-https://sa-east-1.console.aws.amazon.com/cloudwatch/deeplink.js?region=sa-east-1#alarmsV2:alarm/SES%20Reputation%20BounceRate%20%3E%205%25\n-\n-Alarm Details:\n-- Name:                       SES Reputation BounceRate > 5%\n-- Description:\n-- State Change:               INSUFFICIENT_DATA -> ALARM\n-- Reason for State Change:    Threshold Crossed: 1 out of the last 1 datapoints [0.10138648180242635 (09/12/24 18:23:00)] was greater than the threshold (0.1) (minimum 1 datapoint for OK -> ALARM transition).\n-- Timestamp:                  Monday 09 December, 2024 18:28:10 UTC\n-\n-187 1:12:42.759\n-198 por 195\n-dnf\n-\n-20 como pos 23 con tiempo 1:36:35.785\n-14 y 21 intercambiar\n-\n-\n-SELECT idproducto, obstienda, REPLACE(obstienda, '&nbsp;', ' ') AS nueva_obstienda FROM saas_8802.productos;\n-UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp;', ' ');\n-UPDATE saas_8802.productos SET obstienda = REPLACE(obstienda, '&nbsp', ' ');\n-\n-SELECT * FROM `wp_posts` WHERE post_content LIKE '%&nbsp;%'\n-\n-UPDATE wp_posts SET post_content = REPLACE(post_content, '&nbsp;', ' ');\n-UPDATE wp_posts SET post_content = REPLACE(post_content, '&nbsp', ' ');\n-\n-SELECT idventa, fecha, cuit, dni, tipodoc FROM saas_10129.ventas WHERE idventa IN (SELECT idventa FROM saas_10129.ventasxventas WHERE idrelacion = 1549);\n-\n-UPDATE saas_99.`ventas` SET `tipodoc` = '80' WHERE fecha > '2024-11-01' AND cuit != 0 AND tipodoc IN (0, 99);\n-UPDATE saas_99.`ventas` SET `tipodoc` = '96' WHERE fecha > '2024-11-01' AND dni != 0 AND tipodoc IN (0, 99);\n-UPDATE saas_99.`clientes` SET `tipodoc` = '96' WHERE updated_at > '2024-11-01' AND dni != 0 AND tipodoc IN (0);\n-\n-\n--------------------------------------------------------------------\n-\n-## EMPRESAS EN BETA Y ALFA\n-\n-(98, 161, 2629)\n-(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664, 12281)\n-\n-\n-## PROBLEMAS ZUMBANDO\n-\n-### MAIL:\n-\n-Nombre de usuario de IAM: ses-smtp-user.20240702-164504\n-Nombre de usuario de SMTP: ********************\n-Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n-\n-NEW IAM PARA SES\n-AWS_KEY: ********************\n-AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n-\n-EX IAM PARA SES\n-AWS_KEY: ********************\n-AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n-\n-\n-### LAMDA:\n-\n-Acceso: https://saasargentina.signin.aws.amazon.com/console\n-User: empresa_9589_script_1\n-Password: qZbquV3&\n-Region: San Pablo (sa-east-1)\n-Lambda >> Functions: empresa_9589_script_1\n-\n-\n-## SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n-\n-*INFORME VENTAS ANUAL*\n-\n-SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n-FROM ventas AS v\n-    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n-    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n-    LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\n-    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n-    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n-WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n-    AND tipofacturacion = 'electronico'\n-ORDER BY fecha\n-\n *EXPORTACIÓN PARA INFORMES OFFLINE*\n \n prod_saas\n ./6149.sh\n"}, {"date": 1739389556191, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -53,14 +53,20 @@\n ORDER BY fecha\n \n *ARCHIVO DE COMBOS*\n \n-SELECT pxc.idcombo, pc.codigo AS codigo_combo, pc.nombre AS combo, pxc.idproducto, p.codigo, p.nombre AS producto, pxc.cantidad\n+SELECT pxc.idcombo, c.codigo AS codigo_combo, c.nombre AS combo, pxc.idproducto, p.codigo, p.nombre AS producto, pxc.cantidad\n FROM productosxcombos AS pxc\n-    LEFT JOIN productos AS pc ON pxc.idcombo = pc.idproducto\n+    LEFT JOIN productos AS c ON pxc.idcombo = c.idproducto\n     LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto\n+WHERE c.combo = 1 AND c.mostrartienda = 1\n ORDER BY pxc.idcombo\n \n+SELECT COUNT(DISTINCT idcombo)\n+FROM productosxcombos AS pxc\n+    LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto\n+WHERE p.combo = 1 AND p.mostrartienda = 1\n+\n *EXPORTACIÓN PARA INFORMES OFFLINE*\n \n prod_saas\n ./6149.sh\n"}, {"date": 1740666474966, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,12 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n+- <PERSON><PERSON> de ayuda\n+- Ventasxmayor\n+- Ver <PERSON>\n+- <PERSON><PERSON>\n+- Generar issue por error de Uriel y los que tengo en DEV y poner como tarea\n \n \n -------------------------------------------------------------------\n \n"}, {"date": 1740681691410, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,9 +2,9 @@\n -------------------------------------------------------------------------------\n \n - Consultas de ayuda\n - Ventasxmayor\n-- Ver Lambda\n+\n - Image<PERSON>\n - Generar issue por error de Uriel y los que tengo en DEV y poner como tarea\n \n \n"}, {"date": 1740681715621, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,14 +1,9 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n-- Consul<PERSON> de ayuda\n-- Ventasxmayor\n \n-- Imagen Fernando\n-- Generar issue por error de Uriel y los que tengo en DEV y poner como tarea\n \n-\n -------------------------------------------------------------------\n \n ## EMPRESAS EN BETA Y ALFA\n \n"}, {"date": 1741876749635, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,231 @@\n+# 📦 ROADS > SAAS > SOPORTE\n+-------------------------------------------------------------------------------\n+\n+\n+\n+-------------------------------------------------------------------\n+\n+## EMPRESAS EN BETA Y ALFA\n+\n+(98, 161, 2629)\n+(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664, 12281)\n+\n+\n+## PROBLEMAS ZUMBANDO\n+\n+### MAIL:\n+\n+Nombre de usuario de IAM: ses-smtp-user.20240702-164504\n+Nombre de usuario de SMTP: ********************\n+Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n+\n+NEW IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n+\n+EX IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n+\n+\n+### LAMDA:\n+\n+Acceso: https://saasargentina.signin.aws.amazon.com/console\n+User: empresa_9589_script_1\n+Password: qZbquV3&\n+Region: San Pablo (sa-east-1)\n+Lambda >> Functions: empresa_9589_script_1\n+\n+\n+## SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n+\n+*INFORME VENTAS ANUAL*\n+\n+SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n+FROM ventas AS v\n+    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n+    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n+    LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\n+    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n+    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n+WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n+    AND tipofacturacion = 'electronico'\n+ORDER BY fecha\n+\n+*ARCHIVO DE COMBOS*\n+\n+SELECT pxc.idcombo, c.codigo AS codigo_combo, c.nombre AS combo, pxc.idproducto, p.codigo, p.nombre AS producto, pxc.cantidad\n+FROM productosxcombos AS pxc\n+    LEFT JOIN productos AS c ON pxc.idcombo = c.idproducto\n+    LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto\n+WHERE c.combo = 1 AND c.mostrartienda = 1\n+ORDER BY pxc.idcombo\n+\n+SELECT COUNT(DISTINCT idcombo)\n+FROM productosxcombos AS pxc\n+    LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto\n+WHERE p.combo = 1 AND p.mostrartienda = 1\n+\n+*EXPORTACIÓN PARA INFORMES OFFLINE*\n+\n+prod_saas\n+./6149.sh\n+exit\n+\n+cd /home/<USER>/SOPORTE/6149\n+./6149.sh\n+\n+db\n+\n+USE saas_6149;\n+TRUNCATE clientes;\n+TRUNCATE ventas;\n+TRUNCATE ivasxventas;\n+TRUNCATE tributosxventas;\n+TRUNCATE ventas_ml;\n+TRUNCATE datosxextras;\n+SOURCE saas_6149_ventas.sql;\n+SOURCE saas_6149_clientes.sql;\n+SOURCE saas_6149_ivasxventas.sql;\n+SOURCE saas_6149_tributosxventas.sql;\n+SOURCE saas_6149_ventas_ml.sql;\n+SOURCE saas_6149_datosxextras.sql;\n+SOURCE saas_6149_usuarios.sql;\n+UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n+\n+## Imagenes Gezatek\n+\n+SELECT idproducto, nombre, codigo,\n+CONCAT('https://s3-sa-east-1.amazonaws.com/saasargentina/', archivos.iua, '/imagen') AS imagen,\n+CONCAT('https://s3-sa-east-1.amazonaws.com/saasargentina/', archivos.iua, '/miniatura') AS miniatura\n+FROM productos INNER JOIN archivos ON archivos.modulo = 'productos' AND archivos.id = productos.idproducto\n+WHERE publico = 1 AND mostrartienda = 1\n+ORDER BY idproducto\n+\n+### PARA VACIAR LOGS:\n+\n+- Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro\n+sudo su\n+du -h / | grep '[0-9\\.]\\+G' > espacio\n+cat espacio\n+\n+cd /saas/logs\n+> alfa_ssl_access_log\n+> alfa_ssl_error_log\n+> api-alfa_ssl_access_log\n+> api-alfa_ssl_error_log\n+> api-beta_ssl_access_log\n+> api-beta_ssl_error_log\n+> api_ssl_access_log\n+> api_ssl_error_log\n+> app_ssl_access_log\n+> app_ssl_error_log\n+> beta_ssl_access_log\n+> beta_ssl_error_log\n+> informes-alfa_ssl_access_log\n+> informes-alfa_ssl_error_log\n+> informes-beta_ssl_access_log\n+> informes-beta_ssl_error_log\n+> informes_ssl_access_log\n+> informes_ssl_error_log\n+> login_ssl_access_log\n+> login_ssl_error_log\n+> scripts_ssl_access_log\n+> scripts_ssl_error_log\n+> www_ssl_access_log\n+> www_ssl_error_log\n+\n+\n+### LIMPIEZA\n+\n+- Desconectar de ML lo que hay en scripts/crontab/meli_eliminar.txt (sería ideal con un Lamda)\n+- Vaciar los archivos de archivos_eliminar.txt\n+También tengo un archivo con miles de 414\n+mrMpouB6CwYbxYzfX4dM\n+YAMbM5AUUXWbjsXiC3Gz\n+UeO2rCGc6RDvSwQwTj33\n+Gkf3NV2VdsbTMWyO8cHS\n+eegSb4ea6Nkn3HCETMiZ\n+SOZ5XZbrhJpiMZPnS8M2\n+UJ3PBMUghUYcfeatSmsC\n+zDitU7SXxOJ6sgxQXqQ9\n+mEBenx5EYfj8hHkcu84d\n+x4u37Dn9rrEiKRXdeEGJ\n+f2KFQ5DuQSAsiT87zDIi\n+9ry6R98Yh4TPirTF4J80\n+HyDtEy8HDjTxquYQYdbq\n+Abn7K2fmfh7z2SXdZU8M\n+aQjIEVpZNqFaX3NMPoFr\n+FsTyDjT8Dtu6mQIomJjg\n+Xk0mxVTMcPM3pxDwki6U\n+cNcgqiRkH6krEocjaknC\n+DudGFpZ365KNo9Kt5PYa\n+s6mpAAFGd2JibdSmqxKq\n+NVce5qjBo3TaY8Ykksex\n+aMEakHabEhTzIm06Ewnm\n+J0isamvJiwCYShT2u2HE\n+Pkdw5wDfgWG8zXDzS6vC\n+sNVBUxXog4Jz7jukKWre\n+\n+\n+### ALTA SUCURSAL GAMING CITY\n+- Sincronizar rubros y listas de precios manualmente\n+- Agregar la instancia a empresa_874_script_2.php\n+- Agregar la instancia a es_franquiciado\n+- Pasar la base de datos a BETA\n+- Forzar una sincronización completa con `UPDATE saas_874.productos SET updated_at = NOW();` y `sudo php -f /saas/customer/services/scripts/crontab/crontab.php idempresas 874`\n+- Activar 90 días de prueba\n+\n+\n+### CRONTAB\n+\n+Scripts de la empresa Nº 99 (SaaS Argentina):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 no debe ejecutarse hoy.\n+El script Nº 3 no debe ejecutarse hoy.\n+\n+Scripts de la empresa Nº 161 (Cronometraje Instantáneo):\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 182 (Cámara de Comercio, Turismo, Industria y Producción de Villa La Angostura):\n+El script Nº 2 no debe ejecutarse hoy.\n+\n+Scripts de la empresa Nº 1387 (Gezatek SRL):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 2142 (Gezatek SRL):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+---\n+\n+Scripts de la empresa Nº 4052 (Soy Gamer BELGRANO):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8802 (RUTIDEN SUC CIPOLLETTI):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8816 (Almacén del Bioquímico):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8918 (ROMEO BOXES):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 9589 (GAMING CITY ABASTO SHOPPING):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 9746 (GAMING CITY CABALLITO):\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 11360 (Ipanema Distribuciones):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+---\n+\n+Scripts de la empresa Nº 874 (Gaming-City Computacion):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 6149 (Duaitek SRL):\n+El script Nº 1 se ejecutó correctamente.\n"}, {"date": 1742218477714, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,13 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n+## EN UNOS DÍAS EJECUTAR:\n \n+En db DROP DATABASE saas_5007;\n+En db-beta DROP DATABASE saas_10798;\n \n+\n -------------------------------------------------------------------\n \n ## EMPRESAS EN BETA Y ALFA\n \n@@ -228,227 +232,4 @@\n El script Nº 2 se ejecutó correctamente.\n \n Scripts de la empresa Nº 6149 (Duaitek SRL):\n El script Nº 1 se ejecutó correctamente.\n-# 📦 ROADS > SAAS > SOPORTE\n--------------------------------------------------------------------------------\n-\n-\n-\n--------------------------------------------------------------------\n-\n-## EMPRESAS EN BETA Y ALFA\n-\n-(98, 161, 2629)\n-(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664, 12281)\n-\n-\n-## PROBLEMAS ZUMBANDO\n-\n-### MAIL:\n-\n-Nombre de usuario de IAM: ses-smtp-user.20240702-164504\n-Nombre de usuario de SMTP: ********************\n-Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n-\n-NEW IAM PARA SES\n-AWS_KEY: ********************\n-AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n-\n-EX IAM PARA SES\n-AWS_KEY: ********************\n-AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n-\n-\n-### LAMDA:\n-\n-Acceso: https://saasargentina.signin.aws.amazon.com/console\n-User: empresa_9589_script_1\n-Password: qZbquV3&\n-Region: San Pablo (sa-east-1)\n-Lambda >> Functions: empresa_9589_script_1\n-\n-\n-## SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n-\n-*INFORME VENTAS ANUAL*\n-\n-SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n-FROM ventas AS v\n-    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n-    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n-    LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\n-    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n-    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n-WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n-    AND tipofacturacion = 'electronico'\n-ORDER BY fecha\n-\n-*ARCHIVO DE COMBOS*\n-\n-SELECT pxc.idcombo, c.codigo AS codigo_combo, c.nombre AS combo, pxc.idproducto, p.codigo, p.nombre AS producto, pxc.cantidad\n-FROM productosxcombos AS pxc\n-    LEFT JOIN productos AS c ON pxc.idcombo = c.idproducto\n-    LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto\n-WHERE c.combo = 1 AND c.mostrartienda = 1\n-ORDER BY pxc.idcombo\n-\n-SELECT COUNT(DISTINCT idcombo)\n-FROM productosxcombos AS pxc\n-    LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto\n-WHERE p.combo = 1 AND p.mostrartienda = 1\n-\n-*EXPORTACIÓN PARA INFORMES OFFLINE*\n-\n-prod_saas\n-./6149.sh\n-exit\n-\n-cd /home/<USER>/SOPORTE/6149\n-./6149.sh\n-\n-db\n-\n-USE saas_6149;\n-TRUNCATE clientes;\n-TRUNCATE ventas;\n-TRUNCATE ivasxventas;\n-TRUNCATE tributosxventas;\n-TRUNCATE ventas_ml;\n-TRUNCATE datosxextras;\n-SOURCE saas_6149_ventas.sql;\n-SOURCE saas_6149_clientes.sql;\n-SOURCE saas_6149_ivasxventas.sql;\n-SOURCE saas_6149_tributosxventas.sql;\n-SOURCE saas_6149_ventas_ml.sql;\n-SOURCE saas_6149_datosxextras.sql;\n-SOURCE saas_6149_usuarios.sql;\n-UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n-\n-\n-### PARA VACIAR LOGS:\n-\n-- Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro\n-sudo su\n-du -h / | grep '[0-9\\.]\\+G' > espacio\n-cat espacio\n-\n-cd /saas/logs\n-> alfa_ssl_access_log\n-> alfa_ssl_error_log\n-> api-alfa_ssl_access_log\n-> api-alfa_ssl_error_log\n-> api-beta_ssl_access_log\n-> api-beta_ssl_error_log\n-> api_ssl_access_log\n-> api_ssl_error_log\n-> app_ssl_access_log\n-> app_ssl_error_log\n-> beta_ssl_access_log\n-> beta_ssl_error_log\n-> informes-alfa_ssl_access_log\n-> informes-alfa_ssl_error_log\n-> informes-beta_ssl_access_log\n-> informes-beta_ssl_error_log\n-> informes_ssl_access_log\n-> informes_ssl_error_log\n-> login_ssl_access_log\n-> login_ssl_error_log\n-> scripts_ssl_access_log\n-> scripts_ssl_error_log\n-> www_ssl_access_log\n-> www_ssl_error_log\n-\n-\n-### LIMPIEZA\n-\n-- Desconectar de ML lo que hay en scripts/crontab/meli_eliminar.txt (sería ideal con un Lamda)\n-- Vaciar los archivos de archivos_eliminar.txt\n-También tengo un archivo con miles de 414\n-mrMpouB6CwYbxYzfX4dM\n-YAMbM5AUUXWbjsXiC3Gz\n-UeO2rCGc6RDvSwQwTj33\n-Gkf3NV2VdsbTMWyO8cHS\n-eegSb4ea6Nkn3HCETMiZ\n-SOZ5XZbrhJpiMZPnS8M2\n-UJ3PBMUghUYcfeatSmsC\n-zDitU7SXxOJ6sgxQXqQ9\n-mEBenx5EYfj8hHkcu84d\n-x4u37Dn9rrEiKRXdeEGJ\n-f2KFQ5DuQSAsiT87zDIi\n-9ry6R98Yh4TPirTF4J80\n-HyDtEy8HDjTxquYQYdbq\n-Abn7K2fmfh7z2SXdZU8M\n-aQjIEVpZNqFaX3NMPoFr\n-FsTyDjT8Dtu6mQIomJjg\n-Xk0mxVTMcPM3pxDwki6U\n-cNcgqiRkH6krEocjaknC\n-DudGFpZ365KNo9Kt5PYa\n-s6mpAAFGd2JibdSmqxKq\n-NVce5qjBo3TaY8Ykksex\n-aMEakHabEhTzIm06Ewnm\n-J0isamvJiwCYShT2u2HE\n-Pkdw5wDfgWG8zXDzS6vC\n-sNVBUxXog4Jz7jukKWre\n-\n-\n-### ALTA SUCURSAL GAMING CITY\n-- Sincronizar rubros y listas de precios manualmente\n-- Agregar la instancia a empresa_874_script_2.php\n-- Agregar la instancia a es_franquiciado\n-- Pasar la base de datos a BETA\n-- Forzar una sincronización completa con `UPDATE saas_874.productos SET updated_at = NOW();` y `sudo php -f /saas/customer/services/scripts/crontab/crontab.php idempresas 874`\n-- Activar 90 días de prueba\n-\n-\n-### CRONTAB\n-\n-Scripts de la empresa Nº 99 (SaaS Argentina):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 no debe ejecutarse hoy.\n-El script Nº 3 no debe ejecutarse hoy.\n-\n-Scripts de la empresa Nº 161 (Cronometraje Instantáneo):\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 182 (Cámara de Comercio, Turismo, Industria y Producción de Villa La Angostura):\n-El script Nº 2 no debe ejecutarse hoy.\n-\n-Scripts de la empresa Nº 1387 (Gezatek SRL):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 2142 (Gezatek SRL):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n----\n-\n-Scripts de la empresa Nº 4052 (Soy Gamer BELGRANO):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8802 (RUTIDEN SUC CIPOLLETTI):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8816 (Almacén del Bioquímico):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8918 (ROMEO BOXES):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 9589 (GAMING CITY ABASTO SHOPPING):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 9746 (GAMING CITY CABALLITO):\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 11360 (Ipanema Distribuciones):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n----\n-\n-Scripts de la empresa Nº 874 (Gaming-City Computacion):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 6149 (Duaitek SRL):\n-El script Nº 1 se ejecutó correctamente.\n"}, {"date": 1743347446965, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,8 +8,16 @@\n \n \n -------------------------------------------------------------------\n \n+## ANTIAFIP NOTA DE CREDITO\n+\n+saas_3393\n+\n+SELECT idventa, fecha, estado, estadocae, numero, total, cae, cuit, dni, tipodoc, idcliente, vencimientocae FROM saas_3393.`ventas` WHERE idtipoventa = 18 AND numero > 1281 ORDER BY numero LIMIT 200;\n+\n+SELECT idventa, fecha, estado, estadocae, numero, total, cae, cuit, dni, tipodoc, idcliente, vencimientocae FROM saas_3393.`ventas` WHERE idventa IN (SELECT idventa FROM saas_3393.ventasxventas WHERE idrelacion = 86363);\n+\n ## EMPRESAS EN BETA Y ALFA\n \n (98, 161, 2629)\n (168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664, 12281)\n"}, {"date": 1743866670394, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,13 +1,9 @@\n # 📦 ROADS > SAAS > SOPORTE\n -------------------------------------------------------------------------------\n \n-## EN UNOS DÍAS EJECUTAR:\n \n-En db DROP DATABASE saas_5007;\n-En db-beta DROP DATABASE saas_10798;\n \n-\n -------------------------------------------------------------------\n \n ## ANTIAFIP NOTA DE CREDITO\n \n"}, {"date": 1743866693615, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,9 +15,9 @@\n \n ## EMPRESAS EN BETA Y ALFA\n \n (98, 161, 2629)\n-(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664, 12281)\n+(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5007, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12281)\n \n \n ## PROBLEMAS ZUMBANDO\n \n"}, {"date": 1743866856494, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,8 +12,9 @@\n SELECT idventa, fecha, estado, estadocae, numero, total, cae, cuit, dni, tipodoc, idcliente, vencimientocae FROM saas_3393.`ventas` WHERE idtipoventa = 18 AND numero > 1281 ORDER BY numero LIMIT 200;\n \n SELECT idventa, fecha, estado, estadocae, numero, total, cae, cuit, dni, tipodoc, idcliente, vencimientocae FROM saas_3393.`ventas` WHERE idventa IN (SELECT idventa FROM saas_3393.ventasxventas WHERE idrelacion = 86363);\n \n+\n ## EMPRESAS EN BETA Y ALFA\n \n (98, 161, 2629)\n (168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5007, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12281)\n"}, {"date": 1743866884412, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -101,17 +101,19 @@\n SOURCE saas_6149_datosxextras.sql;\n SOURCE saas_6149_usuarios.sql;\n UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n \n-## Imagenes Gezatek\n \n+## IMAGENES GEZATEK\n+\n SELECT idproducto, nombre, codigo,\n CONCAT('https://s3-sa-east-1.amazonaws.com/saasargentina/', archivos.iua, '/imagen') AS imagen,\n CONCAT('https://s3-sa-east-1.amazonaws.com/saasargentina/', archivos.iua, '/miniatura') AS miniatura\n FROM productos INNER JOIN archivos ON archivos.modulo = 'productos' AND archivos.id = productos.idproducto\n WHERE publico = 1 AND mostrartienda = 1\n ORDER BY idproducto\n \n+\n ### PARA VACIAR LOGS:\n \n - Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro\n sudo su\n"}, {"date": 1747307454099, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,243 @@\n+# 📦 ROADS > SAAS > SOPORTE\n+-------------------------------------------------------------------------------\n+\n+\n+\n+\n+-------------------------------------------------------------------------------\n+\n+## ANTIAFIP NOTA DE CREDITO\n+\n+saas_3393\n+\n+SELECT idventa, fecha, estado, estadocae, numero, total, cae, cuit, dni, tipodoc, idcliente, vencimientocae FROM saas_3393.`ventas` WHERE idtipoventa = 18 AND numero > 1281 ORDER BY numero LIMIT 200;\n+\n+SELECT idventa, fecha, estado, estadocae, numero, total, cae, cuit, dni, tipodoc, idcliente, vencimientocae FROM saas_3393.`ventas` WHERE idventa IN (SELECT idventa FROM saas_3393.ventasxventas WHERE idrelacion = 86363);\n+\n+\n+## EMPRESAS EN BETA Y ALFA\n+\n+(98, 161, 2629)\n+(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5007, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12281)\n+\n+\n+## PROBLEMAS ZUMBANDO\n+\n+### MAIL:\n+\n+Nombre de usuario de IAM: ses-smtp-user.20240702-164504\n+Nombre de usuario de SMTP: ********************\n+Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n+\n+NEW IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n+\n+EX IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n+\n+\n+### LAMDA:\n+\n+Acceso: https://saasargentina.signin.aws.amazon.com/console\n+User: empresa_9589_script_1\n+Password: qZbquV3&\n+Region: San Pablo (sa-east-1)\n+Lambda >> Functions: empresa_9589_script_1\n+\n+\n+## SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n+\n+*INFORME VENTAS ANUAL*\n+\n+SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n+FROM ventas AS v\n+    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n+    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n+    LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\n+    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n+    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n+WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n+    AND tipofacturacion = 'electronico'\n+ORDER BY fecha\n+\n+*ARCHIVO DE COMBOS*\n+\n+SELECT pxc.idcombo, c.codigo AS codigo_combo, c.nombre AS combo, pxc.idproducto, p.codigo, p.nombre AS producto, pxc.cantidad\n+FROM productosxcombos AS pxc\n+    LEFT JOIN productos AS c ON pxc.idcombo = c.idproducto\n+    LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto\n+WHERE c.combo = 1 AND c.mostrartienda = 1\n+ORDER BY pxc.idcombo\n+\n+SELECT COUNT(DISTINCT idcombo)\n+FROM productosxcombos AS pxc\n+    LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto\n+WHERE p.combo = 1 AND p.mostrartienda = 1\n+\n+*EXPORTACIÓN PARA INFORMES OFFLINE*\n+\n+prod_saas\n+./6149.sh\n+exit\n+\n+cd /home/<USER>/SOPORTE/6149\n+./6149.sh\n+\n+db\n+\n+USE saas_6149;\n+TRUNCATE clientes;\n+TRUNCATE ventas;\n+TRUNCATE ivasxventas;\n+TRUNCATE tributosxventas;\n+TRUNCATE ventas_ml;\n+TRUNCATE datosxextras;\n+SOURCE saas_6149_ventas.sql;\n+SOURCE saas_6149_clientes.sql;\n+SOURCE saas_6149_ivasxventas.sql;\n+SOURCE saas_6149_tributosxventas.sql;\n+SOURCE saas_6149_ventas_ml.sql;\n+SOURCE saas_6149_datosxextras.sql;\n+SOURCE saas_6149_usuarios.sql;\n+UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n+\n+\n+## IMAGENES GEZATEK\n+\n+SELECT idproducto, nombre, codigo,\n+CONCAT('https://s3-sa-east-1.amazonaws.com/saasargentina/', archivos.iua, '/imagen') AS imagen,\n+CONCAT('https://s3-sa-east-1.amazonaws.com/saasargentina/', archivos.iua, '/miniatura') AS miniatura\n+FROM productos INNER JOIN archivos ON archivos.modulo = 'productos' AND archivos.id = productos.idproducto\n+WHERE publico = 1 AND mostrartienda = 1\n+ORDER BY idproducto\n+\n+\n+### PARA VACIAR LOGS:\n+\n+- Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro\n+sudo su\n+du -h / | grep '[0-9\\.]\\+G' > espacio\n+cat espacio\n+\n+cd /saas/logs\n+> alfa_ssl_access_log\n+> alfa_ssl_error_log\n+> api-alfa_ssl_access_log\n+> api-alfa_ssl_error_log\n+> api-beta_ssl_access_log\n+> api-beta_ssl_error_log\n+> api_ssl_access_log\n+> api_ssl_error_log\n+> app_ssl_access_log\n+> app_ssl_error_log\n+> beta_ssl_access_log\n+> beta_ssl_error_log\n+> informes-alfa_ssl_access_log\n+> informes-alfa_ssl_error_log\n+> informes-beta_ssl_access_log\n+> informes-beta_ssl_error_log\n+> informes_ssl_access_log\n+> informes_ssl_error_log\n+> login_ssl_access_log\n+> login_ssl_error_log\n+> scripts_ssl_access_log\n+> scripts_ssl_error_log\n+> www_ssl_access_log\n+> www_ssl_error_log\n+\n+\n+### LIMPIEZA\n+\n+- Desconectar de ML lo que hay en scripts/crontab/meli_eliminar.txt (sería ideal con un Lamda)\n+- Vaciar los archivos de archivos_eliminar.txt\n+También tengo un archivo con miles de 414\n+mrMpouB6CwYbxYzfX4dM\n+YAMbM5AUUXWbjsXiC3Gz\n+UeO2rCGc6RDvSwQwTj33\n+Gkf3NV2VdsbTMWyO8cHS\n+eegSb4ea6Nkn3HCETMiZ\n+SOZ5XZbrhJpiMZPnS8M2\n+UJ3PBMUghUYcfeatSmsC\n+zDitU7SXxOJ6sgxQXqQ9\n+mEBenx5EYfj8hHkcu84d\n+x4u37Dn9rrEiKRXdeEGJ\n+f2KFQ5DuQSAsiT87zDIi\n+9ry6R98Yh4TPirTF4J80\n+HyDtEy8HDjTxquYQYdbq\n+Abn7K2fmfh7z2SXdZU8M\n+aQjIEVpZNqFaX3NMPoFr\n+FsTyDjT8Dtu6mQIomJjg\n+Xk0mxVTMcPM3pxDwki6U\n+cNcgqiRkH6krEocjaknC\n+DudGFpZ365KNo9Kt5PYa\n+s6mpAAFGd2JibdSmqxKq\n+NVce5qjBo3TaY8Ykksex\n+aMEakHabEhTzIm06Ewnm\n+J0isamvJiwCYShT2u2HE\n+Pkdw5wDfgWG8zXDzS6vC\n+sNVBUxXog4Jz7jukKWre\n+\n+\n+### ALTA SUCURSAL GAMING CITY\n+- Sincronizar rubros y listas de precios manualmente\n+- Agregar la instancia a empresa_874_script_2.php\n+- Agregar la instancia a es_franquiciado\n+- Pasar la base de datos a BETA\n+- Forzar una sincronización completa con `UPDATE saas_874.productos SET updated_at = NOW();` y `sudo php -f /saas/customer/services/scripts/crontab/crontab.php idempresas 874`\n+- Activar 90 días de prueba\n+\n+\n+### CRONTAB\n+\n+Scripts de la empresa Nº 99 (SaaS Argentina):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 no debe ejecutarse hoy.\n+El script Nº 3 no debe ejecutarse hoy.\n+\n+Scripts de la empresa Nº 161 (Cronometraje Instantáneo):\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 182 (Cámara de Comercio, Turismo, Industria y Producción de Villa La Angostura):\n+El script Nº 2 no debe ejecutarse hoy.\n+\n+Scripts de la empresa Nº 1387 (Gezatek SRL):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 2142 (Gezatek SRL):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+---\n+\n+Scripts de la empresa Nº 4052 (Soy Gamer BELGRANO):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8802 (RUTIDEN SUC CIPOLLETTI):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8816 (Almacén del Bioquímico):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 8918 (ROMEO BOXES):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 9589 (GAMING CITY ABASTO SHOPPING):\n+El script Nº 1 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 9746 (GAMING CITY CABALLITO):\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 11360 (Ipanema Distribuciones):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+---\n+\n+Scripts de la empresa Nº 874 (Gaming-City Computacion):\n+El script Nº 1 se ejecutó correctamente.\n+El script Nº 2 se ejecutó correctamente.\n+\n+Scripts de la empresa Nº 6149 (Duaitek SRL):\n+El script Nº 1 se ejecutó correctamente.\n"}, {"date": 1747308076464, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -48,37 +48,17 @@\n \n \n ## SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n \n-*INFORME VENTAS ANUAL*\n+**EXPORTACIÓN PARA INFORMES OFFLINE**\n \n-SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n-FROM ventas AS v\n-    LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n-    LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n-    LEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\n-    LEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\n-    LEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\n-WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n-    AND tipofacturacion = 'electronico'\n-ORDER BY fecha\n+- Mandar con <NAME_EMAIL>\n+- Exportar desde Chrome\n+- Sacar el memory_limit\n+- Generar CSVs y guardarlos a XLSX\n+- Informe de Comprobantes emitidos (con Usuario, Provincia, CUIT y Datos ML y Datos Extras Ventas)\n+- Subdiario (sólo los impuestos de Misiones, Provincias, No Grabado, 10,5 y 21 de IVA)\n \n-*ARCHIVO DE COMBOS*\n-\n-SELECT pxc.idcombo, c.codigo AS codigo_combo, c.nombre AS combo, pxc.idproducto, p.codigo, p.nombre AS producto, pxc.cantidad\n-FROM productosxcombos AS pxc\n-    LEFT JOIN productos AS c ON pxc.idcombo = c.idproducto\n-    LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto\n-WHERE c.combo = 1 AND c.mostrartienda = 1\n-ORDER BY pxc.idcombo\n-\n-SELECT COUNT(DISTINCT idcombo)\n-FROM productosxcombos AS pxc\n-    LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto\n-WHERE p.combo = 1 AND p.mostrartienda = 1\n-\n-*EXPORTACIÓN PARA INFORMES OFFLINE*\n-\n prod_saas\n ./6149.sh\n exit\n \n@@ -102,198 +82,10 @@\n SOURCE saas_6149_datosxextras.sql;\n SOURCE saas_6149_usuarios.sql;\n UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n \n+**INFORME VENTAS ANUAL**\n \n-## IMAGENES GEZATEK\n-\n-SELECT idproducto, nombre, codigo,\n-CONCAT('https://s3-sa-east-1.amazonaws.com/saasargentina/', archivos.iua, '/imagen') AS imagen,\n-CONCAT('https://s3-sa-east-1.amazonaws.com/saasargentina/', archivos.iua, '/miniatura') AS miniatura\n-FROM productos INNER JOIN archivos ON archivos.modulo = 'productos' AND archivos.id = productos.idproducto\n-WHERE publico = 1 AND mostrartienda = 1\n-ORDER BY idproducto\n-\n-\n-### PARA VACIAR LOGS:\n-\n-- Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro\n-sudo su\n-du -h / | grep '[0-9\\.]\\+G' > espacio\n-cat espacio\n-\n-cd /saas/logs\n-> alfa_ssl_access_log\n-> alfa_ssl_error_log\n-> api-alfa_ssl_access_log\n-> api-alfa_ssl_error_log\n-> api-beta_ssl_access_log\n-> api-beta_ssl_error_log\n-> api_ssl_access_log\n-> api_ssl_error_log\n-> app_ssl_access_log\n-> app_ssl_error_log\n-> beta_ssl_access_log\n-> beta_ssl_error_log\n-> informes-alfa_ssl_access_log\n-> informes-alfa_ssl_error_log\n-> informes-beta_ssl_access_log\n-> informes-beta_ssl_error_log\n-> informes_ssl_access_log\n-> informes_ssl_error_log\n-> login_ssl_access_log\n-> login_ssl_error_log\n-> scripts_ssl_access_log\n-> scripts_ssl_error_log\n-> www_ssl_access_log\n-> www_ssl_error_log\n-\n-\n-### LIMPIEZA\n-\n-- Desconectar de ML lo que hay en scripts/crontab/meli_eliminar.txt (sería ideal con un Lamda)\n-- Vaciar los archivos de archivos_eliminar.txt\n-También tengo un archivo con miles de 414\n-mrMpouB6CwYbxYzfX4dM\n-YAMbM5AUUXWbjsXiC3Gz\n-UeO2rCGc6RDvSwQwTj33\n-Gkf3NV2VdsbTMWyO8cHS\n-eegSb4ea6Nkn3HCETMiZ\n-SOZ5XZbrhJpiMZPnS8M2\n-UJ3PBMUghUYcfeatSmsC\n-zDitU7SXxOJ6sgxQXqQ9\n-mEBenx5EYfj8hHkcu84d\n-x4u37Dn9rrEiKRXdeEGJ\n-f2KFQ5DuQSAsiT87zDIi\n-9ry6R98Yh4TPirTF4J80\n-HyDtEy8HDjTxquYQYdbq\n-Abn7K2fmfh7z2SXdZU8M\n-aQjIEVpZNqFaX3NMPoFr\n-FsTyDjT8Dtu6mQIomJjg\n-Xk0mxVTMcPM3pxDwki6U\n-cNcgqiRkH6krEocjaknC\n-DudGFpZ365KNo9Kt5PYa\n-s6mpAAFGd2JibdSmqxKq\n-NVce5qjBo3TaY8Ykksex\n-aMEakHabEhTzIm06Ewnm\n-J0isamvJiwCYShT2u2HE\n-Pkdw5wDfgWG8zXDzS6vC\n-sNVBUxXog4Jz7jukKWre\n-\n-\n-### ALTA SUCURSAL GAMING CITY\n-- Sincronizar rubros y listas de precios manualmente\n-- Agregar la instancia a empresa_874_script_2.php\n-- Agregar la instancia a es_franquiciado\n-- Pasar la base de datos a BETA\n-- Forzar una sincronización completa con `UPDATE saas_874.productos SET updated_at = NOW();` y `sudo php -f /saas/customer/services/scripts/crontab/crontab.php idempresas 874`\n-- Activar 90 días de prueba\n-\n-\n-### CRONTAB\n-\n-Scripts de la empresa Nº 99 (SaaS Argentina):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 no debe ejecutarse hoy.\n-El script Nº 3 no debe ejecutarse hoy.\n-\n-Scripts de la empresa Nº 161 (Cronometraje Instantáneo):\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 182 (Cámara de Comercio, Turismo, Industria y Producción de Villa La Angostura):\n-El script Nº 2 no debe ejecutarse hoy.\n-\n-Scripts de la empresa Nº 1387 (Gezatek SRL):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 2142 (Gezatek SRL):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n----\n-\n-Scripts de la empresa Nº 4052 (Soy Gamer BELGRANO):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8802 (RUTIDEN SUC CIPOLLETTI):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8816 (Almacén del Bioquímico):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 8918 (ROMEO BOXES):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 9589 (GAMING CITY ABASTO SHOPPING):\n-El script Nº 1 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 9746 (GAMING CITY CABALLITO):\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 11360 (Ipanema Distribuciones):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n----\n-\n-Scripts de la empresa Nº 874 (Gaming-City Computacion):\n-El script Nº 1 se ejecutó correctamente.\n-El script Nº 2 se ejecutó correctamente.\n-\n-Scripts de la empresa Nº 6149 (Duaitek SRL):\n-El script Nº 1 se ejecutó correctamente.\n-# 📦 ROADS > SAAS > SOPORTE\n--------------------------------------------------------------------------------\n-\n-\n-\n--------------------------------------------------------------------\n-\n-## ANTIAFIP NOTA DE CREDITO\n-\n-saas_3393\n-\n-SELECT idventa, fecha, estado, estadocae, numero, total, cae, cuit, dni, tipodoc, idcliente, vencimientocae FROM saas_3393.`ventas` WHERE idtipoventa = 18 AND numero > 1281 ORDER BY numero LIMIT 200;\n-\n-SELECT idventa, fecha, estado, estadocae, numero, total, cae, cuit, dni, tipodoc, idcliente, vencimientocae FROM saas_3393.`ventas` WHERE idventa IN (SELECT idventa FROM saas_3393.ventasxventas WHERE idrelacion = 86363);\n-\n-\n-## EMPRESAS EN BETA Y ALFA\n-\n-(98, 161, 2629)\n-(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, 2132, 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5007, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12281)\n-\n-\n-## PROBLEMAS ZUMBANDO\n-\n-### MAIL:\n-\n-Nombre de usuario de IAM: ses-smtp-user.20240702-164504\n-Nombre de usuario de SMTP: ********************\n-Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n-\n-NEW IAM PARA SES\n-AWS_KEY: ********************\n-AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n-\n-EX IAM PARA SES\n-AWS_KEY: ********************\n-AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n-\n-\n-### LAMDA:\n-\n-Acceso: https://saasargentina.signin.aws.amazon.com/console\n-User: empresa_9589_script_1\n-Password: qZbquV3&\n-Region: San Pablo (sa-east-1)\n-Lambda >> Functions: empresa_9589_script_1\n-\n-\n-## SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n-\n-*INFORME VENTAS ANUAL*\n-\n SELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\n FROM ventas AS v\n     LEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\n     LEFT JOIN clientes AS c ON v.idcliente = c.idcliente\n@@ -303,9 +95,9 @@\n WHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\n     AND tipofacturacion = 'electronico'\n ORDER BY fecha\n \n-*ARCHIVO DE COMBOS*\n+**ARCHIVO DE COMBOS**\n \n SELECT pxc.idcombo, c.codigo AS codigo_combo, c.nombre AS combo, pxc.idproducto, p.codigo, p.nombre AS producto, pxc.cantidad\n FROM productosxcombos AS pxc\n     LEFT JOIN productos AS c ON pxc.idcombo = c.idproducto\n@@ -317,36 +109,9 @@\n FROM productosxcombos AS pxc\n     LEFT JOIN productos AS p ON pxc.idcombo = p.idproducto\n WHERE p.combo = 1 AND p.mostrartienda = 1\n \n-*EXPORTACIÓN PARA INFORMES OFFLINE*\n \n-prod_saas\n-./6149.sh\n-exit\n-\n-cd /home/<USER>/SOPORTE/6149\n-./6149.sh\n-\n-db\n-\n-USE saas_6149;\n-TRUNCATE clientes;\n-TRUNCATE ventas;\n-TRUNCATE ivasxventas;\n-TRUNCATE tributosxventas;\n-TRUNCATE ventas_ml;\n-TRUNCATE datosxextras;\n-SOURCE saas_6149_ventas.sql;\n-SOURCE saas_6149_clientes.sql;\n-SOURCE saas_6149_ivasxventas.sql;\n-SOURCE saas_6149_tributosxventas.sql;\n-SOURCE saas_6149_ventas_ml.sql;\n-SOURCE saas_6149_datosxextras.sql;\n-SOURCE saas_6149_usuarios.sql;\n-UPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n-\n-\n ## IMAGENES GEZATEK\n \n SELECT idproducto, nombre, codigo,\n CONCAT('https://s3-sa-east-1.amazonaws.com/saasargentina/', archivos.iua, '/imagen') AS imagen,\n"}, {"date": 1748287431594, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,10 @@\n # 📦 ROADS > SAAS > SOPORTE\n--------------------------------------------------------------------------------\n \n \n \n \n--------------------------------------------------------------------------------\n+*******************************************************************************\n \n ## ANTIAFIP NOTA DE CREDITO\n \n saas_3393\n"}], "date": 1725281405250, "name": "Commit-0", "content": "# 📦 ROADS > SAAS > SOPORTE\n-------------------------------------------------------------------------------\n\n### SUBDIARIOS URIEL NUEVO SÓLO VENTAS\n\nSELECT fecha, cv.nombre, cv.letra, cv.puntodeventa, numero, c.nombre, c.razonsocial, ML_order_id, d.texto AS carrito, subtotal, descuento, neto, nogravado, exento, COALESCE(iv10.iva, 0) AS iva_10, COALESCE(iv21.iva, 0) AS iva_21, tributos, totaL\nFROM ventas AS v\nLEFT JOIN categorias_ventas AS cv ON v.idtipoventa = cv.idtipoventa\nLEFT JOIN clientes AS c ON v.idcliente = c.idcliente\nLEFT JOIN datosxextras AS d ON d.idextraxmodulo = 5 AND v.idventa = d.idrelacion\nLEFT JOIN ivasxventas AS iv10 ON v.idventa = iv10.idventa AND iv10.idiva = 4\nLEFT JOIN ivasxventas AS iv21 ON v.idventa = iv21.idventa AND iv21.idiva = 5\nWHERE fecha > '2023-01-01' AND fecha < '2023-02-01'\nAND tipofacturacion = 'electronico'\nORDER BY fecha\n\n\nprod_saas\ncd 6149\nrm saas_6149_clientes.sql.gz\nrm saas_6149_ventas.sql.gz\nrm saas_6149_ivasxventas.sql.gz\nrm saas_6149_tributosxventas.sql.gz\nrm saas_6149_datosxextras.sql.gz\n\nmysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 clientes | gzip -9 > saas_6149_clientes.sql.gz\nmysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ventas --where=\"fecha > '2024-08-01'\" | gzip -9 > saas_6149_ventas.sql.gz\nmysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 ivasxventas | gzip -9 > saas_6149_ivasxventas.sql.gz\nmysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 tributosxventas | gzip -9 > saas_6149_tributosxventas.sql.gz\nmysqldump --no-tablespaces -hdb-beta-ro.saasargentina.com -usaasroot -pLegFDwFnUxfUv46kwjtqbBpH saas_6149 datosxextras --where=\"idextraxmodulo IN (5, 12)\" | gzip -9 > saas_6149_datosxextras.sql.gz\n\nexit\ncd /home/<USER>/SOPORTE/6149\n\nrm saas_6149_clientes.sql\nrm saas_6149_ventas.sql\nrm saas_6149_ivasxventas.sql\nrm saas_6149_tributosxventas.sql\nrm saas_6149_datosxextras.sql\n\nscp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_clientes.sql.gz .\nscp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_ventas.sql.gz .\nscp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_ivasxventas.sql.gz .\nscp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_tributosxventas.sql.gz .\nscp -i ~/.ssh/desarrollo.pem ec2-user@*************:/home/<USER>/6149/saas_6149_datosxextras.sql.gz .\n\ngzip -d saas_6149_clientes.sql.gz\ngzip -d saas_6149_ventas.sql.gz\ngzip -d saas_6149_ivasxventas.sql.gz\ngzip -d saas_6149_tributosxventas.sql.gz\ngzip -d saas_6149_datosxextras.sql.gz\n\nUSE saas_6149;\nTRUNCATE clientes;\nTRUNCATE ventas;\nTRUNCATE ivasxventas;\nTRUNCATE tributosxventas;\nTRUNCATE datosxextras;\nSOURCE saas_6149_ventas.sql;\nSOURCE saas_6149_clientes.sql;\nSOURCE saas_6149_ivasxventas.sql;\nSOURCE saas_6149_tributosxventas.sql;\nSOURCE saas_6149_datosxextras.sql;\nUPDATE ventas SET estado = 'anulado' WHERE estadocae = 'rechazado';\n\n\n### PARA VACIAR LOGS:\n\n- Revisar espacio en disco y generar issue para limpiar o que no guarde si notas algo raro\nsudo su\ndu -h / | grep '[0-9\\.]\\+G' > espacio\ncat espacio\n\n> alfa_ssl_access_log\n> alfa_ssl_error_log\n> api-alfa_ssl_access_log\n> api-alfa_ssl_error_log\n> api-beta_ssl_access_log\n> api-beta_ssl_error_log\n> api_ssl_access_log\n> api_ssl_error_log\n> app_ssl_access_log\n> app_ssl_error_log\n> beta_ssl_access_log\n> beta_ssl_error_log\n> informes-alfa_ssl_access_log\n> informes-alfa_ssl_error_log\n> informes-beta_ssl_access_log\n> informes-beta_ssl_error_log\n> informes_ssl_access_log\n> informes_ssl_error_log\n> login_ssl_access_log\n> login_ssl_error_log\n> scripts_ssl_access_log\n> scripts_ssl_error_log\n> www_ssl_access_log\n> www_ssl_error_log\n\n\n### LIMPIEZA\n\n- Desconectar de ML lo que hay en scripts/crontab/meli_eliminar.txt (sería ideal con un Lamda)\n- Vaciar los archivos de archivos_eliminar.txt\nTambién tengo un archivo con miles de 414\nmrMpouB6CwYbxYzfX4dM\nYAMbM5AUUXWbjsXiC3Gz\nUeO2rCGc6RDvSwQwTj33\nGkf3NV2VdsbTMWyO8cHS\neegSb4ea6Nkn3HCETMiZ\nSOZ5XZbrhJpiMZPnS8M2\nUJ3PBMUghUYcfeatSmsC\nzDitU7SXxOJ6sgxQXqQ9\nmEBenx5EYfj8hHkcu84d\nx4u37Dn9rrEiKRXdeEGJ\nf2KFQ5DuQSAsiT87zDIi\n9ry6R98Yh4TPirTF4J80\nHyDtEy8HDjTxquYQYdbq\nAbn7K2fmfh7z2SXdZU8M\naQjIEVpZNqFaX3NMPoFr\nFsTyDjT8Dtu6mQIomJjg\nXk0mxVTMcPM3pxDwki6U\ncNcgqiRkH6krEocjaknC\nDudGFpZ365KNo9Kt5PYa\ns6mpAAFGd2JibdSmqxKq\nNVce5qjBo3TaY8Ykksex\naMEakHabEhTzIm06Ewnm\nJ0isamvJiwCYShT2u2HE\nPkdw5wDfgWG8zXDzS6vC\nsNVBUxXog4Jz7jukKWre\n\n\n### ALTA SUCURSAL GAMING CITY\n- Sincronizar rubros manualmente\n- Agregar la instancia a empresa_874_script_1.php\n- Agregar la instancia a es_franquiciado\n- Pasar la base de datos a BETA\n- Forzar una sincronización completa con productos en updated_at = now()\n- Activar 90 días de prueba\n\n"}]}