{"sourceFile": "CONFIG/hosts", "activeCommit": 0, "commits": [{"activePatchIndex": 5, "patches": [{"date": 1726005931886, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1726426285024, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,7 @@\n 127.0.0.1   localhost\n \n+************* saas-prod\n 127.0.0.1   saasargentina.des\n 127.0.0.1   login.saasargentina.des\n 127.0.0.1   app.saasargentina.des\n 127.0.0.1   api.saasargentina.des\n"}, {"date": 1726427543103, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -35,9 +35,9 @@\n \n 127.0.0.1   aavla.des\n 127.0.0.1   simply.des\n 127.0.0.1   audiologiccorp.des\n-# 127.0.0.1   simplementeviviendo.des\n+127.0.0.1   simplementeviviendo.des\n \n # The following lines are desirable for IPv6 capable hosts\n ::1     ip6-localhost ip6-loopback\n fe00::0 ip6-localnet\n"}, {"date": 1726838147478, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,12 +18,12 @@\n 127.0.0.1   beta.cronometrajeinstantaneo.lan\n 127.0.0.1   app.cronometrajeinstantaneo.lan\n 127.0.0.1   vivo.cronometrajeinstantaneo.lan\n \n-127.0.0.1   andresmaiden.des\n-127.0.0.1   tools.andresmaiden.des\n-127.0.0.1   brain.andresmaiden.des\n-127.0.0.1   ai.andresmaiden.des\n+127.0.0.1   andresmisiak.des\n+127.0.0.1   tools.andresmisiak.des\n+127.0.0.1   brain.andresmisiak.des\n+127.0.0.1   ai.andresmisiak.des\n \n 127.0.0.1   crono.des\n 127.0.0.1   admin.crono.des\n 127.0.0.1   app.crono.des\n"}, {"date": 1727122705478, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,9 @@\n 127.0.0.1   localhost\n \n ************* saas-prod\n+************* saas-hertruethis\n+\n 127.0.0.1   saasargentina.des\n 127.0.0.1   login.saasargentina.des\n 127.0.0.1   app.saasargentina.des\n 127.0.0.1   api.saasargentina.des\n"}, {"date": 1738851417975, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,8 +6,9 @@\n 127.0.0.1   saasargentina.des\n 127.0.0.1   login.saasargentina.des\n 127.0.0.1   app.saasargentina.des\n 127.0.0.1   api.saasargentina.des\n+127.0.0.1   api-dev.saasargentina.des\n 127.0.0.1   informes.saasargentina.des\n 127.0.0.1   scripts.saasargentina.des\n \n 127.0.0.1   cronometrajeinstantaneo.des\n"}], "date": 1726005931886, "name": "Commit-0", "content": "127.0.0.1   localhost\n\n127.0.0.1   saasargentina.des\n127.0.0.1   login.saasargentina.des\n127.0.0.1   app.saasargentina.des\n127.0.0.1   api.saasargentina.des\n127.0.0.1   informes.saasargentina.des\n127.0.0.1   scripts.saasargentina.des\n\n127.0.0.1   cronometrajeinstantaneo.des\n127.0.0.1   admin.cronometrajeinstantaneo.des\n127.0.0.1   app.cronometrajeinstantaneo.des\n127.0.0.1   vivo.cronometrajeinstantaneo.des\n\n127.0.0.1   cronometrajeinstantaneo.lan\n127.0.0.1   admin.cronometrajeinstantaneo.lan\n127.0.0.1   beta.cronometrajeinstantaneo.lan\n127.0.0.1   app.cronometrajeinstantaneo.lan\n127.0.0.1   vivo.cronometrajeinstantaneo.lan\n\n127.0.0.1   andresmaiden.des\n127.0.0.1   tools.andresmaiden.des\n127.0.0.1   brain.andresmaiden.des\n127.0.0.1   ai.andresmaiden.des\n\n127.0.0.1   crono.des\n127.0.0.1   admin.crono.des\n127.0.0.1   app.crono.des\n127.0.0.1   vivo.crono.des\n\n127.0.0.1   woocommerce.des\n127.0.0.1   laravel.des\n127.0.0.1   visiteangostura.des\n\n127.0.0.1   aavla.des\n127.0.0.1   simply.des\n127.0.0.1   audiologiccorp.des\n# 127.0.0.1   simplementeviviendo.des\n\n# The following lines are desirable for IPv6 capable hosts\n::1     ip6-localhost ip6-loopback\nfe00::0 ip6-localnet\nff00::0 ip6-mcastprefix\nff02::1 ip6-allnodes\nff02::2 ip6-allrouters\n"}]}