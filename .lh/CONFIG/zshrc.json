{"sourceFile": "CONFIG/zshrc", "activeCommit": 0, "commits": [{"activePatchIndex": 16, "patches": [{"date": 1726426432178, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1726426856305, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -131,9 +131,9 @@\n \n alias prod_saas=\"ssh ec2-user@saas-prod\"\n alias prod_saas_bastion=\"ssh -i ~/.ssh/desarrollo.pem <EMAIL>\"\n alias prod_hertruethis=\"ssh -i ~/.ssh/desarrollo.pem ec2-user@**************\"\n-alias prod_crono=\"ssh -i <EMAIL>\"\n+alias prod_crono=\"ssh <EMAIL>\"\n alias prod_turismobtc=\"gcloud compute ssh produccion\"\n alias prod_donweb=\"echo 'Qu9jfvVrU7YW' | xclip -selection c && ssh <EMAIL>\"\n \n alias hertruethis=echo\\ \"Contraseña Super Maestra Hertruethis\"\\ \\|\\ xclip\\ -sel\\ clip\n@@ -271,8 +271,9 @@\n     nohup nautilus -w $1 > /dev/null 2>&1 &\n }\n \n function memory_hack {\n+    unison\n     php -f ~/www/andresmaiden/tools/memory-hack.php\n }\n \n function phonegap_release () {\n"}, {"date": 1726492376600, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -271,9 +271,8 @@\n     nohup nautilus -w $1 > /dev/null 2>&1 &\n }\n \n function memory_hack {\n-    unison\n     php -f ~/www/andresmaiden/tools/memory-hack.php\n }\n \n function phonegap_release () {\n"}, {"date": 1726588799851, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -158,9 +158,9 @@\n alias nano=\"echo Mejor usa mcedit con e\"\n alias e=\"mcedit\"\n alias sz=\"source ~/.zshrc\"\n \n-alias ai=\"~/www/andresmaiden/ai/ai.php\"\n+alias ai=\"~/www/andresmisiak/ai/ai.php\"\n alias f=\"fabric\"\n alias copy='xsel --clipboard --input'\n alias paste='xsel --clipboard --output'\n function pattern () { code $HOME/.config/fabric/patterns/$1/system.md; }\n@@ -271,9 +271,9 @@\n     nohup nautilus -w $1 > /dev/null 2>&1 &\n }\n \n function memory_hack {\n-    php -f ~/www/andresmaiden/tools/memory-hack.php\n+    php -f ~/www/andresmisiak/tools/memory-hack.php\n }\n \n function phonegap_release () {\n     if [ -e platforms/android/app/build/outputs/apk/release/$1.keystore ]\n"}, {"date": 1727123139564, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -130,9 +130,9 @@\n alias gfo=\"git fetch -p origin\"\n \n alias prod_saas=\"ssh ec2-user@saas-prod\"\n alias prod_saas_bastion=\"ssh -i ~/.ssh/desarrollo.pem <EMAIL>\"\n-alias prod_hertruethis=\"ssh -i ~/.ssh/desarrollo.pem ec2-user@**************\"\n+alias prod_hertruethis=\"ssh -i ~/.ssh/desarrollo.pem ec2-user@saas-hertruethis\"\n alias prod_crono=\"ssh <EMAIL>\"\n alias prod_turismobtc=\"gcloud compute ssh produccion\"\n alias prod_donweb=\"echo 'Qu9jfvVrU7YW' | xclip -selection c && ssh <EMAIL>\"\n \n"}, {"date": 1728421879630, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -141,9 +141,9 @@\n alias network=\"sudo service network-manager restart\"\n alias art=\"php artisan\"\n alias untar=\"tar -zxvf \"\n alias consumo=\"sudo nethogs -v 3\"\n-alias mysql=\"sudo mysql -u root -p\"\n+alias db=\"sudo mysql -u root -p\"\n \n alias agi=\"sudo apt-get install\"\n alias aup=\"sudo apt-get update\"\n alias l=\"exa -lah\"\n"}, {"date": 1729831233744, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -159,14 +159,14 @@\n alias e=\"mcedit\"\n alias sz=\"source ~/.zshrc\"\n \n alias ai=\"~/www/andresmisiak/ai/ai.php\"\n-alias f=\"fabric\"\n+# alias f=\"fabric\"\n alias copy='xsel --clipboard --input'\n alias paste='xsel --clipboard --output'\n-function pattern () { code $HOME/.config/fabric/patterns/$1/system.md; }\n-function fe () { echo $2 | fabric -sp $1; }\n-rsync -a ~/MEGA/BRAIN/AI/* ~/.config/fabric/patterns\n+# function pattern () { code $HOME/.config/fabric/patterns/$1/system.md; }\n+# function fe () { echo $2 | fabric -sp $1; }\n+# rsync -a ~/MEGA/BRAIN/AI/* ~/.config/fabric/patterns\n \n # FUNCIONES VARIAS\n function gdiff () { git diff $1 --color | diff-so-fancy }\n \n"}, {"date": 1729831289805, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -411,5 +411,5 @@\n \n LC_CTYPE=es_AR.UTF-8\n LC_ALL=es_AR.UTF-8\n LANG=es_AR.UTF-8\n-if [ -f \"/home/<USER>/.config/fabric/fabric-bootstrap.inc\" ]; then . \"/home/<USER>/.config/fabric/fabric-bootstrap.inc\"; fi\n\\ No newline at end of file\n+# if [ -f \"/home/<USER>/.config/fabric/fabric-bootstrap.inc\" ]; then . \"/home/<USER>/.config/fabric/fabric-bootstrap.inc\"; fi\n\\ No newline at end of file\n"}, {"date": 1729839426960, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -158,9 +158,9 @@\n alias nano=\"echo Mejor usa mcedit con e\"\n alias e=\"mcedit\"\n alias sz=\"source ~/.zshrc\"\n \n-alias ai=\"~/www/andresmisiak/ai/ai.php\"\n+alias ai=\"php -f /home/<USER>/www/andresmisiak/ai/ai.php\"\n # alias f=\"fabric\"\n alias copy='xsel --clipboard --input'\n alias paste='xsel --clipboard --output'\n # function pattern () { code $HOME/.config/fabric/patterns/$1/system.md; }\n"}, {"date": 1734982565212, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,417 @@\n+# Path to your oh-my-zsh installation.\n+export ZSH=~/.oh-my-zsh\n+\n+# Set name of the theme to load.\n+# Look in ~/.oh-my-zsh/themes/\n+# Optionally, if you set this to \"random\", it'll load a random theme each\n+# time that oh-my-zsh is loaded.\n+ZSH_THEME=\"robbyrussell\"\n+\n+# Uncomment the following line to use case-sensitive completion.\n+# CASE_SENSITIVE=\"true\"\n+\n+# Uncomment the following line to use hyphen-insensitive completion. Case\n+# sensitive completion must be off. _ and - will be interchangeable.\n+# HYPHEN_INSENSITIVE=\"true\"\n+\n+# Uncomment the following line to disable bi-weekly auto-update checks.\n+# DISABLE_AUTO_UPDATE=\"true\"\n+\n+# Uncomment the following line to change how often to auto-update (in days).\n+# export UPDATE_ZSH_DAYS=13\n+\n+# Uncomment the following line to disable colors in ls.\n+# DISABLE_LS_COLORS=\"true\"\n+\n+# Uncomment the following line to disable auto-setting terminal title.\n+# DISABLE_AUTO_TITLE=\"true\"\n+\n+# Uncomment the following line to enable command auto-correction.\n+# ENABLE_CORRECTION=\"true\"\n+\n+# Uncomment the following line to display red dots whilst waiting for completion.\n+# COMPLETION_WAITING_DOTS=\"true\"\n+\n+# Uncomment the following line if you want to disable marking untracked files\n+# under VCS as dirty. This makes repository status check for large repositories\n+# much, much faster.\n+# DISABLE_UNTRACKED_FILES_DIRTY=\"true\"\n+\n+# Uncomment the following line if you want to change the command execution time\n+# stamp shown in the history command output.\n+# The optional three formats: \"mm/dd/yyyy\"|\"dd.mm.yyyy\"|\"yyyy-mm-dd\"\n+# HIST_STAMPS=\"mm/dd/yyyy\"\n+\n+# Would you like to use another custom folder than $ZSH/custom?\n+# ZSH_CUSTOM=/path/to/new-custom-folder\n+\n+# Which plugins would you like to load? (plugins can be found in ~/.oh-my-zsh/plugins/*)\n+# Custom plugins may be added to ~/.oh-my-zsh/custom/plugins/\n+# Example format: plugins=(rails git textmate ruby lighthouse)\n+# Add wisely, as too many plugins slow down shell startup.\n+\n+plugins=(\n+    git autojump\n+    zsh-autosuggestions\n+    sudo\n+    copypath\n+    copyfile\n+    copybuffer\n+    dirhistory\n+    history\n+)\n+[[ -s /home/<USER>/.autojump/etc/profile.d/autojump.sh ]] && source /home/<USER>/.autojump/etc/profile.d/autojump.sh\n+autoload -U compinit && compinit -u\n+\n+\n+# User configuration\n+source $ZSH/oh-my-zsh.sh\n+\n+# You may need to manually set your language environment\n+export LANG=en_US.UTF-8\n+\n+# Setting for the new UTF-8 terminal support in Lion\n+LC_CTYPE=en_US.UTF-8\n+LC_ALL=en_US.UTF-8\n+\n+\n+# PATH VARIOS\n+export PATH=${PATH}:/usr/local/sbin\n+export PATH=${PATH}:/usr/local/bin\n+export PATH=${PATH}:/usr/sbin\n+export PATH=${PATH}:/usr/bin\n+export PATH=${PATH}:/sbin\n+export PATH=${PATH}:/bin\n+export PATH=${PATH}:/usr/games\n+export PATH=${PATH}:/usr/local/games\n+export PATH=${PATH}:$HOME/.composer/vendor/bin\n+export PATH=${PATH}:$HOME/Apps/diff-so-fancy\n+export PATH=${PATH}:$HOME/.local/bin\n+export PATH=${PATH}:/bin\n+export PATH=${PATH}:/snap/bin\n+\n+# PATH PARA ANDROID, JAVA Y PHONEGAP\n+export ANDROID_SDK_ROOT=$HOME/Apps/android-sdk-linux\n+export PATH=${PATH}:$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$ANDROID_SDK_ROOT/cmdline-tools/tools/bin\n+export PATH=${PATH}:$HOME/Apps/android-sdk-linux/build-tools\n+export PATH=${PATH}:$HOME/Apps/android-sdk-linux/sources\n+export PATH=${PATH}:$HOME/Apps/android-sdk-linux/platforms\n+export JAVA_HOME=\"/usr/lib/jvm/adoptopenjdk-8-hotspot-amd64\"\n+\n+# Set personal aliases, overriding those provided by oh-my-zsh libs,\n+alias services=\"cd $HOME/www/saasargentina/services\"\n+alias acc=\"cd $HOME/www/saasargentina/services/acc\"\n+alias api=\"cd $HOME/www/saasargentina/services/api\"\n+alias saas=\"cd $HOME/www/saasargentina/services/app\"\n+alias informes=\"cd $HOME/www/saasargentina/services/informes\"\n+alias login=\"cd $HOME/www/saasargentina/services/login\"\n+alias scripts=\"cd $HOME/www/saasargentina/services/scripts\"\n+\n+alias crono=\"cd $HOME/www/cronometrajeinstantaneo/\"\n+alias admin=\"cd $HOME/www/cronometrajeinstantaneo/admin\"\n+alias app=\"cd $HOME/www/cronometrajeinstantaneo/app\"\n+alias wp=\"cd $HOME/www/cronometrajeinstantaneo/wp\"\n+\n+alias www=\"cd $HOME/www\"\n+alias desk=\"cd $HOME/desk\"\n+alias woo=\"cd $HOME/www/woocommerce/wp-content/plugins\"\n+\n+alias gch=\"git checkout\"\n+alias gst=\"git status\"\n+alias gb=\"git branch\"\n+alias gc=\"git commit -am\"\n+alias gpo=\"git push origin\"\n+alias gpl=\"git pull --rebase origin\"\n+alias gpo=\"git push origin\"\n+alias gm=\"git merge\"\n+alias gd=\"git diff\"\n+alias gdl=\"git diff HEAD~1 HEAD\"\n+alias gl=\"git log --stat --pretty=oneline\"\n+alias gfo=\"git fetch -p origin\"\n+\n+alias prod_saas=\"ssh ec2-user@saas-prod\"\n+alias prod_saas_bastion=\"ssh -i ~/.ssh/desarrollo.pem <EMAIL>\"\n+alias prod_hertruethis=\"ssh -i ~/.ssh/desarrollo.pem ec2-user@saas-hertruethis\"\n+alias prod_crono=\"ssh <EMAIL>\"\n+alias prod_turismobtc=\"gcloud compute ssh produccion\"\n+alias prod_donweb=\"echo 'Qu9jfvVrU7YW' | xclip -selection c && ssh <EMAIL>\"\n+\n+alias hertruethis=echo\\ \"Contraseña Super Maestra Hertruethis\"\\ \\|\\ xclip\\ -sel\\ clip\n+alias files=\"find . -type f \\| wc -l\"\n+alias network=\"sudo service network-manager restart\"\n+alias art=\"php artisan\"\n+alias untar=\"tar -zxvf \"\n+alias consumo=\"sudo nethogs -v 3\"\n+alias db=\"sudo mysql -u root -p\"\n+\n+alias agi=\"sudo apt-get install\"\n+alias aup=\"sudo apt-get update\"\n+alias l=\"exa -lah\"\n+alias prueba=\"code $HOME/www/prueba.php\"\n+alias hacer=\"code ~/MEGA/HACER.todo\"\n+alias hibernate=\"sudo systemctl hibernate -i\"\n+alias bluetooth_restart=\"sudo systemctl restart bluetooth\"\n+alias phpunit=\"./vendor/bin/phpunit\"\n+alias tail_error=\"sudo tail /var/log/apache2/error.log\"\n+alias minecraft=\"java -jar ~/Apps/Minecraft/TLauncher-2.82.jar\"\n+alias find=\"echo Mejor usa: fd\"\n+alias nano=\"echo Mejor usa mcedit con e\"\n+alias e=\"mcedit\"\n+alias sz=\"source ~/.zshrc\"\n+alias rup=\"rsync -avz /home/<USER>/MEGA/ <EMAIL>:/home/<USER>/MEGA/\"\n+alias rdown=\"rsync -avz <EMAIL>:/home/<USER>/MEGA/ /home/<USER>/MEGA/\"\n+\n+alias ai=\"php -f /home/<USER>/www/andresmisiak/ai/ai.php\"\n+# alias f=\"fabric\"\n+alias copy='xsel --clipboard --input'\n+alias paste='xsel --clipboard --output'\n+# function pattern () { code $HOME/.config/fabric/patterns/$1/system.md; }\n+# function fe () { echo $2 | fabric -sp $1; }\n+# rsync -a ~/MEGA/BRAIN/AI/* ~/.config/fabric/patterns\n+\n+# FUNCIONES VARIAS\n+function gdiff () { git diff $1 --color | diff-so-fancy }\n+\n+function gh () {\n+    if [ -e \"hash\" ]; then\n+        commit=$(git rev-parse HEAD)\n+        echo $commit > hash\n+        git commit -am \"Update hash\"\n+    else\n+        echo \"Error: File 'hash' does not exist in the current directory.\"\n+        return 1\n+    fi\n+}\n+\n+function saas_hash () {\n+    echo \"Estoy usando gh de git hash ahora\"\n+}\n+\n+function migrate () {acc; ./command.php migrate $1 $2 $3 $4 $5; cd -;}\n+function nuevophp () {acc; ./command.php nuevophp $1; cd -;}\n+function nuevosql () {acc; ./command.php nuevosql $1; cd -;}\n+function dl () {\n+    file=$1\n+\n+    if [ \"$file\" != 'api-ml' ] && [ \"$file\" != 'api-v1' ] && [ \"$file\" != 'crono' ] && [ \"$file\" != 'saas' ] && [ \"$file\" != 'laravel' ];\n+    then\n+        echo Incorrect file: api-ml, api-v1, crono, saas, laravel\n+        return 0\n+    fi\n+\n+    if [ -n \"$2\" ]\n+    then\n+        d=$2\n+    else\n+        d=$(date \"+%Y-%m-%d\")\n+    fi\n+\n+    if [ \"$file\" = 'api-ml' ]\n+    then\n+        scp ec2-user@saas-prod:/saas/customer/services/acc/empresas/logs/api-ml/$d.log $HOME/SOPORTE/api-ml_$d.log\n+        code $HOME/SOPORTE/api-ml_$d.log\n+    fi\n+\n+    if [ \"$file\" = 'api-v1' ]\n+    then\n+        scp ec2-user@saas-prod:/saas/customer/services/acc/empresas/logs/api-v1/$d.log $HOME/SOPORTE/api-v1_$d.log\n+        code $HOME/SOPORTE/api-v1_$d.log\n+    fi\n+\n+    if [ \"$file\" = 'crono' ]\n+    then\n+        cd $HOME/SOPORTE\n+        mv cronometrajeinstantaneo.{sql,old}\n+        scp -i ~/.ssh/andresmaiden2 <EMAIL>:/home/<USER>/backups/backup.sql.gz cronometrajeinstantaneo.sql.gz\n+        gzip -d cronometrajeinstantaneo.sql.gz\n+        sudo mysql -u root -p cronometrajeinstantaneo < cronometrajeinstantaneo.sql\n+    fi\n+\n+    if [ \"$file\" = 'saas' ]\n+    then\n+\n+        if [ -n \"$2\" ]\n+        then\n+            id=$2\n+            cd $HOME/SOPORTE\n+            rm saas_$id.sql\n+            archivo_saas=$(ssh ec2-user@saas-prod \"find /saas/customer/services/acc/backups -type f -name saas_${id}_* -print -quit\")\n+            scp ec2-user@saas-prod:${archivo_saas} .\n+            archivo_local=$(fd saas_${id})\n+            mv ${archivo_local} saas_${id}.sql.gz\n+            gzip -d saas_$id.sql.gz\n+            sudo mysql -u root -p saas_${id} < saas_${id}.sql\n+        else\n+            echo Falta el idempresa\n+        fi\n+    fi\n+\n+    if [ \"$file\" = 'laravel' ]\n+    then\n+        mv $HOME/SOPORTE/laravel.{log,old}\n+        scp -i ~/.ssh/andresmaiden2 <EMAIL>:/var/www/cronometrajeinstantaneo/admin/storage/logs/laravel.log $HOME/SOPORTE/laravel.log\n+        code $HOME/SOPORTE/laravel.log\n+    fi\n+\n+}\n+\n+function mkcd () {mkdir $1; cd $1;}\n+function php7 () {\n+    sudo a2dismod php8.3\n+    sudo a2enmod php7.1\n+    sudo service apache2 restart\n+    sudo update-alternatives --set php /usr/bin/php7.1\n+}\n+function php8 () {\n+    sudo a2dismod php7.1\n+    sudo a2enmod php8.3\n+    sudo service apache2 restart\n+    sudo update-alternatives --set php /usr/bin/php8.3\n+}\n+\n+function open() {\n+    nohup nautilus -w $1 > /dev/null 2>&1 &\n+}\n+\n+function memory_hack {\n+    php -f ~/www/andresmisiak/tools/memory-hack.php\n+}\n+\n+function phonegap_release () {\n+    if [ -e platforms/android/app/build/outputs/apk/release/$1.keystore ]\n+    then\n+        b platforms/android/app/build/outputs/apk/release/key.txt\n+        phonegap build --release android;\n+        jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore platforms/android/app/build/outputs/apk/release/$1.keystore platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk $1\n+        mv platforms/android/app/build/outputs/apk/release/app-release{,-old}.apk\n+        ~/Apps/android-sdk-linux/build-tools/28.0.3/zipalign -v 4 platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk platforms/android/app/build/outputs/apk/release/app-release.apk\n+    else\n+        echo \"No existe el archivo platforms/android/app/build/outputs/apk/release/$1.keystore\"\n+    fi\n+}\n+\n+function crono_build_apk () {\n+    cd $HOME/phonegap/cronometrajeinstantaneo;\n+    phonegap build;\n+    mv platforms/android/app/build/outputs/apk/debug/app-debug.apk $HOME/CRONO/descargas/cronometrajeinstantaneo-android-beta.apk;\n+    scp -i ~/.ssh/andresmaiden $HOME/CRONO/descargas/Cronometraje\\ Instantaneo.debug.v2.9.8.apk <EMAIL>:/var/www/cronometrajeinstantaneo/www/descargas/cronometrajeinstantaneo.beta.v2.9.8.apk;\n+    cd -;\n+}\n+\n+function crono_build_linux () {\n+    cd $HOME/www/cronometrajeinstantaneo/desktop;\n+    npm run package-linux;\n+    tar -zcvf release-builds/cronometrajeinstantaneo-linux-x64.tar.gz release-builds/cronometrajeinstantaneo-linux-x64\n+    scp -i ~/.ssh/andresmaiden $HOME/www/cronometrajeinstantaneo/desktop/release-builds/cronometrajeinstantaneo-linux-x64.tar.gz <EMAIL>:$HOME/www/cronometrajeinstantaneo/public/descargas/cronometrajeinstantaneo-linux-x64.tar.gz;\n+    cd -;\n+}\n+\n+function crono_upload_descargas () {\n+    file=$1\n+    scp -i ~/.ssh/andresmaiden2 $file <EMAIL>:/var/www/cronometrajeinstantaneo/descargas/$file;\n+}\n+\n+function crono_encoded () {\n+    cd $HOME/www;\n+    $HOME/Apps/ioncube_encoder5_basic_10.2/ioncube_encoder.sh -72 \\\n+        --replace \\\n+        --copy \"@/*/\" \\\n+        --encode app/ \\\n+        --encode bootstrap/ \\\n+        --encode config/ \\\n+        --encode old-includes/ \\\n+        --encode public/ \\\n+        --encode routes/ \\\n+        --encode tests/ \\\n+        --ignore .git/ \\\n+        --ignore node_modules/ \\\n+        --ignore database/ \\\n+        cronometrajeinstantaneo/ -o www_encoded/;\n+    random=$(b /dev/urandom | tr -dc 'a-z' | fold -w 4 | head -n 1);\n+    tar -zcvf www_encoded_$random.tar.gz www_encoded;\n+    rm -Rf www_encoded;\n+    scp -i ~/.ssh/andresmaiden2 www_encoded_$random.tar.gz <EMAIL>:/var/www/cronometrajeinstantaneo/public/descargas/www_encoded_$random.tar.gz;\n+    # scp www_encoded_$random.tar.gz pi@192.168.0.10:/home/<USER>/www_encoded_$random.tar.gz;\n+    echo 'Descargar actualización y luego ELIMINAR EL ARCHIVO';\n+    echo \"wget https://cronometrajeinstantaneo.com/descargas/www_encoded_$random.tar.gz\"\n+    echo \"tar -zxvf www_encoded_$random.tar.gz\"\n+    echo 'sudo mv /var/www/{,old}www_encoded'\n+    echo 'sudo mv www_encoded /var/www'\n+    echo 'chmod -R 775 /var/www/www_encoded/storage'\n+    echo 'chmod -R 775 /var/www/www_encoded/bootstrap/cache'\n+    cd -;\n+}\n+\n+function crono_logo () {\n+    scp -i ~/.ssh/andresmaiden2 $1 <EMAIL>:/var/www/cronometrajeinstantaneo/public/images/eventos/$1;\n+}\n+\n+function art_clear () {\n+    art cache:clear\n+    art config:clear\n+    art view:clear\n+}\n+\n+function somoshinchada {\n+    cd $HOME/www/mundial2018-vue;\n+    git pull origin master;\n+    git push origin master;\n+    npm run build;\n+    scp -r -i ~/.ssh/andresmaiden dist/* <EMAIL>:/var/www/somoshinchada/dist;\n+    cd -;\n+}\n+\n+function deploy () {\n+\n+    env=$1\n+    branch=$1\n+\n+    if [ -z $env ]; then\n+        echo Specify env: prod, beta, alfa, master\n+        return 0\n+    fi\n+\n+    if [ $env != 'prod' ] && [ $env != 'beta' ] && [ $env != 'alfa' ] && [ $env != 'master' ]; then\n+        echo Incorrect env: prod, beta, alfa, master\n+        return 0\n+    fi\n+\n+    if [ $env = 'prod' ]; then\n+        random=$(cat /dev/urandom | tr -dc 'a-z' | fold -w 4 | head -n 1)\n+        read \"reply?IS THIS PROD!? ($random): \"\n+        if [[ $reply != $random ]]; then\n+            echo Canceling...\n+            return 0\n+        fi\n+    fi\n+\n+    git checkout $branch\n+    git push origin $branch:$branch\n+\n+    if [ $env != 'master' ]; then\n+        git push $env $branch:master;\n+    fi\n+\n+    echo 'Ready, remember to migrate!';\n+}\n+\n+\n+# EJECUCIONES ESPECIFICAS\n+memory_hack\n+\n+\n+# AGREGADOS AUTOMÁTICAMENTE\n+export NVM_DIR=\"$HOME/.nvm\"\n+[ -s \"$NVM_DIR/nvm.sh\" ] && \\. \"$NVM_DIR/nvm.sh\"  # This loads nvm\n+[ -s \"$NVM_DIR/bash_completion\" ] && \\. \"$NVM_DIR/bash_completion\"  # This loads nvm bash_completion\n+\n+# The next line updates PATH for the Google Cloud SDK.\n+if [ -f '/home/<USER>/google-cloud-sdk/path.zsh.inc' ]; then . '/home/<USER>/google-cloud-sdk/path.zsh.inc'; fi\n+\n+# The next line enables shell command completion for gcloud.\n+if [ -f '/home/<USER>/google-cloud-sdk/completion.zsh.inc' ]; then . '/home/<USER>/google-cloud-sdk/completion.zsh.inc'; fi\n+\n+LC_CTYPE=es_AR.UTF-8\n+LC_ALL=es_AR.UTF-8\n+LANG=es_AR.UTF-8\n+# if [ -f \"/home/<USER>/.config/fabric/fabric-bootstrap.inc\" ]; then . \"/home/<USER>/.config/fabric/fabric-bootstrap.inc\"; fi\n\\ No newline at end of file\n"}, {"date": 1735562353402, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -158,8 +158,9 @@\n alias nano=\"echo Mejor usa mcedit con e\"\n alias e=\"mcedit\"\n alias sz=\"source ~/.zshrc\"\n alias rup=\"rsync -avz /home/<USER>/MEGA/ <EMAIL>:/home/<USER>/MEGA/\"\n+alias rown=\"ssh <EMAIL> 'chown -R andresmaiden:www-data /home/<USER>/MEGA/BRAIN'\"\n alias rdown=\"rsync -avz <EMAIL>:/home/<USER>/MEGA/ /home/<USER>/MEGA/\"\n \n alias ai=\"php -f /home/<USER>/www/andresmisiak/ai/ai.php\"\n # alias f=\"fabric\"\n@@ -413,420 +414,5 @@\n \n LC_CTYPE=es_AR.UTF-8\n LC_ALL=es_AR.UTF-8\n LANG=es_AR.UTF-8\n-# if [ -f \"/home/<USER>/.config/fabric/fabric-bootstrap.inc\" ]; then . \"/home/<USER>/.config/fabric/fabric-bootstrap.inc\"; fi\n-# Path to your oh-my-zsh installation.\n-export ZSH=~/.oh-my-zsh\n-\n-# Set name of the theme to load.\n-# Look in ~/.oh-my-zsh/themes/\n-# Optionally, if you set this to \"random\", it'll load a random theme each\n-# time that oh-my-zsh is loaded.\n-ZSH_THEME=\"robbyrussell\"\n-\n-# Uncomment the following line to use case-sensitive completion.\n-# CASE_SENSITIVE=\"true\"\n-\n-# Uncomment the following line to use hyphen-insensitive completion. Case\n-# sensitive completion must be off. _ and - will be interchangeable.\n-# HYPHEN_INSENSITIVE=\"true\"\n-\n-# Uncomment the following line to disable bi-weekly auto-update checks.\n-# DISABLE_AUTO_UPDATE=\"true\"\n-\n-# Uncomment the following line to change how often to auto-update (in days).\n-# export UPDATE_ZSH_DAYS=13\n-\n-# Uncomment the following line to disable colors in ls.\n-# DISABLE_LS_COLORS=\"true\"\n-\n-# Uncomment the following line to disable auto-setting terminal title.\n-# DISABLE_AUTO_TITLE=\"true\"\n-\n-# Uncomment the following line to enable command auto-correction.\n-# ENABLE_CORRECTION=\"true\"\n-\n-# Uncomment the following line to display red dots whilst waiting for completion.\n-# COMPLETION_WAITING_DOTS=\"true\"\n-\n-# Uncomment the following line if you want to disable marking untracked files\n-# under VCS as dirty. This makes repository status check for large repositories\n-# much, much faster.\n-# DISABLE_UNTRACKED_FILES_DIRTY=\"true\"\n-\n-# Uncomment the following line if you want to change the command execution time\n-# stamp shown in the history command output.\n-# The optional three formats: \"mm/dd/yyyy\"|\"dd.mm.yyyy\"|\"yyyy-mm-dd\"\n-# HIST_STAMPS=\"mm/dd/yyyy\"\n-\n-# Would you like to use another custom folder than $ZSH/custom?\n-# ZSH_CUSTOM=/path/to/new-custom-folder\n-\n-# Which plugins would you like to load? (plugins can be found in ~/.oh-my-zsh/plugins/*)\n-# Custom plugins may be added to ~/.oh-my-zsh/custom/plugins/\n-# Example format: plugins=(rails git textmate ruby lighthouse)\n-# Add wisely, as too many plugins slow down shell startup.\n-\n-plugins=(\n-    git autojump\n-    zsh-autosuggestions\n-    sudo\n-    copypath\n-    copyfile\n-    copybuffer\n-    dirhistory\n-    history\n-)\n-[[ -s /home/<USER>/.autojump/etc/profile.d/autojump.sh ]] && source /home/<USER>/.autojump/etc/profile.d/autojump.sh\n-autoload -U compinit && compinit -u\n-\n-\n-# User configuration\n-source $ZSH/oh-my-zsh.sh\n-\n-# You may need to manually set your language environment\n-export LANG=en_US.UTF-8\n-\n-# Setting for the new UTF-8 terminal support in Lion\n-LC_CTYPE=en_US.UTF-8\n-LC_ALL=en_US.UTF-8\n-\n-\n-# PATH VARIOS\n-export PATH=${PATH}:/usr/local/sbin\n-export PATH=${PATH}:/usr/local/bin\n-export PATH=${PATH}:/usr/sbin\n-export PATH=${PATH}:/usr/bin\n-export PATH=${PATH}:/sbin\n-export PATH=${PATH}:/bin\n-export PATH=${PATH}:/usr/games\n-export PATH=${PATH}:/usr/local/games\n-export PATH=${PATH}:$HOME/.composer/vendor/bin\n-export PATH=${PATH}:$HOME/Apps/diff-so-fancy\n-export PATH=${PATH}:$HOME/.local/bin\n-export PATH=${PATH}:/bin\n-export PATH=${PATH}:/snap/bin\n-\n-# PATH PARA ANDROID, JAVA Y PHONEGAP\n-export ANDROID_SDK_ROOT=$HOME/Apps/android-sdk-linux\n-export PATH=${PATH}:$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$ANDROID_SDK_ROOT/cmdline-tools/tools/bin\n-export PATH=${PATH}:$HOME/Apps/android-sdk-linux/build-tools\n-export PATH=${PATH}:$HOME/Apps/android-sdk-linux/sources\n-export PATH=${PATH}:$HOME/Apps/android-sdk-linux/platforms\n-export JAVA_HOME=\"/usr/lib/jvm/adoptopenjdk-8-hotspot-amd64\"\n-\n-# Set personal aliases, overriding those provided by oh-my-zsh libs,\n-alias services=\"cd $HOME/www/saasargentina/services\"\n-alias acc=\"cd $HOME/www/saasargentina/services/acc\"\n-alias api=\"cd $HOME/www/saasargentina/services/api\"\n-alias saas=\"cd $HOME/www/saasargentina/services/app\"\n-alias informes=\"cd $HOME/www/saasargentina/services/informes\"\n-alias login=\"cd $HOME/www/saasargentina/services/login\"\n-alias scripts=\"cd $HOME/www/saasargentina/services/scripts\"\n-\n-alias crono=\"cd $HOME/www/cronometrajeinstantaneo/\"\n-alias admin=\"cd $HOME/www/cronometrajeinstantaneo/admin\"\n-alias app=\"cd $HOME/www/cronometrajeinstantaneo/app\"\n-alias wp=\"cd $HOME/www/cronometrajeinstantaneo/wp\"\n-\n-alias www=\"cd $HOME/www\"\n-alias desk=\"cd $HOME/desk\"\n-alias woo=\"cd $HOME/www/woocommerce/wp-content/plugins\"\n-\n-alias gch=\"git checkout\"\n-alias gst=\"git status\"\n-alias gb=\"git branch\"\n-alias gc=\"git commit -am\"\n-alias gpo=\"git push origin\"\n-alias gpl=\"git pull --rebase origin\"\n-alias gpo=\"git push origin\"\n-alias gm=\"git merge\"\n-alias gd=\"git diff\"\n-alias gdl=\"git diff HEAD~1 HEAD\"\n-alias gl=\"git log --stat --pretty=oneline\"\n-alias gfo=\"git fetch -p origin\"\n-\n-alias prod_saas=\"ssh ec2-user@saas-prod\"\n-alias prod_saas_bastion=\"ssh -i ~/.ssh/desarrollo.pem <EMAIL>\"\n-alias prod_hertruethis=\"ssh -i ~/.ssh/desarrollo.pem ec2-user@saas-hertruethis\"\n-alias prod_crono=\"ssh <EMAIL>\"\n-alias prod_turismobtc=\"gcloud compute ssh produccion\"\n-alias prod_donweb=\"echo 'Qu9jfvVrU7YW' | xclip -selection c && ssh <EMAIL>\"\n-\n-alias hertruethis=echo\\ \"Contraseña Super Maestra Hertruethis\"\\ \\|\\ xclip\\ -sel\\ clip\n-alias files=\"find . -type f \\| wc -l\"\n-alias network=\"sudo service network-manager restart\"\n-alias art=\"php artisan\"\n-alias untar=\"tar -zxvf \"\n-alias consumo=\"sudo nethogs -v 3\"\n-alias db=\"sudo mysql -u root -p\"\n-\n-alias agi=\"sudo apt-get install\"\n-alias aup=\"sudo apt-get update\"\n-alias l=\"exa -lah\"\n-alias prueba=\"code $HOME/www/prueba.php\"\n-alias hacer=\"code ~/MEGA/HACER.todo\"\n-alias hibernate=\"sudo systemctl hibernate -i\"\n-alias bluetooth_restart=\"sudo systemctl restart bluetooth\"\n-alias phpunit=\"./vendor/bin/phpunit\"\n-alias tail_error=\"sudo tail /var/log/apache2/error.log\"\n-alias minecraft=\"java -jar ~/Apps/Minecraft/TLauncher-2.82.jar\"\n-alias find=\"echo Mejor usa: fd\"\n-alias nano=\"echo Mejor usa mcedit con e\"\n-alias e=\"mcedit\"\n-alias sz=\"source ~/.zshrc\"\n-\n-alias ai=\"php -f /home/<USER>/www/andresmisiak/ai/ai.php\"\n-# alias f=\"fabric\"\n-alias copy='xsel --clipboard --input'\n-alias paste='xsel --clipboard --output'\n-# function pattern () { code $HOME/.config/fabric/patterns/$1/system.md; }\n-# function fe () { echo $2 | fabric -sp $1; }\n-# rsync -a ~/MEGA/BRAIN/AI/* ~/.config/fabric/patterns\n-\n-# FUNCIONES VARIAS\n-function gdiff () { git diff $1 --color | diff-so-fancy }\n-\n-function gh () {\n-    if [ -e \"hash\" ]; then\n-        commit=$(git rev-parse HEAD)\n-        echo $commit > hash\n-        git commit -am \"Update hash\"\n-    else\n-        echo \"Error: File 'hash' does not exist in the current directory.\"\n-        return 1\n-    fi\n-}\n-\n-function saas_hash () {\n-    echo \"Estoy usando gh de git hash ahora\"\n-}\n-\n-function migrate () {acc; ./command.php migrate $1 $2 $3 $4 $5; cd -;}\n-function nuevophp () {acc; ./command.php nuevophp $1; cd -;}\n-function nuevosql () {acc; ./command.php nuevosql $1; cd -;}\n-function dl () {\n-    file=$1\n-\n-    if [ \"$file\" != 'api-ml' ] && [ \"$file\" != 'api-v1' ] && [ \"$file\" != 'crono' ] && [ \"$file\" != 'saas' ] && [ \"$file\" != 'laravel' ];\n-    then\n-        echo Incorrect file: api-ml, api-v1, crono, saas, laravel\n-        return 0\n-    fi\n-\n-    if [ -n \"$2\" ]\n-    then\n-        d=$2\n-    else\n-        d=$(date \"+%Y-%m-%d\")\n-    fi\n-\n-    if [ \"$file\" = 'api-ml' ]\n-    then\n-        scp ec2-user@saas-prod:/saas/customer/services/acc/empresas/logs/api-ml/$d.log $HOME/SOPORTE/api-ml_$d.log\n-        code $HOME/SOPORTE/api-ml_$d.log\n-    fi\n-\n-    if [ \"$file\" = 'api-v1' ]\n-    then\n-        scp ec2-user@saas-prod:/saas/customer/services/acc/empresas/logs/api-v1/$d.log $HOME/SOPORTE/api-v1_$d.log\n-        code $HOME/SOPORTE/api-v1_$d.log\n-    fi\n-\n-    if [ \"$file\" = 'crono' ]\n-    then\n-        cd $HOME/SOPORTE\n-        mv cronometrajeinstantaneo.{sql,old}\n-        scp -i ~/.ssh/andresmaiden2 <EMAIL>:/home/<USER>/backups/backup.sql.gz cronometrajeinstantaneo.sql.gz\n-        gzip -d cronometrajeinstantaneo.sql.gz\n-        sudo mysql -u root -p cronometrajeinstantaneo < cronometrajeinstantaneo.sql\n-    fi\n-\n-    if [ \"$file\" = 'saas' ]\n-    then\n-\n-        if [ -n \"$2\" ]\n-        then\n-            id=$2\n-            cd $HOME/SOPORTE\n-            rm saas_$id.sql\n-            archivo_saas=$(ssh ec2-user@saas-prod \"find /saas/customer/services/acc/backups -type f -name saas_${id}_* -print -quit\")\n-            scp ec2-user@saas-prod:${archivo_saas} .\n-            archivo_local=$(fd saas_${id})\n-            mv ${archivo_local} saas_${id}.sql.gz\n-            gzip -d saas_$id.sql.gz\n-            sudo mysql -u root -p saas_${id} < saas_${id}.sql\n-        else\n-            echo Falta el idempresa\n-        fi\n-    fi\n-\n-    if [ \"$file\" = 'laravel' ]\n-    then\n-        mv $HOME/SOPORTE/laravel.{log,old}\n-        scp -i ~/.ssh/andresmaiden2 <EMAIL>:/var/www/cronometrajeinstantaneo/admin/storage/logs/laravel.log $HOME/SOPORTE/laravel.log\n-        code $HOME/SOPORTE/laravel.log\n-    fi\n-\n-}\n-\n-function mkcd () {mkdir $1; cd $1;}\n-function php7 () {\n-    sudo a2dismod php8.3\n-    sudo a2enmod php7.1\n-    sudo service apache2 restart\n-    sudo update-alternatives --set php /usr/bin/php7.1\n-}\n-function php8 () {\n-    sudo a2dismod php7.1\n-    sudo a2enmod php8.3\n-    sudo service apache2 restart\n-    sudo update-alternatives --set php /usr/bin/php8.3\n-}\n-\n-function open() {\n-    nohup nautilus -w $1 > /dev/null 2>&1 &\n-}\n-\n-function memory_hack {\n-    php -f ~/www/andresmisiak/tools/memory-hack.php\n-}\n-\n-function phonegap_release () {\n-    if [ -e platforms/android/app/build/outputs/apk/release/$1.keystore ]\n-    then\n-        b platforms/android/app/build/outputs/apk/release/key.txt\n-        phonegap build --release android;\n-        jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore platforms/android/app/build/outputs/apk/release/$1.keystore platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk $1\n-        mv platforms/android/app/build/outputs/apk/release/app-release{,-old}.apk\n-        ~/Apps/android-sdk-linux/build-tools/28.0.3/zipalign -v 4 platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk platforms/android/app/build/outputs/apk/release/app-release.apk\n-    else\n-        echo \"No existe el archivo platforms/android/app/build/outputs/apk/release/$1.keystore\"\n-    fi\n-}\n-\n-function crono_build_apk () {\n-    cd $HOME/phonegap/cronometrajeinstantaneo;\n-    phonegap build;\n-    mv platforms/android/app/build/outputs/apk/debug/app-debug.apk $HOME/CRONO/descargas/cronometrajeinstantaneo-android-beta.apk;\n-    scp -i ~/.ssh/andresmaiden $HOME/CRONO/descargas/Cronometraje\\ Instantaneo.debug.v2.9.8.apk <EMAIL>:/var/www/cronometrajeinstantaneo/www/descargas/cronometrajeinstantaneo.beta.v2.9.8.apk;\n-    cd -;\n-}\n-\n-function crono_build_linux () {\n-    cd $HOME/www/cronometrajeinstantaneo/desktop;\n-    npm run package-linux;\n-    tar -zcvf release-builds/cronometrajeinstantaneo-linux-x64.tar.gz release-builds/cronometrajeinstantaneo-linux-x64\n-    scp -i ~/.ssh/andresmaiden $HOME/www/cronometrajeinstantaneo/desktop/release-builds/cronometrajeinstantaneo-linux-x64.tar.gz <EMAIL>:$HOME/www/cronometrajeinstantaneo/public/descargas/cronometrajeinstantaneo-linux-x64.tar.gz;\n-    cd -;\n-}\n-\n-function crono_upload_descargas () {\n-    file=$1\n-    scp -i ~/.ssh/andresmaiden2 $file <EMAIL>:/var/www/cronometrajeinstantaneo/descargas/$file;\n-}\n-\n-function crono_encoded () {\n-    cd $HOME/www;\n-    $HOME/Apps/ioncube_encoder5_basic_10.2/ioncube_encoder.sh -72 \\\n-        --replace \\\n-        --copy \"@/*/\" \\\n-        --encode app/ \\\n-        --encode bootstrap/ \\\n-        --encode config/ \\\n-        --encode old-includes/ \\\n-        --encode public/ \\\n-        --encode routes/ \\\n-        --encode tests/ \\\n-        --ignore .git/ \\\n-        --ignore node_modules/ \\\n-        --ignore database/ \\\n-        cronometrajeinstantaneo/ -o www_encoded/;\n-    random=$(b /dev/urandom | tr -dc 'a-z' | fold -w 4 | head -n 1);\n-    tar -zcvf www_encoded_$random.tar.gz www_encoded;\n-    rm -Rf www_encoded;\n-    scp -i ~/.ssh/andresmaiden2 www_encoded_$random.tar.gz <EMAIL>:/var/www/cronometrajeinstantaneo/public/descargas/www_encoded_$random.tar.gz;\n-    # scp www_encoded_$random.tar.gz pi@192.168.0.10:/home/<USER>/www_encoded_$random.tar.gz;\n-    echo 'Descargar actualización y luego ELIMINAR EL ARCHIVO';\n-    echo \"wget https://cronometrajeinstantaneo.com/descargas/www_encoded_$random.tar.gz\"\n-    echo \"tar -zxvf www_encoded_$random.tar.gz\"\n-    echo 'sudo mv /var/www/{,old}www_encoded'\n-    echo 'sudo mv www_encoded /var/www'\n-    echo 'chmod -R 775 /var/www/www_encoded/storage'\n-    echo 'chmod -R 775 /var/www/www_encoded/bootstrap/cache'\n-    cd -;\n-}\n-\n-function crono_logo () {\n-    scp -i ~/.ssh/andresmaiden2 $1 <EMAIL>:/var/www/cronometrajeinstantaneo/public/images/eventos/$1;\n-}\n-\n-function art_clear () {\n-    art cache:clear\n-    art config:clear\n-    art view:clear\n-}\n-\n-function somoshinchada {\n-    cd $HOME/www/mundial2018-vue;\n-    git pull origin master;\n-    git push origin master;\n-    npm run build;\n-    scp -r -i ~/.ssh/andresmaiden dist/* <EMAIL>:/var/www/somoshinchada/dist;\n-    cd -;\n-}\n-\n-function deploy () {\n-\n-    env=$1\n-    branch=$1\n-\n-    if [ -z $env ]; then\n-        echo Specify env: prod, beta, alfa, master\n-        return 0\n-    fi\n-\n-    if [ $env != 'prod' ] && [ $env != 'beta' ] && [ $env != 'alfa' ] && [ $env != 'master' ]; then\n-        echo Incorrect env: prod, beta, alfa, master\n-        return 0\n-    fi\n-\n-    if [ $env = 'prod' ]; then\n-        random=$(cat /dev/urandom | tr -dc 'a-z' | fold -w 4 | head -n 1)\n-        read \"reply?IS THIS PROD!? ($random): \"\n-        if [[ $reply != $random ]]; then\n-            echo Canceling...\n-            return 0\n-        fi\n-    fi\n-\n-    git checkout $branch\n-    git push origin $branch:$branch\n-\n-    if [ $env != 'master' ]; then\n-        git push $env $branch:master;\n-    fi\n-\n-    echo 'Ready, remember to migrate!';\n-}\n-\n-\n-# EJECUCIONES ESPECIFICAS\n-memory_hack\n-\n-\n-# AGREGADOS AUTOMÁTICAMENTE\n-export NVM_DIR=\"$HOME/.nvm\"\n-[ -s \"$NVM_DIR/nvm.sh\" ] && \\. \"$NVM_DIR/nvm.sh\"  # This loads nvm\n-[ -s \"$NVM_DIR/bash_completion\" ] && \\. \"$NVM_DIR/bash_completion\"  # This loads nvm bash_completion\n-\n-# The next line updates PATH for the Google Cloud SDK.\n-if [ -f '/home/<USER>/google-cloud-sdk/path.zsh.inc' ]; then . '/home/<USER>/google-cloud-sdk/path.zsh.inc'; fi\n-\n-# The next line enables shell command completion for gcloud.\n-if [ -f '/home/<USER>/google-cloud-sdk/completion.zsh.inc' ]; then . '/home/<USER>/google-cloud-sdk/completion.zsh.inc'; fi\n-\n-LC_CTYPE=es_AR.UTF-8\n-LC_ALL=es_AR.UTF-8\n-LANG=es_AR.UTF-8\n # if [ -f \"/home/<USER>/.config/fabric/fabric-bootstrap.inc\" ]; then . \"/home/<USER>/.config/fabric/fabric-bootstrap.inc\"; fi\n\\ No newline at end of file\n"}, {"date": 1735563059298, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -157,8 +157,9 @@\n alias find=\"echo Mejor usa: fd\"\n alias nano=\"echo Mejor usa mcedit con e\"\n alias e=\"mcedit\"\n alias sz=\"source ~/.zshrc\"\n+alias rdiff=\"rsync -avnc --delete /home/<USER>/MEGA/ <EMAIL>:/home/<USER>/MEGA/\"\n alias rup=\"rsync -avz /home/<USER>/MEGA/ <EMAIL>:/home/<USER>/MEGA/\"\n alias rown=\"ssh <EMAIL> 'chown -R andresmaiden:www-data /home/<USER>/MEGA/BRAIN'\"\n alias rdown=\"rsync -avz <EMAIL>:/home/<USER>/MEGA/ /home/<USER>/MEGA/\"\n \n"}, {"date": 1739887221018, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -88,8 +88,9 @@\n export PATH=${PATH}:$HOME/Apps/diff-so-fancy\n export PATH=${PATH}:$HOME/.local/bin\n export PATH=${PATH}:/bin\n export PATH=${PATH}:/snap/bin\n+export PATH=${PATH}:$HOME/Apps/selenium\n \n # PATH PARA ANDROID, JAVA Y PHONEGAP\n export ANDROID_SDK_ROOT=$HOME/Apps/android-sdk-linux\n export PATH=${PATH}:$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$ANDROID_SDK_ROOT/cmdline-tools/tools/bin\n"}, {"date": 1739892281137, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -97,8 +97,9 @@\n export PATH=${PATH}:$HOME/Apps/android-sdk-linux/build-tools\n export PATH=${PATH}:$HOME/Apps/android-sdk-linux/sources\n export PATH=${PATH}:$HOME/Apps/android-sdk-linux/platforms\n export JAVA_HOME=\"/usr/lib/jvm/adoptopenjdk-8-hotspot-amd64\"\n+export USE_GKE_GCLOUD_AUTH_PLUGIN=True\n \n # Set personal aliases, overriding those provided by oh-my-zsh libs,\n alias services=\"cd $HOME/www/saasargentina/services\"\n alias acc=\"cd $HOME/www/saasargentina/services/acc\"\n"}, {"date": 1740751422805, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -134,9 +134,9 @@\n alias prod_saas=\"ssh ec2-user@saas-prod\"\n alias prod_saas_bastion=\"ssh -i ~/.ssh/desarrollo.pem <EMAIL>\"\n alias prod_hertruethis=\"ssh -i ~/.ssh/desarrollo.pem ec2-user@saas-hertruethis\"\n alias prod_crono=\"ssh <EMAIL>\"\n-alias prod_turismobtc=\"gcloud compute ssh produccion\"\n+alias prod_agente=\"gcloud compute ssh --zone 'us-central1-c' 'agente' --project 'cronometrajeinstantaneo-151420'\"\n alias prod_donweb=\"echo 'Qu9jfvVrU7YW' | xclip -selection c && ssh <EMAIL>\"\n \n alias hertruethis=echo\\ \"Contraseña Super Maestra Hertruethis\"\\ \\|\\ xclip\\ -sel\\ clip\n alias files=\"find . -type f \\| wc -l\"\n"}, {"date": 1745331577174, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -98,9 +98,11 @@\n export PATH=${PATH}:$HOME/Apps/android-sdk-linux/sources\n export PATH=${PATH}:$HOME/Apps/android-sdk-linux/platforms\n export JAVA_HOME=\"/usr/lib/jvm/adoptopenjdk-8-hotspot-amd64\"\n export USE_GKE_GCLOUD_AUTH_PLUGIN=True\n+export HISTIGNORE=\"ls:cd:exit\"\n \n+\n # Set personal aliases, overriding those provided by oh-my-zsh libs,\n alias services=\"cd $HOME/www/saasargentina/services\"\n alias acc=\"cd $HOME/www/saasargentina/services/acc\"\n alias api=\"cd $HOME/www/saasargentina/services/api\"\n"}, {"date": 1746651144126, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -165,9 +165,11 @@\n alias rdiff=\"rsync -avnc --delete /home/<USER>/MEGA/ <EMAIL>:/home/<USER>/MEGA/\"\n alias rup=\"rsync -avz /home/<USER>/MEGA/ <EMAIL>:/home/<USER>/MEGA/\"\n alias rown=\"ssh <EMAIL> 'chown -R andresmaiden:www-data /home/<USER>/MEGA/BRAIN'\"\n alias rdown=\"rsync -avz <EMAIL>:/home/<USER>/MEGA/ /home/<USER>/MEGA/\"\n+alias transcribir='source ~/Apps/transcribir/venv/bin/activate && python ~/Apps/transcribir/transcribir.py'\n \n+\n alias ai=\"php -f /home/<USER>/www/andresmisiak/ai/ai.php\"\n # alias f=\"fabric\"\n alias copy='xsel --clipboard --input'\n alias paste='xsel --clipboard --output'\n"}], "date": 1726426432178, "name": "Commit-0", "content": "# Path to your oh-my-zsh installation.\nexport ZSH=~/.oh-my-zsh\n\n# Set name of the theme to load.\n# Look in ~/.oh-my-zsh/themes/\n# Optionally, if you set this to \"random\", it'll load a random theme each\n# time that oh-my-zsh is loaded.\nZSH_THEME=\"robbyrussell\"\n\n# Uncomment the following line to use case-sensitive completion.\n# CASE_SENSITIVE=\"true\"\n\n# Uncomment the following line to use hyphen-insensitive completion. Case\n# sensitive completion must be off. _ and - will be interchangeable.\n# HYPHEN_INSENSITIVE=\"true\"\n\n# Uncomment the following line to disable bi-weekly auto-update checks.\n# DISABLE_AUTO_UPDATE=\"true\"\n\n# Uncomment the following line to change how often to auto-update (in days).\n# export UPDATE_ZSH_DAYS=13\n\n# Uncomment the following line to disable colors in ls.\n# DISABLE_LS_COLORS=\"true\"\n\n# Uncomment the following line to disable auto-setting terminal title.\n# DISABLE_AUTO_TITLE=\"true\"\n\n# Uncomment the following line to enable command auto-correction.\n# ENABLE_CORRECTION=\"true\"\n\n# Uncomment the following line to display red dots whilst waiting for completion.\n# COMPLETION_WAITING_DOTS=\"true\"\n\n# Uncomment the following line if you want to disable marking untracked files\n# under VCS as dirty. This makes repository status check for large repositories\n# much, much faster.\n# DISABLE_UNTRACKED_FILES_DIRTY=\"true\"\n\n# Uncomment the following line if you want to change the command execution time\n# stamp shown in the history command output.\n# The optional three formats: \"mm/dd/yyyy\"|\"dd.mm.yyyy\"|\"yyyy-mm-dd\"\n# HIST_STAMPS=\"mm/dd/yyyy\"\n\n# Would you like to use another custom folder than $ZSH/custom?\n# ZSH_CUSTOM=/path/to/new-custom-folder\n\n# Which plugins would you like to load? (plugins can be found in ~/.oh-my-zsh/plugins/*)\n# Custom plugins may be added to ~/.oh-my-zsh/custom/plugins/\n# Example format: plugins=(rails git textmate ruby lighthouse)\n# Add wisely, as too many plugins slow down shell startup.\n\nplugins=(\n    git autojump\n    zsh-autosuggestions\n    sudo\n    copypath\n    copyfile\n    copybuffer\n    dirhistory\n    history\n)\n[[ -s /home/<USER>/.autojump/etc/profile.d/autojump.sh ]] && source /home/<USER>/.autojump/etc/profile.d/autojump.sh\nautoload -U compinit && compinit -u\n\n\n# User configuration\nsource $ZSH/oh-my-zsh.sh\n\n# You may need to manually set your language environment\nexport LANG=en_US.UTF-8\n\n# Setting for the new UTF-8 terminal support in Lion\nLC_CTYPE=en_US.UTF-8\nLC_ALL=en_US.UTF-8\n\n\n# PATH VARIOS\nexport PATH=${PATH}:/usr/local/sbin\nexport PATH=${PATH}:/usr/local/bin\nexport PATH=${PATH}:/usr/sbin\nexport PATH=${PATH}:/usr/bin\nexport PATH=${PATH}:/sbin\nexport PATH=${PATH}:/bin\nexport PATH=${PATH}:/usr/games\nexport PATH=${PATH}:/usr/local/games\nexport PATH=${PATH}:$HOME/.composer/vendor/bin\nexport PATH=${PATH}:$HOME/Apps/diff-so-fancy\nexport PATH=${PATH}:$HOME/.local/bin\nexport PATH=${PATH}:/bin\nexport PATH=${PATH}:/snap/bin\n\n# PATH PARA ANDROID, JAVA Y PHONEGAP\nexport ANDROID_SDK_ROOT=$HOME/Apps/android-sdk-linux\nexport PATH=${PATH}:$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$ANDROID_SDK_ROOT/cmdline-tools/tools/bin\nexport PATH=${PATH}:$HOME/Apps/android-sdk-linux/build-tools\nexport PATH=${PATH}:$HOME/Apps/android-sdk-linux/sources\nexport PATH=${PATH}:$HOME/Apps/android-sdk-linux/platforms\nexport JAVA_HOME=\"/usr/lib/jvm/adoptopenjdk-8-hotspot-amd64\"\n\n# Set personal aliases, overriding those provided by oh-my-zsh libs,\nalias services=\"cd $HOME/www/saasargentina/services\"\nalias acc=\"cd $HOME/www/saasargentina/services/acc\"\nalias api=\"cd $HOME/www/saasargentina/services/api\"\nalias saas=\"cd $HOME/www/saasargentina/services/app\"\nalias informes=\"cd $HOME/www/saasargentina/services/informes\"\nalias login=\"cd $HOME/www/saasargentina/services/login\"\nalias scripts=\"cd $HOME/www/saasargentina/services/scripts\"\n\nalias crono=\"cd $HOME/www/cronometrajeinstantaneo/\"\nalias admin=\"cd $HOME/www/cronometrajeinstantaneo/admin\"\nalias app=\"cd $HOME/www/cronometrajeinstantaneo/app\"\nalias wp=\"cd $HOME/www/cronometrajeinstantaneo/wp\"\n\nalias www=\"cd $HOME/www\"\nalias desk=\"cd $HOME/desk\"\nalias woo=\"cd $HOME/www/woocommerce/wp-content/plugins\"\n\nalias gch=\"git checkout\"\nalias gst=\"git status\"\nalias gb=\"git branch\"\nalias gc=\"git commit -am\"\nalias gpo=\"git push origin\"\nalias gpl=\"git pull --rebase origin\"\nalias gpo=\"git push origin\"\nalias gm=\"git merge\"\nalias gd=\"git diff\"\nalias gdl=\"git diff HEAD~1 HEAD\"\nalias gl=\"git log --stat --pretty=oneline\"\nalias gfo=\"git fetch -p origin\"\n\nalias prod_saas=\"ssh ec2-user@saas-prod\"\nalias prod_saas_bastion=\"ssh -i ~/.ssh/desarrollo.pem <EMAIL>\"\nalias prod_hertruethis=\"ssh -i ~/.ssh/desarrollo.pem ec2-user@**************\"\nalias prod_crono=\"ssh -i <EMAIL>\"\nalias prod_turismobtc=\"gcloud compute ssh produccion\"\nalias prod_donweb=\"echo 'Qu9jfvVrU7YW' | xclip -selection c && ssh <EMAIL>\"\n\nalias hertruethis=echo\\ \"Contraseña Super Maestra Hertruethis\"\\ \\|\\ xclip\\ -sel\\ clip\nalias files=\"find . -type f \\| wc -l\"\nalias network=\"sudo service network-manager restart\"\nalias art=\"php artisan\"\nalias untar=\"tar -zxvf \"\nalias consumo=\"sudo nethogs -v 3\"\nalias mysql=\"sudo mysql -u root -p\"\n\nalias agi=\"sudo apt-get install\"\nalias aup=\"sudo apt-get update\"\nalias l=\"exa -lah\"\nalias prueba=\"code $HOME/www/prueba.php\"\nalias hacer=\"code ~/MEGA/HACER.todo\"\nalias hibernate=\"sudo systemctl hibernate -i\"\nalias bluetooth_restart=\"sudo systemctl restart bluetooth\"\nalias phpunit=\"./vendor/bin/phpunit\"\nalias tail_error=\"sudo tail /var/log/apache2/error.log\"\nalias minecraft=\"java -jar ~/Apps/Minecraft/TLauncher-2.82.jar\"\nalias find=\"echo Mejor usa: fd\"\nalias nano=\"echo Mejor usa mcedit con e\"\nalias e=\"mcedit\"\nalias sz=\"source ~/.zshrc\"\n\nalias ai=\"~/www/andresmaiden/ai/ai.php\"\nalias f=\"fabric\"\nalias copy='xsel --clipboard --input'\nalias paste='xsel --clipboard --output'\nfunction pattern () { code $HOME/.config/fabric/patterns/$1/system.md; }\nfunction fe () { echo $2 | fabric -sp $1; }\nrsync -a ~/MEGA/BRAIN/AI/* ~/.config/fabric/patterns\n\n# FUNCIONES VARIAS\nfunction gdiff () { git diff $1 --color | diff-so-fancy }\n\nfunction gh () {\n    if [ -e \"hash\" ]; then\n        commit=$(git rev-parse HEAD)\n        echo $commit > hash\n        git commit -am \"Update hash\"\n    else\n        echo \"Error: File 'hash' does not exist in the current directory.\"\n        return 1\n    fi\n}\n\nfunction saas_hash () {\n    echo \"Estoy usando gh de git hash ahora\"\n}\n\nfunction migrate () {acc; ./command.php migrate $1 $2 $3 $4 $5; cd -;}\nfunction nuevophp () {acc; ./command.php nuevophp $1; cd -;}\nfunction nuevosql () {acc; ./command.php nuevosql $1; cd -;}\nfunction dl () {\n    file=$1\n\n    if [ \"$file\" != 'api-ml' ] && [ \"$file\" != 'api-v1' ] && [ \"$file\" != 'crono' ] && [ \"$file\" != 'saas' ] && [ \"$file\" != 'laravel' ];\n    then\n        echo Incorrect file: api-ml, api-v1, crono, saas, laravel\n        return 0\n    fi\n\n    if [ -n \"$2\" ]\n    then\n        d=$2\n    else\n        d=$(date \"+%Y-%m-%d\")\n    fi\n\n    if [ \"$file\" = 'api-ml' ]\n    then\n        scp ec2-user@saas-prod:/saas/customer/services/acc/empresas/logs/api-ml/$d.log $HOME/SOPORTE/api-ml_$d.log\n        code $HOME/SOPORTE/api-ml_$d.log\n    fi\n\n    if [ \"$file\" = 'api-v1' ]\n    then\n        scp ec2-user@saas-prod:/saas/customer/services/acc/empresas/logs/api-v1/$d.log $HOME/SOPORTE/api-v1_$d.log\n        code $HOME/SOPORTE/api-v1_$d.log\n    fi\n\n    if [ \"$file\" = 'crono' ]\n    then\n        cd $HOME/SOPORTE\n        mv cronometrajeinstantaneo.{sql,old}\n        scp -i ~/.ssh/andresmaiden2 <EMAIL>:/home/<USER>/backups/backup.sql.gz cronometrajeinstantaneo.sql.gz\n        gzip -d cronometrajeinstantaneo.sql.gz\n        sudo mysql -u root -p cronometrajeinstantaneo < cronometrajeinstantaneo.sql\n    fi\n\n    if [ \"$file\" = 'saas' ]\n    then\n\n        if [ -n \"$2\" ]\n        then\n            id=$2\n            cd $HOME/SOPORTE\n            rm saas_$id.sql\n            archivo_saas=$(ssh ec2-user@saas-prod \"find /saas/customer/services/acc/backups -type f -name saas_${id}_* -print -quit\")\n            scp ec2-user@saas-prod:${archivo_saas} .\n            archivo_local=$(fd saas_${id})\n            mv ${archivo_local} saas_${id}.sql.gz\n            gzip -d saas_$id.sql.gz\n            sudo mysql -u root -p saas_${id} < saas_${id}.sql\n        else\n            echo Falta el idempresa\n        fi\n    fi\n\n    if [ \"$file\" = 'laravel' ]\n    then\n        mv $HOME/SOPORTE/laravel.{log,old}\n        scp -i ~/.ssh/andresmaiden2 <EMAIL>:/var/www/cronometrajeinstantaneo/admin/storage/logs/laravel.log $HOME/SOPORTE/laravel.log\n        code $HOME/SOPORTE/laravel.log\n    fi\n\n}\n\nfunction mkcd () {mkdir $1; cd $1;}\nfunction php7 () {\n    sudo a2dismod php8.3\n    sudo a2enmod php7.1\n    sudo service apache2 restart\n    sudo update-alternatives --set php /usr/bin/php7.1\n}\nfunction php8 () {\n    sudo a2dismod php7.1\n    sudo a2enmod php8.3\n    sudo service apache2 restart\n    sudo update-alternatives --set php /usr/bin/php8.3\n}\n\nfunction open() {\n    nohup nautilus -w $1 > /dev/null 2>&1 &\n}\n\nfunction memory_hack {\n    php -f ~/www/andresmaiden/tools/memory-hack.php\n}\n\nfunction phonegap_release () {\n    if [ -e platforms/android/app/build/outputs/apk/release/$1.keystore ]\n    then\n        b platforms/android/app/build/outputs/apk/release/key.txt\n        phonegap build --release android;\n        jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore platforms/android/app/build/outputs/apk/release/$1.keystore platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk $1\n        mv platforms/android/app/build/outputs/apk/release/app-release{,-old}.apk\n        ~/Apps/android-sdk-linux/build-tools/28.0.3/zipalign -v 4 platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk platforms/android/app/build/outputs/apk/release/app-release.apk\n    else\n        echo \"No existe el archivo platforms/android/app/build/outputs/apk/release/$1.keystore\"\n    fi\n}\n\nfunction crono_build_apk () {\n    cd $HOME/phonegap/cronometrajeinstantaneo;\n    phonegap build;\n    mv platforms/android/app/build/outputs/apk/debug/app-debug.apk $HOME/CRONO/descargas/cronometrajeinstantaneo-android-beta.apk;\n    scp -i ~/.ssh/andresmaiden $HOME/CRONO/descargas/Cronometraje\\ Instantaneo.debug.v2.9.8.apk <EMAIL>:/var/www/cronometrajeinstantaneo/www/descargas/cronometrajeinstantaneo.beta.v2.9.8.apk;\n    cd -;\n}\n\nfunction crono_build_linux () {\n    cd $HOME/www/cronometrajeinstantaneo/desktop;\n    npm run package-linux;\n    tar -zcvf release-builds/cronometrajeinstantaneo-linux-x64.tar.gz release-builds/cronometrajeinstantaneo-linux-x64\n    scp -i ~/.ssh/andresmaiden $HOME/www/cronometrajeinstantaneo/desktop/release-builds/cronometrajeinstantaneo-linux-x64.tar.gz <EMAIL>:$HOME/www/cronometrajeinstantaneo/public/descargas/cronometrajeinstantaneo-linux-x64.tar.gz;\n    cd -;\n}\n\nfunction crono_upload_descargas () {\n    file=$1\n    scp -i ~/.ssh/andresmaiden2 $file <EMAIL>:/var/www/cronometrajeinstantaneo/descargas/$file;\n}\n\nfunction crono_encoded () {\n    cd $HOME/www;\n    $HOME/Apps/ioncube_encoder5_basic_10.2/ioncube_encoder.sh -72 \\\n        --replace \\\n        --copy \"@/*/\" \\\n        --encode app/ \\\n        --encode bootstrap/ \\\n        --encode config/ \\\n        --encode old-includes/ \\\n        --encode public/ \\\n        --encode routes/ \\\n        --encode tests/ \\\n        --ignore .git/ \\\n        --ignore node_modules/ \\\n        --ignore database/ \\\n        cronometrajeinstantaneo/ -o www_encoded/;\n    random=$(b /dev/urandom | tr -dc 'a-z' | fold -w 4 | head -n 1);\n    tar -zcvf www_encoded_$random.tar.gz www_encoded;\n    rm -Rf www_encoded;\n    scp -i ~/.ssh/andresmaiden2 www_encoded_$random.tar.gz <EMAIL>:/var/www/cronometrajeinstantaneo/public/descargas/www_encoded_$random.tar.gz;\n    # scp www_encoded_$random.tar.gz pi@192.168.0.10:/home/<USER>/www_encoded_$random.tar.gz;\n    echo 'Descargar actualización y luego ELIMINAR EL ARCHIVO';\n    echo \"wget https://cronometrajeinstantaneo.com/descargas/www_encoded_$random.tar.gz\"\n    echo \"tar -zxvf www_encoded_$random.tar.gz\"\n    echo 'sudo mv /var/www/{,old}www_encoded'\n    echo 'sudo mv www_encoded /var/www'\n    echo 'chmod -R 775 /var/www/www_encoded/storage'\n    echo 'chmod -R 775 /var/www/www_encoded/bootstrap/cache'\n    cd -;\n}\n\nfunction crono_logo () {\n    scp -i ~/.ssh/andresmaiden2 $1 <EMAIL>:/var/www/cronometrajeinstantaneo/public/images/eventos/$1;\n}\n\nfunction art_clear () {\n    art cache:clear\n    art config:clear\n    art view:clear\n}\n\nfunction somoshinchada {\n    cd $HOME/www/mundial2018-vue;\n    git pull origin master;\n    git push origin master;\n    npm run build;\n    scp -r -i ~/.ssh/andresmaiden dist/* <EMAIL>:/var/www/somoshinchada/dist;\n    cd -;\n}\n\nfunction deploy () {\n\n    env=$1\n    branch=$1\n\n    if [ -z $env ]; then\n        echo Specify env: prod, beta, alfa, master\n        return 0\n    fi\n\n    if [ $env != 'prod' ] && [ $env != 'beta' ] && [ $env != 'alfa' ] && [ $env != 'master' ]; then\n        echo Incorrect env: prod, beta, alfa, master\n        return 0\n    fi\n\n    if [ $env = 'prod' ]; then\n        random=$(cat /dev/urandom | tr -dc 'a-z' | fold -w 4 | head -n 1)\n        read \"reply?IS THIS PROD!? ($random): \"\n        if [[ $reply != $random ]]; then\n            echo Canceling...\n            return 0\n        fi\n    fi\n\n    git checkout $branch\n    git push origin $branch:$branch\n\n    if [ $env != 'master' ]; then\n        git push $env $branch:master;\n    fi\n\n    echo 'Ready, remember to migrate!';\n}\n\n\n# EJECUCIONES ESPECIFICAS\nmemory_hack\n\n\n# AGREGADOS AUTOMÁTICAMENTE\nexport NVM_DIR=\"$HOME/.nvm\"\n[ -s \"$NVM_DIR/nvm.sh\" ] && \\. \"$NVM_DIR/nvm.sh\"  # This loads nvm\n[ -s \"$NVM_DIR/bash_completion\" ] && \\. \"$NVM_DIR/bash_completion\"  # This loads nvm bash_completion\n\n# The next line updates PATH for the Google Cloud SDK.\nif [ -f '/home/<USER>/google-cloud-sdk/path.zsh.inc' ]; then . '/home/<USER>/google-cloud-sdk/path.zsh.inc'; fi\n\n# The next line enables shell command completion for gcloud.\nif [ -f '/home/<USER>/google-cloud-sdk/completion.zsh.inc' ]; then . '/home/<USER>/google-cloud-sdk/completion.zsh.inc'; fi\n\nLC_CTYPE=es_AR.UTF-8\nLC_ALL=es_AR.UTF-8\nLANG=es_AR.UTF-8\nif [ -f \"/home/<USER>/.config/fabric/fabric-bootstrap.inc\" ]; then . \"/home/<USER>/.config/fabric/fabric-bootstrap.inc\"; fi"}]}