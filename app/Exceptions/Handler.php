<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            // Skip reporting Livewire hydration errors for streaming-meta component
            if (str_contains($e->getMessage(), 'Livewire encountered corrupt data when trying to hydrate the [streaming-meta] component')) {
                return;
            }

            if (app()->bound('sentry')) {
                app('sentry')->captureException($e);
            }
        });
    }
}
