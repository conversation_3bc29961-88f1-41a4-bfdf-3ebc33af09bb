<?php

namespace App\Exports;

use DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Carbon\Carbon;

use App\Models\Lecturas;
use App\Models\Controles;

class LecturasExport implements FromCollection, WithHeadings
{
    private $controles = [];

    public function __construct()
    {
        $this->controles = Controles::select('idcontrol', 'codigo')
            ->where('idevento', session('idevento'))
            ->get()
            ->mapWithKeys(function($control) {
                return [$control->idcontrol => $control->codigo];
            })
            ->toArray();
    }

    public function headings(): array
    {
        return ['Control', 'Número', 'Crono', 'Día', 'Hora', 'Horas', 'Minutos', 'Segundos', 'Milisegundos'];
    }

    public function collection()
    {
        $lecturas = Lecturas::select('idcontrol', 'idparticipante', 'tiempo')
                ->where('idevento', session('idevento'))
                ->where('estado', '!=', 'eliminado')
                ->get()
                ->sortBy('tiempo');

        $lecturas->transform(function($lectura) {
            $lectura->idcontrol = $this->controles[$lectura->idcontrol];
            $lectura->dia = Carbon::parse($lectura->tiempo)->format('Y-m-d');
            $lectura->hora = Carbon::parse($lectura->tiempo)->format('H:i:s.v');
            $lectura->horas = Carbon::parse($lectura->tiempo)->format('H');
            $lectura->minutos = Carbon::parse($lectura->tiempo)->format('i');
            $lectura->segundos = Carbon::parse($lectura->tiempo)->format('s');
            $lectura->milisegundos = Carbon::parse($lectura->tiempo)->format('v');

            return $lectura;
        });

        return $lecturas;
    }

}
