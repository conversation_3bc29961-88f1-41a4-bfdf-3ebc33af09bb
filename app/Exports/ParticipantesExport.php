<?php

namespace App\Exports;

use App\Models\ConfigOrganizacion;
use App\Models\ConfigCronometraje;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

use App\Models\Datos;
use App\Models\Datosxparticipantes;
use App\Models\Participantes;

class ParticipantesExport implements FromCollection, WithHeadings
{
    private $encabezado_dinamico = array();
    private $tipo_nombre = '';

    public function __construct()
    {
        $this->encabezado_dinamico = Datos::where('idevento', session('idevento'))
                                    ->join('datosxeventos', 'datosxeventos.iddato', '=', 'datos.iddato')
                                    ->orderBy('datosxeventos.iddato')
                                    ->pluck('nombre')
                                    ->toArray();

        $this->tipo_nombre = ConfigOrganizacion::where('idevento', session('idevento'))->value('tipo_nombre');
        $this->tags_descartables = ConfigCronometraje::where('idevento', session('idevento'))->value('tags_descartables');

    }

    public function headings(): array
    {
        return array_merge(
            ['ID', 'Número', 'Chip', 'Estado', 'Estado Carrera', 'Nombre'],
            ($this->tipo_nombre == 'Nombre y Apellido separados' ? ['Apellido'] : []),
            ['Mail', 'Categoría', 'Localidad', 'Observacion', 'Ficha', 'Pago'],
            $this->encabezado_dinamico);
    }

    public function collection()
    {
        $campos = array_merge(
            ['participantes.idinscripcion as id', 'idparticipante'],
            ($this->tags_descartables ? ['tag'] : ['tags.codigo']),
            ['participantes.estado', 'estado_carrera', 'participantes.nombre'],
            ($this->tipo_nombre == 'Nombre y Apellido separados' ? ['apellido'] : []),
            ['mail', 'categorias.nombre AS categoria', 'localidad', 'participantes.observacion', 'estado_pago', 'estado_ficha']);
        $participantes = Participantes::select($campos)
                    ->leftJoin('categorias', 'participantes.idcategoria', '=', 'categorias.idcategoria')
                    ->leftJoin('tags', 'participantes.idinscripcion', '=', 'tags.idinscripcion')
                    ->where('participantes.idevento', session('idevento'))
                    ->where('participantes.estado', '!=', 'eliminado')
                    ->orderBy('participantes.idparticipante')
                    ->get();

        $datosxparticipantes = Datosxparticipantes::select('idinscripcion', 'iddato', 'dato')
                    ->whereIn('idinscripcion', $participantes->pluck('id'))
                    ->get();

        $datosxparticipantes_array = $datosxparticipantes->mapToGroups(function ($item, $key) {
            return [$item['idinscripcion'] => $item];
        });

        $participantes_full = $participantes->each(function ($participante) use ($datosxparticipantes_array) {
            if (isset($datosxparticipantes_array[$participante->id])) {
                foreach($datosxparticipantes_array[$participante->id] as $dxp) {
                    $participante->{$dxp->iddato} = $dxp->dato;
                }
                unset($participante->idinscripcion);
            }
        });

        return $participantes_full;
    }

}
