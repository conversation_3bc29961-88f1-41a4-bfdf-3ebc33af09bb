<?php

namespace App\Actions\Fortify;

use App\Models\Organizaciones;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Lara<PERSON>\Fortify\Contracts\CreatesNewUsers;
use Lara<PERSON>\Jetstream\Jetstream;
use Illuminate\Support\Str;

class CreateNewUser implements CreatesNewUsers
{
    use PasswordValidationRules;

    /**
     * Validate and create a newly registered user.
     *
     * @param  array  $input
     * @return \App\Models\User
     */
    public function create(array $input)
    {
        Validator::make($input, [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => $this->passwordRules(),
            'terms' => Jetstream::hasTermsAndPrivacyPolicyFeature() ? ['required', 'accepted'] : '',
        ])->validate();

        $organizacion = Organizaciones::create([
            'uuid' => Str::uuid()->toString(),
            'nombre' => $input['name'],
            'mail' => $input['email'],
            'pass' => Hash::make($input['password']),
            'estado' => 1
        ]);

        $user = User::create([
            'name' => $input['name'],
            'email' => $input['email'],
            'password' => Hash::make($input['password']),
            'organizacion_uuid' => $organizacion->uuid
        ]);

        return $user;
    }
}
