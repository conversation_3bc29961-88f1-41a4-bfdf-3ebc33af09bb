<?php

namespace App\Filament\Inicio\Resources;

use Filament\Resources\Resource;
use App\Filament\Inicio\Resources\OrganizacionResource\Pages;

use App\Filament\Inicio\Resources\OrganizacionResource\RelationManagers\ContactosRelationManager;

use Filament\Infolists\Infolist;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Components\Section;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Section as FormsSection;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Textarea;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Columns\CheckboxColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;

use Filament\Support\Enums\FontWeight;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;

use App\Models\Contacto;
use App\Models\Organizacion;
use App\Models\User;
use App\Traits\HasResourceAuthorization;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use App\Events\OrganizacionCreated;

class OrganizacionResource extends Resource
{
    use HasResourceAuthorization;

    protected static ?string $model = Organizacion::class;

    protected static ?string $slug = 'organizadores';

    protected static ?string $modelLabel = 'Organizador';

    protected static ?string $pluralModelLabel = 'Organizadores';

    protected static ?string $navigationLabel = 'Organizadores';

    protected static ?string $navigationIcon = 'heroicon-s-users';

    protected static ?int $navigationSort = 3;


    public static function getRelations(): array
    {
        return [
            ContactosRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrganizacion::route('/'),
            'create' => Pages\CreateOrganizacion::route('/create'),
            'view' => Pages\ViewOrganizacion::route('/{record}'),
            'edit' => Pages\EditOrganizacion::route('/{record}/edit'),
        ];
    }

    public static function canViewAny(): bool
    {
        return auth()->check(); // Cualquier usuario autenticado puede ver la lista
    }

    public static function canCreate(): bool
    {
        return auth()->check() && in_array(auth()->user()->tipo, ['cronometrador', 'super_admin', 'admin', 'local_admin']);
    }

    public static function canEdit(Model $record): bool
    {
        return HasResourceAuthorization::canEdit($record);
    }

    public static function canView(Model $record): bool
    {
        return static::canEdit($record); // Solo pueden ver el detalle si pueden editar
    }

    public static function canDelete(Model $record): bool
    {
        return false; // Deshabilitamos completamente la funcionalidad de borrado
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(function () {
                if (auth()->user()->is_admin()) {
                    return Organizacion::query()
                        ->where('organiza', 1);
                }

                if (auth()->user()->tipo == 'local_admin') {
                    return Organizacion::query()
                        ->where('organiza', 1)
                        ->where('idpais', auth()->user()->idpais);
                }

                // Usuarios normales solo ven sus organizaciones
                return Organizacion::query()
                    ->where(function ($query) {
                        $query->where('idorganizacion', auth()->id())
                              ->orWhere('idcronometrador', auth()->id());
                    })
                    ->where('organiza', 1)
                    ->where('estado', 1);
            })
            ->recordUrl(fn(Organizacion $record): string => route('filament.inicio.resources.organizadores.view', ['record' => $record]))
            ->description('Estas son tus organizaciones de eventos deportivos')
            ->columns([
                IconColumn::make('estado')
                    ->boolean()
                    ->label(__('Estado'))
                    ->disabled()
                    ->visible(auth()->user()->is_admin()),
                TextColumn::make('idorganizacion')
                    ->label(__('ID'))
                    ->visible(fn () => auth()->user()->is_admin()),
                TextColumn::make('nombre')
                    ->label(__('Nombre'))
                    ->sortable()
                    ->searchable()
                    ->weight(\Filament\Support\Enums\FontWeight::Bold)
                    ->description(function (Organizacion $organizacion) {
                        if (auth()->user()->is_admin())
                            return $organizacion->obsinterna ?? '';
                    })->wrap(),
                TextColumn::make('zona')
                    ->label(__('Zona'))
                    ->searchable()
                    ->visible(fn () => auth()->user()->is_all_admin()),
                TextColumn::make('mail')
                    ->searchable(),
                TextColumn::make('cronometrador.nombre')
                    ->label(__('Cronometrador')),
                TextColumn::make('pais.nombre_es')
                    ->searchable(),
            ])
            ->filters([
                SelectFilter::make('idpais')
                    ->relationship(name: 'pais', titleAttribute: 'nombre_es')
                    ->label(__('Pais'))
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                // Quitamos todos los botones de acción de la tabla
            ])
            ->bulkActions([
            ])
            ->deferLoading()
            ->persistFiltersInSession()
            // ->emptyState(view('tables.organizaciones.empty-state'))
            ->emptyStateHeading('No tienes ninguna organización de eventos deportivos')
            ->emptyStateDescription('Agrega a tu organización de eventos deportivos potenciando tu trabajo como organizador al máximo')
            ->emptyStateActions([
                Action::make('create')
                    ->url(fn () => static::getUrl('create'))
                    ->label(__('Crea tu primera organización'))
                    ->icon('heroicon-m-plus')
                    ->button(),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Datos básicos')
                    ->schema([
                        TextEntry::make('nombre')
                            ->label('Nombre'),
                        TextEntry::make('zona')
                            ->label('Zona')
                            ->visible(auth()->user()->is_all_admin()),
                        TextEntry::make('mail')
                            ->label('Email'),
                        TextEntry::make('pais.nombre_es')
                            ->label('País'),
                        TextEntry::make('user.name')
                            ->label('Usuario propietario')
                            ->visible(auth()->user()->is_all_admin()),
                        TextEntry::make('cronometrador.name')
                            ->label(__('Cronometrador')),
                        TextEntry::make('descripcion')
                            ->html()
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Section::make('Permisos de Organizadores')
                    ->schema([
                        TextEntry::make('estado')
                            ->label('Organizador habilitado')
                            ->formatStateUsing(fn ($state) => $state ? 'Sí' : 'No'),
                        TextEntry::make('organiza')
                            ->label('Organiza eventos')
                            ->formatStateUsing(fn ($state) => $state ? 'Sí' : 'No'),
                        TextEntry::make('cronometra')
                            ->label('Cronometra eventos')
                            ->formatStateUsing(fn ($state) => $state ? 'Sí' : 'No'),
                        TextEntry::make('obsinterna')
                            ->label('Observaciones internas')
                            ->columnSpanFull(),
                    ])
                    ->icon('heroicon-m-lock-closed')
                    ->columns(4)
                    ->visible(auth()->user()->is_all_admin()),
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('nombre')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->live(),
                TextInput::make('zona')
                    ->label(__('Zona'))
                    ->maxLength(100)
                    ->visible(fn () => auth()->user()->is_all_admin()),
                Select::make('idpais')
                    ->required()
                    ->relationship(name: 'pais', titleAttribute: 'nombre_es')
                    ->label(__('Pais'))
                    ->searchable()
                    ->preload(),
                TextInput::make('mail')
                    ->email()
                    ->required()
                    ->unique(Organizacion::class, 'mail', ignoreRecord: true)
                    ->live(),
                TextInput::make('password')
                    ->label('Contraseña para el Usuario')
                    ->required()
                    ->password()
                    ->revealable()
                    ->visibleOn('create'),
                Select::make('idcronometrador')
                    ->label('Cronometrador')
                    ->options(function (string $operation) {
                        $options = Organizacion::query()
                            ->where('cronometra', 1)
                            ->where('estado', 1)
                            ->orderBy('nombre')
                            ->pluck('nombre', 'idorganizacion')
                            ->prepend('Sin cronometrador', 0);

                        return $options;
                    })
                    ->default(0)
                    ->helperText('Seleccionar sólo si es un cliente de un cronometrador')
                    ->visible(auth()->user()->is_all_admin()),
                RichEditor::make('descripcion')
                    ->label(__('Descripción'))
                    ->columnSpan([
                        'lg' => 'full',
                        '2xl' => 2,
                    ]),
                FormsSection::make('Permisos de Organizadores')
                    ->description('Configurable sólo por administradores')
                    ->icon('heroicon-m-lock-closed')
                    ->columns(4)
                    ->schema([
                        Checkbox::make('estado')
                            ->label(__('Organizador habilitado'))
                            ->default(true),
                        Checkbox::make('organiza')
                            ->label(__('Organiza eventos'))
                            ->default(true),
                        Checkbox::make('cronometra')
                            ->label(__('Cronometra eventos'))
                            ->default(false),
                        Textarea::make('obsinterna')
                            ->label(__('Observaciones internas'))
                            ->columnSpan([
                                'lg' => 'full',
                                '2xl' => 2,
                            ]),
                    ])
                    ->visible(auth()->user()->is_all_admin()),
            ]);
    }

}
