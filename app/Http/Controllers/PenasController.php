<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;

use App\Models\Carreras;
use App\Models\Etapas;
use App\Models\Controles;
use App\Models\Penas;

use App\Helpers\FuncionesComunes;

class PenasController extends Controller
{
    public function store(Request $request)
    {
        $idevento = session('idevento');
        // if (!$idevento)
        //     return response()->json(['errors' => ['La sesión se venció, debe Salir y volver a Iniciar sesión']], 400);

        if (!$request->idparticipante && !$request->tiempo)
            return response()->json(['errors' => ['El N° y los segundos son obligatorios']], 400);

        if (!$request->idetapa && !$request->idcontrol && !$request->codigo)
            return response()->json(['errors' => ['Es necesario seleccionar una etapa o un control']], 400);

        if ($request->idcontrol && is_numeric($request->idcontrol)) {
            $control = Controles::where([
                'idcontrol' => $request->idcontrol,
                'tipo' => 'penas',
            ])->firstOrFail();
            $etapa = Etapas::where([
                'idetapa' => $control->idetapa,
            ])->firstOrFail();

        } else if ($request->codigo) {
            $control = Controles::where([
                'codigo' => $request->codigo,
                'tipo' => 'penas',
            ])->firstOrFail();
            $etapa = Etapas::where([
                'idetapa' => $control->idetapa,
            ])->firstOrFail();

        } else {
            $etapa = Etapas::where([
                'idetapa' => $request->idetapa,
            ])
            ->whereRaw("idcarrera IN (SELECT idcarrera FROM categorias WHERE
                idcategoria = (SELECT idcategoria FROM participantes
                    WHERE participantes.idevento = etapas.idevento
                        AND idparticipante = '{$request->idparticipante}'
                        AND estado != 'eliminado'
                        LIMIT 1))")
            ->first();
            if (!$etapa)
                return response()->json(['errors' => ["La etapa seleccionada no corresponde al participante {$request->idparticipante}"]], 400);
            $control = 0;
        }

        if ($control && is_numeric($control->idevento) && $control->idevento > 0)
            $idevento = $control->idevento;
        else if ($etapa->idevento && $etapa->idevento)
            $idevento = $etapa->idevento;
        else
            $idevento = 0;

        if ($request->idparticipante && is_numeric($request->idparticipante)) {
            $participante = DB::select(DB::raw(
                "SELECT nombre FROM participantes
                WHERE idparticipante = '{$request->idparticipante}'
                    AND estado != 'eliminado'
                    AND idevento = (SELECT idevento FROM carreras
                        WHERE idcarrera = '{$etapa['idcarrera']}')"));

            $nombre = count($participante)
                ? $participante[0]->nombre
                : '';
        } else {
            $nombre = '';
        }

        $carrera = Carreras::where([
                'idcarrera' => $etapa->idcarrera,
            ])->firstOrFail();

        // if ($carrera['idevento'] != $idevento)
        //     return response()->json(['errors' => ['Error en el id de la etapa']], 400);

        if ($request->idcontrol && is_numeric($request->idcontrol)
            && count(DB::select(DB::raw(
            "SELECT idpena FROM penas
            WHERE idparticipante = '{$request->idparticipante}'
            AND idcontrol = '{$control->idcontrol}'")))) {

            $pena = DB::select(DB::raw(
                "SELECT idpena FROM penas
                WHERE idparticipante = '{$request->idparticipante}'
                AND idcontrol = '{$control->idcontrol}'"))[0];

            DB::statement(
                "UPDATE penas
                SET tiempo = '{$request->tiempo}'
                WHERE idpena = '{$pena->idpena}'");

        } else {
            $pena = Penas::create([
                'idevento' => $idevento,
                'idetapa' => $etapa->idetapa,
                'idcontrol' => ($control->idcontrol ?? 0),
                'idparticipante' => $request->idparticipante,
                'tiempo' => ($request->tiempo ?? 0),
                'observacion' => $request->observacion,
            ]);
        }

        FuncionesComunes::cache(
            DB::table('eventos')->where('idevento', '=', $idevento)->value('codigo'));

        return [
            'idpena' => $pena->idpena,
            'idetapa' => $etapa->idetapa,
            'idcontrol' => ($control->idcontrol ?? 0),
            'idparticipante' => $request->idparticipante,
            'tiempo' => $request->tiempo,
            'observacion' => $request->observacion,
            'nombre' => $nombre,
        ];
    }

    public function update(Request $request, $id)
    {
        $idevento = session('idevento');
        if (!$idevento)
            return response()->json(['errors' => ['La sesión se venció, debe Salir y volver a Iniciar sesión']], 400);

        if (!$request->idetapa && !$request->idcontrol)
            return response()->json(['errors' => ['Es necesario seleccionar una etapa o un control']], 400);

        $pena = Penas::where([
                'idpena' => $id,
            ])->firstOrFail();

        if ($request->idcontrol && is_numeric($request->idcontrol)) {
            $control = Controles::where([
                'idcontrol' => $request->idcontrol,
                'tipo' => 'penas',
            ])->firstOrFail();
            $etapa = Etapas::where([
                'idetapa' => $control->idetapa,
            ])->firstOrFail();

        } else {
            $etapa = Etapas::where([
                'idetapa' => $request->idetapa,
            ])->firstOrFail();
        }

        $carrera = Carreras::where([
                'idcarrera' => $etapa->idcarrera,
            ])->firstOrFail();

        if ($carrera['idevento'] != $idevento)
            return response()->json(['errors' => ['Error en el id de la pena']], 400);


        $pena->idetapa = $etapa->idetapa;
        $pena->idcontrol = $request->idcontrol ? $request->idcontrol : 0;
        $pena->idparticipante = $request->idparticipante;
        $pena->tiempo = $request->tiempo;
        $pena->observacion = $request->observacion;
        $pena->save();

        FuncionesComunes::cache(session('codigo'));

        return $pena;
    }

    public function destroy($id)
    {
        $idevento = session('idevento');
        if (!$idevento)
            return response()->json(['errors' => ['La sesión se venció, debe Salir y volver a Iniciar sesión']], 400);

        $pena = Penas::where([
                'idpena' => $id,
            ])->firstOrFail();

        $etapa = Etapas::where([
                'idetapa' => $pena->idetapa,
            ])->firstOrFail();

        $carrera = Carreras::where([
                'idcarrera' => $etapa->idcarrera,
            ])->firstOrFail();

        if ($carrera['idevento'] != $idevento)
            return response()->json(['errors' => ['Error en el id de la pena']], 400);


        $pena->delete();
        FuncionesComunes::cache(session('codigo'));

        return response()->json(null, 204);
    }
}
