<?php

namespace App\Http\Controllers;

use App\Models\Eventos;
use App\Models\Organizaciones;

class SeleccionarController extends Controller
{
    public function index ($idevento = 0)
    {
        if (!$idevento)
            return redirect()->route('home');

        if (auth()->user()->is_all_admin()) {
            $evento = Eventos::where('idevento', $idevento)->first();

        } else {
            $evento = Eventos::query()
                ->select('eventos.*')
                ->where('idevento', $idevento)
                ->where('eventos.estado', '!=', 'cancelado')
                ->where(function ($query) {
                    $query->where('eventos.user_id', auth()->id())
                        ->orWhere('eventos.idorganizacion', auth()->id())
                        ->orWhere('eventos.idcronometrador', auth()->id());
                })
                ->first();
        }

        if (!$evento)
            return redirect('logout');

        if (!$evento->mail)
            $evento->mail = Organizaciones::select('mail')->where('idorganizacion', '=', $evento->idorganizacion)->firstOrFail()->mail;

        session([
            'idevento'        => $evento->idevento,
            'idorganizacion'  => $evento->idorganizacion,
            'idcronometrador' => $evento->idcronometrador,
            'nombre'          => $evento->nombre,
            'codigo'          => $evento->codigo,
            'mail'            => $evento->mail,
            'idpais'          => $evento->idpais,
        ]);

        return redirect('inicio');
    }
}
