<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\Lecturas;

class SyncController extends Controller
{

    public function lecturas_down(Request $request)
    {
        if (!is_numeric($request->idcontrol))
            abort(400);

        $lecturas = Lecturas::where('idcontrol', '=', $request->idcontrol)->get();

        return $lecturas;
    }

    public function lecturas_up(Request $request)
    {
        if (isset($request->lecturas) && count($request->lecturas))
            Lecturas::upsert($request->lecturas, ['uuid']);
    }

}
