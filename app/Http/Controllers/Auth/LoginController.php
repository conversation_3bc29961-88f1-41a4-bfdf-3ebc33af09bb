<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

use App\Models\User;
use App\Models\Organizaciones;
use App\Models\SocialProfile;
use App\Models\Eventos;

class LoginController extends Controller
{
    /**
     * Redirect the user to the GitHub authentication page.
     *
     * @return \Illuminate\Http\Response
     */
    public function redirectToProvider(string $driver)
    {
        // Intentos de hackeo
        if ($driver && $driver != 'google' && $driver != 'facebook')
            abort(404);

        return Socialite::driver($driver)->redirect();
    }

    /**
     * Obtain the user information from GitHub.
     *
     * @return \Illuminate\Http\Response
     */
    public function handleProviderCallback(Request $request, string $driver)
    {
        if ($request->get('error')) {
            return redirect()->route('login');
        }

        $userSocialite = Socialite::driver($driver)->stateless()->user();
        $organizacion = Organizaciones::where('mail', $userSocialite->getEmail())->first();
        $user = User::where('email', $userSocialite->getEmail())->first();
        $socialProfile = SocialProfile::where('social_id', $userSocialite->getId())
                                        ->where('social_name', $driver)
                                        ->first();

        if (!$organizacion) {
            // $organizacion = Organizaciones::create([
            //     'uuid' => Str::uuid()->toString(),
            //     'nombre' => $userSocialite->getEmail(),
            //     'mail' => $userSocialite->getEmail(),
            //     'estado' => 1
            // ]);
        }

        if (!$user) {
            $user = User::create([
                'name' => $userSocialite->getName(),
                'email' => $userSocialite->getEmail(),
                //'organizacion_uuid' => $organizacion->uuid
            ]);
        }

        if (!$socialProfile){
            SocialProfile::create([
                'user_id' => $user->id,
                'social_id' => $userSocialite->getId(),
                'social_name' => $driver,
                'social_avatar' => $userSocialite->getAvatar()
            ]);
        }

        auth()->login($user);

        return redirect(env('APP_URL').'/historial');

    }
}
