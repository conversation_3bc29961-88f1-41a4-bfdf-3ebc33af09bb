<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;

use App\Models\Eventos;

class StreamingController extends Controller
{
    public $idevento;
    public $idcarrera;
    public $idcategoria;
    public $idetapa;
    public $prelargada;
    public $prellegada;
    public $largada;
    public $llegada;
    public $parciales;

    public function index(Request $request)
    {
        $evento = Eventos::where(['codigo' => $request->codigo])->firstOrFail();
        if (!$evento->vivo)
            abort(403);

        // TODO: Hacer la consulta más al estilo Laravel
        $config_vivo = DB::table('config_vivo')->where('idevento', '=', $evento->idevento)->get()[0];
        if (!$config_vivo->streaming)
            return 'Los widgets de streaming se encuentran apagados';

        $reloj = $podio = $vivo = $meta = false;
        $soloreloj = $soloparticipante = false;
        switch ($request->widget) {
            default: abort(404); break;
            case 'locutor':
                $view = 'vivos.locutor';
                $reloj = $podio = $vivo = $meta = true;
                break;
            case 'pantalla':
                $view = 'vivos.pantalla';
                $reloj = $podio = $vivo = true;
                break;
            case 'reloj':
                $view = 'vivos.widgets';
                $reloj = true;
                break;
            case 'solo-reloj':
                $view = 'vivos.widgets';
                $reloj = $soloreloj = true;
                break;
            case 'solo-participante':
                $view = 'vivos.widgets';
                $reloj = $soloparticipante = true;
                break;
            case 'vivo':
                $view = 'vivos.widgets';
                $vivo = true;
                break;
            case 'meta':
                $view = 'vivos.widgets';
                $meta = true;
                break;
            case 'podio':
                $view = 'vivos.widgets';
                $podio = true;
                break;
        }

        $this->idevento = $evento->idevento;
        $this->idcarrera = $request->idcarrera;
        $this->idcategoria = $request->idcategoria;
        $this->idetapa = $request->idetapa;
        $this->prelargada = $request->prelargada;
        $this->prellegada = $request->prellegada;
        $this->largada = $request->largada;
        $this->llegada = $request->llegada;
        $this->parciales = $request->parciales;

        return view($view, [
            'idevento' => $this->idevento,
            'idcarrera' => $this->idcarrera,
            'idcategoria' => $this->idcategoria,
            'idetapa' => $this->idetapa,
            'prelargada' => $this->prelargada,
            'prellegada' => $this->prellegada,
            'largada' => $this->largada,
            'llegada' => $this->llegada,
            'parciales' => $this->parciales,
            'diff' => $config_vivo->diff,

            'reloj' => $reloj,
            'soloreloj' => $soloreloj,
            'soloparticipante' => $soloparticipante,
            'podio' => $podio,
            'vivo' => $vivo,
            'meta' => $meta,

            'cantidad' => $request->cantidad,
            'desde' => $request->desde,
            'css' => $evento->streaming_estilo,
        ]);
    }
}
