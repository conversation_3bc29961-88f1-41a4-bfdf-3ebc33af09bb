<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use DB;

use App\Models\Precios;
use App\Models\Eventos;
use App\Http\Controllers\AdminController;

class PreciosController extends Controller
{
    public function index()
    {
        $idevento = session('idevento');

        $categorias = DB::select(DB::raw("SELECT categorias.idcategoria, categorias.nombre, categorias.nacimiento_desde, categorias.nacimiento_hasta, precios.*
            FROM precios
            LEFT JOIN preciosxcategorias ON precios.idprecio = preciosxcategorias.idprecio AND precios.idprecio = preciosxcategorias.idprecio
            LEFT JOIN categorias ON preciosxcategorias.idcategoria = categorias.idcategoria AND preciosxcategorias.idprecio = precios.idprecio
            INNER JOIN carreras ON categorias.idcarrera = carreras.idcarrera
            WHERE carreras.idevento = '$idevento'
            ORDER BY categorias.nombre
        "));

        $precios = DB::select(DB::raw("SELECT precios.*
            FROM precios
            LEFT JOIN preciosxcategorias ON precios.idprecio = preciosxcategorias.idprecio AND precios.idprecio = preciosxcategorias.idprecio
            LEFT JOIN categorias ON preciosxcategorias.idcategoria = categorias.idcategoria AND preciosxcategorias.idprecio = precios.idprecio
            INNER JOIN carreras ON categorias.idcarrera = carreras.idcarrera
            WHERE carreras.idevento = '$idevento'
            GROUP BY idprecio
        "));


        $view = 'precios';
        $title = 'Precios';
        $site = 'precios';

        $admin = new AdminController();
        $config_header = $admin->config_header($idevento);
        $pasos = $admin->pasos('inscripciones.3', $config_header['evento'], $config_header['cantidades']);

        $categorias_all = DB::select(DB::raw(
            "SELECT idcategoria, c.idcarrera, c.nombre, equipo, nacimiento_desde, nacimiento_hasta
            FROM categorias AS c
                INNER JOIN carreras ON c.idcarrera = carreras.idcarrera
            WHERE carreras.idevento = '$idevento'
            ORDER BY c.nombre"));

        $formasdepago = array('efectivo' => 'Efectivo', 'transferencia' => 'Transferencia', 'payu' => 'PayU', 'paypal' => 'Paypal', 'mercadopago' => 'MercadoPago');

        return view($view, [
            'site'          => 'crono',
            'title'         => $title,
            'view'          => $view,
            'datos'         => $precios,
            'idevento'      => $idevento,
            'enlaces_config'=> $config_header['enlaces_config'],
            'etapas'        => $config_header['etapas'],
            'cantidades'    => $config_header['cantidades'],
            'evento'        => $config_header['evento'],
            'pasos'         => $pasos,
            'categorias'    => $categorias,
            'categorias_all'=> $categorias_all,
            'formasdepago'  => $formasdepago,
        ]);
    }

    public function store(Request $request)
    {
        $idevento = session('idevento');
        if (!$idevento)
            return response()->json(['errors' => ['La sesión se venció, debe Salir y volver a Iniciar sesión']], 400);

        //ver otras validaciones
        $request->validate([
            'formadepago' => ['required'],
        ]);

        //Pasé un par de horas y no pude traelo "de una", así que tuve que recurrir al viejo explode
        $unserialized = explode('&', $request->datos);
        $fecha_desde =  implode("", explode('fecha_desde=', $unserialized[0]));
        $fecha_hasta = implode("", explode('fecha_hasta=', $unserialized[1]));
        $precio = implode("", explode('precio=', $unserialized[2]));
        $cantidad = implode("", explode('cantidad=', $unserialized[3]));

        $precios = Precios::create([
            'fecha_desde' => $fecha_desde,
            'fecha_hasta' => $fecha_hasta,
            'precio' => $precio,
            'cantidad' => $cantidad,
            'efectivo' => (in_array("efectivo", $request->formadepago) ? '1' : '0'),
            'transferencia' => (in_array("transferencia", $request->formadepago) ? '1' : '0'),
            'paypal' => (in_array("paypal", $request->formadepago) ? '1' : '0'),
            'payu' => (in_array("payu", $request->formadepago) ? '1' : '0'),
            'mercadopago' => (in_array("mercadopago", $request->formadepago) ? '1' : '0'),
        ]);

        foreach ($request->categorias as $idcategoria) {
            DB::select(DB::raw("INSERT INTO preciosxcategorias SET
                idprecio = '".$precios->idprecio."',
                idcategoria = '".$idcategoria."'
            "));
        }

        return $precios;
    }

    public function show($id)
    {
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'formadepago' => ['required'],
        ]);

        $idevento = session('idevento');

        $precios_db = DB::select(DB::raw("SELECT precios.idprecio, preciosxcategorias.idcategoria
            FROM precios
            LEFT JOIN preciosxcategorias ON precios.idprecio = preciosxcategorias.idprecio AND precios.idprecio = preciosxcategorias.idprecio
            LEFT JOIN categorias ON preciosxcategorias.idcategoria = categorias.idcategoria AND preciosxcategorias.idprecio = precios.idprecio
            INNER JOIN carreras ON categorias.idcarrera = carreras.idcarrera
            WHERE carreras.idevento = '$idevento' AND precios.idprecio = '$id'
        "));

        $unserialized = explode('&', $request->datos);
        $fecha_desde =  implode("", explode('fecha_desde_mod=', $unserialized[0]));
        $fecha_hasta = implode("", explode('fecha_hasta_mod=', $unserialized[1]));
        $precio = implode("", explode('precio_mod=', $unserialized[2]));
        $cantidad = implode("", explode('cantidad_mod=', $unserialized[3]));

        $precios = Precios::where([
                'idprecio' => $id,
            ])->firstOrFail();

        $precios->fecha_desde = $fecha_desde;
        $precios->fecha_hasta = $fecha_hasta;
        $precios->precio = $precio;
        $precios->cantidad = $cantidad;
        $precios->efectivo = (in_array("efectivo", $request->formadepago) ? '1' : '0');
        $precios->transferencia = (in_array("transferencia", $request->formadepago) ? '1' : '0');
        $precios->paypal = (in_array("paypal", $request->formadepago) ? '1' : '0');
        $precios->payu = (in_array("payu", $request->formadepago) ? '1' : '0');
        $precios->mercadopago = (in_array("mercadopago", $request->formasdepagopago) ? '1' : '0');
        $precios->save();

        foreach ($precios_db as $key => $precio_db) {
            if (!in_array($precio_db->idcategoria, $request->categorias)){
                DB::select(DB::raw("DELETE FROM preciosxcategorias WHERE idcategoria = '".$precio_db->idcategoria."'"));
            }
        }
        return $precio;
    }

    public function destroy($id)
    {
        $idevento = session('idevento');
        if (!$idevento)
            return response()->json(['errors' => ['La sesión se venció, debe Salir y volver a Iniciar sesión']], 400);

        $precios = Precios::where([
                'idprecio' => $id,
            ])->firstOrFail();
        $precios->delete();

        DB::select(DB::raw("DELETE FROM preciosxcategorias WHERE idprecio = '".$id."'"));

        return response()->json(null, 204);
    }

}
