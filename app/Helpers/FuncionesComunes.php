<?php
namespace App\Helpers;

use Illuminate\Support\Facades\Mail;
use Swift_Mailer;
use Swift_MailTransport;
use Swift_Message;

use Carbon\Carbon;
use DB;

use App\Models\Controles;
use App\Models\Eventos;
use App\Models\Paises;

class FuncionesComunes {

    public static function mostrar_error($mensaje_error)
    {
        FuncionesComunes::enviar_mail(config('app.mail_soporte'), config('app.mail_sistema'), 'Error en el sistema de cronometrajeinstantaneo.com', $mensaje_error);
    }

    public static function checkRemoteFile($url)
    {
        if (env('APP_ENV') == 'local')
            return false;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL,$url);
        // don't download content
        curl_setopt($ch, CURLOPT_NOBODY, 1);
        curl_setopt($ch, CURLOPT_FAILONERROR, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        $result = curl_exec($ch);
        curl_close($ch);
        if ($result !== FALSE)
        {
            return true;
        }
        else
        {
            return false;
        }

    }

    public static function cache($codigo)
    {
        // Pongo como predeterminado el path de prod porque no toma el env()
        file_put_contents(config('app.path_cache', '/var/www/cronometrajeinstantaneo/code/cache').'/cache.txt', $codigo."\r\n", FILE_APPEND);
        file_put_contents(config('app.path_cache', '/var/www/cronometrajeinstantaneo/code/cache').'/resultados/'.$codigo.'.timestamp', time());
    }

    public static function hash()
    {
        $hash = file_get_contents(__DIR__.'/../../hash');
        return $hash;
    }

    public static function sessionMobile($codigo)
    {
        $resultado_control = Controles::select("nombre", "idcontrol", "idetapa", "codigo")
                            ->where("codigo", $codigo)
                            ->limit(1);

        if (!$resultado_control->count()){
            // TODO: Chequear el $this-> desde static, no debe andar
            // $this->enviarMail(config('app.mail_soporte'), config('app.mail_sistema'), 'El url ingresado es incorrecto', 'En mobile han intentado acceder con el codigo '.$codigo, HEADERS);
            header("HTTP/1.1 400 Bad Request");
            exit('ERROR en codigo');
        }

        $control = $resultado_control->get()->first()->toArray();

        $evento = Eventos::select("idevento", "largada", "terminada", "nombre AS evento",
                "codigo AS evento_codigo",
                DB::raw("(SELECT COUNT(DISTINCT idcontrol)
                    FROM controles
                    WHERE idetapa IN (SELECT idetapa FROM etapas WHERE idcarrera IN
                        (SELECT idcarrera FROM carreras WHERE carreras.idevento = eventos.idevento))) AS controles"))
                ->where("idevento", DB::raw("(SELECT idevento FROM eventos WHERE terminada IS NULL AND idevento = (
                    SELECT idevento FROM carreras WHERE idcarrera = (
                        SELECT idcarrera FROM etapas WHERE idetapa =  '".$control['idetapa']."'
                    LIMIT 1) LIMIT 1) LIMIT 1)"))
                ->limit(1);

        $mobile = array();

        foreach ($control as $key => $value) {
            $mobile[$key] = $value;
        }

        if ($evento->count()) {
            foreach ($evento->get()->first()->toArray() as $key => $value) {
                $mobile[$key] = $value;
            }
        }

        return response($mobile)
            ->header('Content-type', 'application/json')
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Access-Control-Allow-Credentials', 'true')
            ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            ->header('Access-Control-Allow-Headers', 'x-requested-with, X-CSRFToken')
            ->header('Access-Control-Max-Age', '1000');
    }

    public static function enviar_mail($destinatarios, $remitente, $asunto, $mensaje)
    {
        if (!$mensaje || strlen($mensaje) < 3)
            return false;

        if (env('APP_DEBUG', false))
            $destinatarios = '<EMAIL>';

        $mails = [];
        if (!is_array($destinatarios)) {
            if (filter_var($destinatarios, FILTER_VALIDATE_EMAIL))
                $mails[] = [$destinatarios => $destinatarios];

        } else {
            foreach ($destinatarios as $destinatario_mail => $destinatario_nombre)
                if (!($destinatario_mail) && filter_var($destinatario_nombre, FILTER_SANITIZE_EMAIL))
                    $mails[] = [$destinatario_nombre => $destinatario_nombre];
                else if (filter_var($destinatario_mail, FILTER_SANITIZE_EMAIL))
                    $mails[] = [$destinatario_mail => $destinatario_nombre];
        }
        if (!count($mails))
            return false;

        if (is_array($remitente) && array_key_exists('<EMAIL>', $remitente)) {
                return FuncionesComunes::enviar_mail_tercero($mails[0], $remitente, $asunto, $mensaje);
            }

        if (is_array($remitente)) {
            $remitente_mail = key($remitente);
            $remitente_nombre = current($remitente);
        } else if (filter_var($remitente, FILTER_VALIDATE_EMAIL)) {
            $remitente_mail = $remitente;
            $remitente_nombre = 'Cronometraje Instantáneo'; // Constante según dominio
        } else {
            $remitente_mail = '<EMAIL>'; // Constante según dominio
            $remitente_nombre = 'Cronometraje Instantáneo'; // Constante según dominio
        }

        try {
            $email = new \SendGrid\Mail\Mail();
            $email->setFrom('<EMAIL>', $remitente_nombre);
            $email->setSubject($asunto);
            $email->addContent("text/html", $mensaje);
            $email->setReplyTo($remitente_mail);
            $sendgrid = new \SendGrid(config('app.sendgrid_api_key'));
            foreach ($mails as $mail) {
                if (key($mail) == current($mail))
                    $email->addTo(key($mail));
                else
                    $email->addTo(key($mail), current($mail));
            }
            $response = $sendgrid->send($email);
            if ($response->statusCode() == '200' || $response->statusCode() == '202')
                return true;
            else
                return false;

        } catch (Exception $e) {
            FuncionesComunes::mostrar_error('Caught exception: '. $e->getMessage() .'<br>'
                . 'Destinatarios: '.json_encode($destinatarios) .'<br>'
                . 'Remitente: '.json_encode($remitente) .'<br>'
                . 'Asunto: '.json_encode($asunto));
        }

    }

    public static function enviar_mail_tercero($destinatario, $remitente, $asunto, $mensaje) {

        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);

        try {
            $mail->isSMTP();

            switch (key($remitente)) {
                case '<EMAIL>':
                    // Configuración del servidor SMTP
                    $mail->Host = 'smtp.elasticemail.com';
                    $mail->SMTPAuth = true;
                    $mail->Username = '<EMAIL>';
                    $mail->Password = '1103AA2E8E3A0FA80DD279886D7AF21E3E64';
                    $mail->SMTPSecure = 'tls';
                    $mail->Port = 2525;
                    $mail->setFrom('<EMAIL>', 'TakeARun');
                    break;
            }

            // Configuración del correo electrónico
            $mail->addAddress($destinatario);
            $mail->isHTML(true);
            $mail->CharSet = 'UTF-8';
            $mail->Subject = $asunto;
            $mail->Body = $mensaje;

            // Envío del correo electrónico
            $mail->send();
            return true;

        } catch (Exception $e) {
            FuncionesComunes::mostrar_error('Ocurrió un error al enviar el correo electrónico: '. $mail->ErrorInfo);
            return false;
        }
    }

    public static function generar_codigo() {
        $chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

        do {
            $random = '';
            for($i = 0; $i < 4; $i++)
                $random.= $chars[rand(0, 35)];

        } while (FuncionesComunes::contar_sql(FuncionesComunes::consulta_sql(
            "SELECT idcontrol FROM controles WHERE codigo = '$random' LIMIT 1")));

        return $random;
    }

    //uso $mobile como evento
    public static function convertirFinal($ensegundos, $precision = null)
    {
        // Las posibles precisiones son: minutos, segundos, decisegundos, centisegundos, miliminutos, solo-minutos, solo-segundos
        $precision = $precision ?? 'milisegundos';

        // Es negativo
        if ($ensegundos < 0) {
            $negativo = '-';
            $ensegundos = $ensegundos * -1;
        } else {
            $negativo = '';
        }

        // Separo los milisegundos
        $temp_array = explode('.', $ensegundos);
        $ensegundos = isset($temp_array[0]) ? $temp_array[0] : 0;
        $milisegundos = isset($temp_array[1]) ? str_split($temp_array[1]) : 0;

        if (in_array($precision, ['milisegundos', 'solo-minutos', 'solo-segundos'])) {
            $ms = '.' . intval($milisegundos[0] ?? 0) . intval($milisegundos[1] ?? 0) . intval($milisegundos[2] ?? 0);

        } else {
            // Redondeo
            if (intval($milisegundos[2]) >= 5) {
                $milisegundos[1] = intval($milisegundos[1]) + 1;
            }
            if (($precision == 'centisegundos' && $milisegundos[1] > 9)
                || ($precision != 'centisegundos' && intval($milisegundos[1]) >= 5)) {
                $milisegundos[0] = intval($milisegundos[0]) + 1;
                $milisegundos[1] = 0;
            }
            if (($precision == 'decisegundos' && $milisegundos[0] > 9)
                || ($precision != 'decisegundos' && intval($milisegundos[0]) >= 5)) {
                $ensegundos++;
                $milisegundos[0] = 0;
            }

            // Armo $ms
            if ($precision == 'centisegundos')
                $ms = '.' . $milisegundos[0] . $milisegundos[1];
            else if ($precision == 'decisegundos')
                $ms = '.' . $milisegundos[0];
            else // $precision == 'segundos' || $precision == 'minutos'
                $ms = '';
        }

        if ($precision == 'solo-segundos')
            return $negativo.$ensegundos.$ms;

        $horas = 0;
        $minutos = 0;
        $segundos = 0;

        if ($precision != 'solo-minutos') {
            while ($ensegundos >= 3600 && $horas < 999)
            {
                $horas++;
                $ensegundos-= 3600;
            }
        }
        while ($ensegundos >= 60)
        {
            $minutos++;
            $ensegundos-= 60;
        }
        if ($precision == 'minutos' && $ensegundos % 60 >= 30) {
            $minutos++;
        }

        $segundos = $ensegundos;
        if (strlen($horas) == 1)
            $horas = '0'.$horas;
        if (strlen($minutos) == 1)
            $minutos = '0'.$minutos;
        if (strlen($segundos) == 1)
            $segundos = '0'.$segundos;

        return $negativo
            .($precision != 'solo-minutos' ? $horas.':' : '')
            .$minutos
            .($precision != 'minutos' ? ':'.$segundos : '')
            .$ms;
    }

    public static function segundosTranscurridos($fecha_inicial, $fecha_final)
    {
        if (!$fecha_final || !$fecha_inicial) {
            return response()->json([
                'message' => 'No hay fecha final'], 404);
        }

        $start = Carbon::parse($fecha_inicial);
        $end = Carbon::parse($fecha_final);

        return $start->diffInMilliseconds($end)/1000;
    }

    public static function log($log, $data = [])
    {
        $separador = ';';
        $linea = date("Y-m-d H:i:s").$separador;
        if ($log == 'api' && isset($data['idcontrol']))
            $linea.= $data['codigo'].$separador;
        $linea.= json_encode($data).$separador;
        $linea.= "\r\n";

        file_put_contents(config('app.path_logs').'/'.date("Y-m-d").'_'.$log.'.csv', $linea, FILE_APPEND);
    }

    public static function chequearTiempo($tiempo)
    {
        /*
            TO DO Chequear
            ErrorException: Cannot modify header information - headers already sent by
        */
        if (!$tiempo) {
            //header("HTTP/1.1 400 Bad Request");
            exit('ERROR en tiempo');
        }
    }

    public static function formatearTiempoCrono($row)
    {
        $regex_dia = '/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/';
        $regex_tiempo = '/^([0-9]{2}:[0-9]{2})(:[0-9]{2})?(.[0-9]{2,3})?$/';

        $row['dia'] = (isset($row['dia']) && preg_match($regex_dia, $row['dia']))
            ? $row['dia']
            : date("Y-m-d");

        if (isset($row['hora']) && preg_match($regex_tiempo, trim($row['hora']))) {

            $crono = trim($row['dia']).' '.trim($row['hora']);

        } else if (isset($row['hora']) && is_numeric($row['hora'])) {
            $hora = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row['hora'])->format('H:i:s.v');
            $crono = $row['dia'].' '.$hora;

        } else {

            $crono = $row['dia'].' '
                .(isset($row['horas']) ? intval($row['horas']) : '00').':'
                .(isset($row['minutos']) ? intval($row['minutos']) : '00').':'
                .(isset($row['segundos']) ? intval($row['segundos']) : '00').'.'
                .(isset($row['milisegundos']) ? intval($row['milisegundos']) : '000');

        }

        return $crono;
    }

    public static function conectar_db()
    {
        $bd_host = config('database.connections.mysql.host');
        $bd_user = config('database.connections.mysql.username');
        $bd_pass = config('database.connections.mysql.password');
        $bd_bd = config('database.connections.mysql.database');

        $bd_link = mysqli_connect($bd_host, $bd_user, $bd_pass, $bd_bd)
            or FuncionesComunes::mostrar_error('Se ha producido un error conectando a la base de datos '.$bd_bd);
        mysqli_set_charset($bd_link, "utf8mb4");
        $GLOBALS['bd_link'] =$bd_link;

        return $bd_link;
    }

    public static function consulta_sql($strSQL)
    {
        $bd_link = $GLOBALS['bd_link'];

        $resultado_sql = mysqli_query($bd_link, $strSQL)
            or FuncionesComunes::mostrar_error('<br>'
                .'Ocurrió un error haciendo una consulta al MySQL.<br><br><b>Error MySQL:</b> '
                .mysqli_error($bd_link)
                .'<br><br>'
                .'<br><b>&Uacute;ltima consulta:</b> <code>'.$strSQL.'</code> </p>');

        return $resultado_sql;
    }

    public static function array_all_sql($resultado_sql, $key = false)
    {
        $array_all = array();
        while ($array = mysqli_fetch_assoc($resultado_sql)) {
            if ($key)
                $array_all[$array[$key]] = $array;
            else
                $array_all[] = $array;
        }
        return $array_all;
    }

    public static function array_sql($resultado_sql)
    {
        return mysqli_fetch_assoc($resultado_sql);
    }

    public static function campo_sql($resultado_sql, $row = 0, $field = 0)
    {
        if ($row > 0)
            FuncionesComunes::puntero_sql($resultado_sql, $row);

        $temp_array = mysqli_fetch_array($resultado_sql);
        return isset($temp_array[$field])
            ? $temp_array[$field]
            : false;
    }

    public static function contar_sql($resultado_sql)
    {
        return mysqli_num_rows($resultado_sql);
    }

    public static function puntero_sql($resultado_sql, $row = 0)
    {
        return mysqli_data_seek($resultado_sql, $row);
    }

    public static function escape_sql($resultado_sql)
    {
        global $bd_link;

        return mysqli_real_escape_string($bd_link, $resultado_sql);
    }

    public static function id_sql()
    {
        global $bd_link;

        return mysqli_insert_id($bd_link);
    }

    public static function afectado_sql()
    {
        global $bd_link;

        return mysqli_affected_rows($bd_link);
    }

    public static function insertar_sql($table, $row)
    {
        global $bd_link;
        $insert_values = [];
        foreach ($row as $key => $value) {
            if (!is_numeric($key) && $key == 'uuid') {
                $insert_values[] = "uuid = uuid()";
                mostrar_error('Ingresando uuid en '.$table);
            } else if (!is_numeric($key)) // Con el mysqli_fetch_array trae también keys numéricas que no queremos
                $insert_values[] = "`" . $key . "` = '" . $value ."'";
        }

        FuncionesComunes::consulta_sql("INSERT INTO $table SET ".implode(', ', $insert_values));
    }

    public static function filter_post($args)
    {
        $post = filter_input_array(INPUT_POST, $args, true);

        if ($post == null) {
            $post = [];
            foreach ($args as $key => $value) {
                $post[$key] = $value == FILTER_SANITIZE_NUMBER_INT ? 0 : '';
            }
        }

        return $post;
    }

    public static function getPaises()
    {
        return Paises::orderBy('orden')->get();
    }


}
