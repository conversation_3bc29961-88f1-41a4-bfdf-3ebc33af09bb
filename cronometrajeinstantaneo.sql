-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.0
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost
-- Tiempo de generación: 26-06-2025 a las 22:30:32
-- Versión del servidor: 10.11.11-MariaDB-0+deb12u1
-- Versión de PHP: 8.3.22

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `cronometrajeinstantaneo`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `carreras`
--

CREATE TABLE `carreras` (
  `idcarrera` int(10) UNSIGNED NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `nombre` varchar(100) NOT NULL,
  `orden` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `inscripciones` tinyint(1) NOT NULL DEFAULT 1,
  `resultados` tinyint(1) NOT NULL DEFAULT 1,
  `largada` datetime(3) NOT NULL,
  `terminada` datetime(3) NOT NULL,
  `puntaje` enum('','generales','categorias','sexo','multiplicador-segundos-sexo') NOT NULL,
  `puntos` text NOT NULL,
  `suma_puntos` varchar(20) NOT NULL,
  `auto_numerar_desde` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `auto_asignar_tag` varchar(8) DEFAULT NULL,
  `inscripciones_texto` text DEFAULT NULL,
  `inscripciones_preinscripto` text DEFAULT NULL,
  `mail_preinscripto` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `categorias`
--

CREATE TABLE `categorias` (
  `idcategoria` int(10) UNSIGNED NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `idcarrera` int(10) UNSIGNED NOT NULL,
  `nombre` varchar(100) NOT NULL DEFAULT '',
  `orden` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `inscripciones` tinyint(1) NOT NULL DEFAULT 1,
  `resultados` tinyint(1) NOT NULL DEFAULT 1,
  `sexo` enum('masculino','femenino','otro','equipomasculino','equipofemenino','equipootro','mixto','equipomixto') DEFAULT NULL,
  `equipo` enum('','dupla','tria','tetra') NOT NULL DEFAULT '',
  `nacimiento_desde` date DEFAULT NULL,
  `nacimiento_hasta` date DEFAULT NULL,
  `observacion` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `categoriasxparticipantes`
--

CREATE TABLE `categoriasxparticipantes` (
  `idcategoriaxparticipante` int(10) UNSIGNED NOT NULL,
  `idcategoria` int(11) NOT NULL,
  `idinscripcion` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `config_cronometraje`
--

CREATE TABLE `config_cronometraje` (
  `idevento` int(10) UNSIGNED NOT NULL,
  `sync_apps` tinyint(1) NOT NULL DEFAULT 1,
  `resultados` tinyint(1) NOT NULL DEFAULT 1,
  `tipolargada` enum('unica_largada','unica_largada_con_etapas','carreras_con_largadas','carreras_con_largadas_vueltas','categorias_con_largadas','categorias_con_largadas_vueltas','etapas_con_largadas','carreras_con_largadas_con_etapas','largadas_individuales','largadas_individuales_con_etapas','vueltas_fijas','etapas_con_largadas_vueltas','largadas_individuales_con_vueltas','largadas_individuales_con_etapas_continuas','') NOT NULL DEFAULT 'unica_largada',
  `tipo_largada` enum('evento','carreras','etapas','participantes','participantes_etapas') NOT NULL DEFAULT 'evento',
  `precision` enum('minutos','segundos','decisegundos','centisegundos','milisegundos','solo-minutos','solo-segundos') NOT NULL DEFAULT 'segundos',
  `clasificacion` enum('tiempo','mejor_etapa','mejor_vuelta','ultima_etapa','regularidad') NOT NULL DEFAULT 'tiempo',
  `tags_descartables` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `ticket` tinyint(1) NOT NULL DEFAULT 1,
  `tiempo_vuelta` int(10) UNSIGNED NOT NULL DEFAULT 30,
  `velocidad_promedio` enum('','km/h','m/s','nudos','min/100m','min/km') NOT NULL DEFAULT '',
  `penas` tinyint(1) NOT NULL DEFAULT 0,
  `penas_en_minutos` tinyint(4) NOT NULL DEFAULT 1,
  `limitar_penas` varchar(60) NOT NULL,
  `unificar_penas` tinyint(1) NOT NULL DEFAULT 1,
  `estado_carrera` tinyint(1) NOT NULL DEFAULT 1,
  `parciales` tinyint(1) NOT NULL DEFAULT 0,
  `minuto_cerrado` tinyint(1) NOT NULL DEFAULT 0,
  `no_oficial` tinyint(1) NOT NULL DEFAULT 0,
  `podios` int(10) UNSIGNED NOT NULL DEFAULT 3,
  `texto_resultados` varchar(191) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `config_organizacion`
--

CREATE TABLE `config_organizacion` (
  `idevento` int(10) UNSIGNED NOT NULL,
  `abiertas` tinyint(1) NOT NULL DEFAULT 0,
  `fecha_inicio` datetime DEFAULT NULL,
  `fecha_fin` datetime DEFAULT NULL,
  `multi_idparticipante` tinyint(1) NOT NULL DEFAULT 0,
  `multi_categoria` tinyint(1) NOT NULL DEFAULT 0,
  `auto_numeracion` tinyint(1) NOT NULL DEFAULT 0,
  `auto_mail` tinyint(1) NOT NULL DEFAULT 0,
  `auto_completar` tinyint(1) NOT NULL DEFAULT 0,
  `estado_predeterminado` enum('preinscripto','inscripto','acreditado','abandono','descalificado','anulado') NOT NULL DEFAULT 'preinscripto',
  `tipo_nombre` enum('libre','Nombre Apellido','Apellido, Nombre','APELLIDO Nombre','Nombre y Apellido separados') NOT NULL DEFAULT 'libre',
  `validar_mail` enum('sin_control','no_obligatorio','unico','exigir_usuario') NOT NULL DEFAULT 'sin_control',
  `nombre_localidad` varchar(191) NOT NULL DEFAULT 'Localidad',
  `modificar` enum('nunca','preinscripto','inscripto','acreditado') DEFAULT 'nunca',
  `iddato_unico` varchar(20) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `config_vivo`
--

CREATE TABLE `config_vivo` (
  `idevento` int(10) UNSIGNED NOT NULL,
  `streaming` tinyint(1) NOT NULL,
  `idcategoria` int(11) NOT NULL,
  `cantidad` tinyint(4) NOT NULL DEFAULT 5,
  `diff` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `contactos`
--

CREATE TABLE `contactos` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `relation_id` int(10) UNSIGNED NOT NULL,
  `relation` enum('organizacion','evento','calendario') NOT NULL,
  `tipo` enum('web','telefono','whatsapp','email','facebook','instagram','youtube','tiktok','otro') NOT NULL,
  `contacto` varchar(191) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `controles`
--

CREATE TABLE `controles` (
  `idcontrol` int(10) UNSIGNED NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `codigo` char(4) NOT NULL,
  `idetapa` int(10) UNSIGNED NOT NULL,
  `nombre` varchar(100) NOT NULL,
  `orden` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `tipo` enum('largada','etapa','control','penas','pre-largada','final','pre-llegada','parcial','acreditar') DEFAULT NULL,
  `tipolectura` enum('movil','codigo','rfid','manual','fotocelula','gps') NOT NULL DEFAULT 'movil',
  `distancia` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `ultimasincro` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  `ultimasincroid` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `datos`
--

CREATE TABLE `datos` (
  `iddato` varchar(20) NOT NULL,
  `nombre` varchar(200) NOT NULL,
  `tipo` enum('text','number','date','pregunta','select','file','acepta') NOT NULL DEFAULT 'text',
  `estadistica` tinyint(4) NOT NULL DEFAULT 0,
  `observacion` text DEFAULT NULL,
  `ayuda` varchar(191) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `datosxeventos`
--

CREATE TABLE `datosxeventos` (
  `iddato` varchar(20) NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `tipo` enum('participante','equipo') NOT NULL DEFAULT 'participante',
  `obligatorio` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `orden` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `inscripciones` tinyint(1) NOT NULL DEFAULT 1,
  `resultados` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `apps` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `observacion` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `datosxparticipantes`
--

CREATE TABLE `datosxparticipantes` (
  `iddato` varchar(20) NOT NULL,
  `idinscripcion` int(10) UNSIGNED NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `dato` varchar(200) NOT NULL DEFAULT '',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `disciplinas`
--

CREATE TABLE `disciplinas` (
  `id` int(10) UNSIGNED NOT NULL,
  `orden` tinyint(3) UNSIGNED NOT NULL,
  `deporte` varchar(20) NOT NULL,
  `slug` varchar(20) NOT NULL,
  `nombre_es` varchar(50) NOT NULL,
  `nombre_en` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `etapas`
--

CREATE TABLE `etapas` (
  `idetapa` int(10) UNSIGNED NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `idcarrera` int(10) UNSIGNED NOT NULL,
  `nombre` varchar(100) NOT NULL,
  `tipo_largada` enum('','evento','carreras','etapas','participantes','participantes_etapas') NOT NULL DEFAULT '',
  `orden` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `resultados` tinyint(1) NOT NULL DEFAULT 1,
  `largada` datetime(3) DEFAULT NULL,
  `terminada` datetime(3) NOT NULL,
  `vueltas` int(10) UNSIGNED NOT NULL DEFAULT 1,
  `distancia` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `velocidad_promedio` enum('','km/h','m/s','nudos','min/100m','min/km') NOT NULL DEFAULT '',
  `tiempo_minimo` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `tiempo_maximo` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `tiempo_ideal` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `eventos`
--

CREATE TABLE `eventos` (
  `idevento` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `idorganizacion` int(10) UNSIGNED NOT NULL,
  `idcronometrador` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `estado` enum('pendiente','iniciado','terminado','postergado','cancelado') NOT NULL DEFAULT 'pendiente',
  `estado_pago` enum('pendiente','aprobado','parcial','gratis','informado') NOT NULL DEFAULT 'pendiente',
  `tipo` enum('','organizador','cronometrador','auto') NOT NULL DEFAULT '',
  `descuento` int(11) NOT NULL DEFAULT 0,
  `pago_informado` text DEFAULT NULL,
  `publico` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `iddisciplina` tinyint(3) UNSIGNED NOT NULL,
  `idpais` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `nombre` varchar(120) NOT NULL,
  `codigo` varchar(120) NOT NULL,
  `sitio` tinyint(1) NOT NULL DEFAULT 0,
  `organizacion` tinyint(1) NOT NULL DEFAULT 0,
  `cronometraje` tinyint(3) UNSIGNED NOT NULL DEFAULT 1,
  `vivo` tinyint(1) NOT NULL DEFAULT 0,
  `mail` varchar(320) NOT NULL,
  `descripcion` text DEFAULT NULL,
  `obsinterna` text DEFAULT NULL,
  `fecha` date NOT NULL,
  `fecha_hasta` date DEFAULT NULL,
  `localidad` varchar(100) NOT NULL,
  `largada` datetime(3) NOT NULL,
  `terminada` datetime(3) NOT NULL,
  `inscripciones_estilo` text DEFAULT NULL,
  `inscripciones_texto` text DEFAULT NULL,
  `inscripciones_js` text DEFAULT NULL,
  `inscripciones_preinscripto` text DEFAULT NULL,
  `inscripciones_cerradas` text DEFAULT NULL,
  `inscripciones_inscripto` text DEFAULT NULL,
  `inscripciones_rechazado` text DEFAULT NULL,
  `resultados_estilo` text DEFAULT NULL,
  `resultados_estilo_ticket` text DEFAULT NULL,
  `resultados_js` text DEFAULT NULL,
  `resultados_con_estilo` tinyint(3) UNSIGNED NOT NULL DEFAULT 1,
  `resultados_filtros` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `resultados_buscador` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `streaming_estilo` text DEFAULT NULL,
  `mail_preinscripto` text DEFAULT NULL,
  `mail_inscripto` text DEFAULT NULL,
  `precio` decimal(15,2) NOT NULL DEFAULT 100.00,
  `sync_enable` tinyint(3) UNSIGNED NOT NULL,
  `sync_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(191) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(191) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `lecturas`
--

CREATE TABLE `lecturas` (
  `idlectura` int(10) UNSIGNED NOT NULL,
  `uuid` char(36) NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `idcontrol` int(10) UNSIGNED NOT NULL,
  `estado` enum('ok','error','eliminado','repetido') NOT NULL DEFAULT 'ok',
  `tiempo` datetime(3) NOT NULL,
  `idparticipante` int(10) UNSIGNED DEFAULT NULL,
  `tagID` varchar(36) DEFAULT NULL,
  `vuelta` tinyint(3) UNSIGNED NOT NULL,
  `tipo` enum('app','rfid','fotocelula','gps','admin','beacon') NOT NULL DEFAULT 'app',
  `timer` varchar(191) NOT NULL,
  `synced_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(191) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `notifications`
--

CREATE TABLE `notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `notifiable_type` varchar(255) NOT NULL,
  `notifiable_id` bigint(20) UNSIGNED NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `organizaciones`
--

CREATE TABLE `organizaciones` (
  `idorganizacion` int(10) UNSIGNED NOT NULL,
  `idcronometrador` bigint(20) UNSIGNED NOT NULL,
  `estado` tinyint(3) UNSIGNED NOT NULL DEFAULT 1,
  `publico` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `organiza` tinyint(1) NOT NULL DEFAULT 1,
  `cronometra` tinyint(1) NOT NULL DEFAULT 0,
  `nombre` varchar(200) NOT NULL,
  `zona` varchar(100) DEFAULT NULL,
  `idpais` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `codigo` varchar(200) NOT NULL,
  `url` varchar(255) DEFAULT NULL,
  `mail` varchar(320) NOT NULL,
  `descripcion` text DEFAULT NULL,
  `obsinterna` text DEFAULT NULL,
  `pass` char(191) DEFAULT NULL,
  `precio` decimal(15,2) NOT NULL DEFAULT 100.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT '0000-00-00 00:00:00',
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `pagos`
--

CREATE TABLE `pagos` (
  `idpago` int(10) UNSIGNED NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `idinscripcion` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `idprecio` int(10) UNSIGNED NOT NULL,
  `estado` enum('','pendiente','aprobado','rechazado') NOT NULL DEFAULT '',
  `fecha` datetime DEFAULT NULL,
  `referencia` varchar(100) DEFAULT NULL,
  `json` text DEFAULT NULL,
  `observaciones` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `paises`
--

CREATE TABLE `paises` (
  `id` int(10) UNSIGNED NOT NULL,
  `orden` tinyint(3) UNSIGNED NOT NULL,
  `code` varchar(2) NOT NULL,
  `slug` varchar(50) NOT NULL,
  `nombre_es` varchar(50) NOT NULL,
  `nombre_en` varchar(50) NOT NULL,
  `timezone` tinyint(4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `participantes`
--

CREATE TABLE `participantes` (
  `idinscripcion` int(10) UNSIGNED NOT NULL,
  `idparticipante` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `idevento` int(10) UNSIGNED NOT NULL,
  `estado` enum('preinscripto','inscripto','acreditado','anulado','eliminado') NOT NULL DEFAULT 'preinscripto',
  `estado_pago` enum('','pendiente','aprobado','parcial','invitado','espera') NOT NULL DEFAULT '',
  `estado_ficha` enum('','pendiente','aprobado') NOT NULL DEFAULT '',
  `estado_carrera` enum('inrace','hooked','lap','dnf','dsq','dns','otro') NOT NULL DEFAULT 'inrace',
  `estado_carrera_otro` varchar(20) NOT NULL,
  `nombre` varchar(150) NOT NULL,
  `apellido` varchar(150) NOT NULL,
  `mail` varchar(150) DEFAULT NULL,
  `idcategoria` int(10) UNSIGNED NOT NULL,
  `tag` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `localidad` varchar(150) DEFAULT NULL,
  `equipo` enum('','dupla','tria','tetra','participante') NOT NULL DEFAULT '',
  `idequipo` int(10) UNSIGNED NOT NULL,
  `observacion` text DEFAULT NULL,
  `fechapago` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `password_resets`
--

CREATE TABLE `password_resets` (
  `email` varchar(191) NOT NULL,
  `token` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `penas`
--

CREATE TABLE `penas` (
  `idpena` int(10) UNSIGNED NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `idetapa` int(10) UNSIGNED NOT NULL,
  `idcontrol` int(10) UNSIGNED NOT NULL,
  `idparticipante` int(10) UNSIGNED NOT NULL,
  `tiempo` int(11) NOT NULL,
  `observacion` text DEFAULT NULL,
  `sincro` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(191) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(191) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `plataformas`
--

CREATE TABLE `plataformas` (
  `idplataforma` int(10) UNSIGNED NOT NULL,
  `idorganizacion` int(10) UNSIGNED NOT NULL,
  `estado` tinyint(1) NOT NULL DEFAULT 1,
  `titulo` varchar(100) DEFAULT NULL,
  `plataforma` enum('efectivo','transferencia','paypal','payu','mercadopago','boton-mercadopago') NOT NULL,
  `descripcion` text NOT NULL,
  `key` varchar(191) NOT NULL,
  `secret` varchar(191) NOT NULL,
  `idpais` int(10) UNSIGNED NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `precios`
--

CREATE TABLE `precios` (
  `idprecio` int(10) UNSIGNED NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `estado` tinyint(1) NOT NULL DEFAULT 1,
  `titulo` varchar(100) DEFAULT NULL,
  `idpais` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `idplataforma` int(10) UNSIGNED NOT NULL,
  `fecha_desde` datetime DEFAULT NULL,
  `fecha_hasta` datetime DEFAULT NULL,
  `precio` decimal(15,2) UNSIGNED NOT NULL DEFAULT 0.00,
  `cantidad` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `cuota` decimal(5,2) NOT NULL DEFAULT 100.00,
  `boton` varchar(100) DEFAULT NULL,
  `url` varchar(1910) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `preciosxcarreras`
--

CREATE TABLE `preciosxcarreras` (
  `idprecioxcarrera` int(10) UNSIGNED NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `idprecio` int(10) UNSIGNED NOT NULL,
  `idcarrera` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `resultados`
--

CREATE TABLE `resultados` (
  `uuid` char(36) NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `idcarrera` int(10) UNSIGNED NOT NULL,
  `idetapa` int(10) UNSIGNED NOT NULL,
  `idcategoria` int(10) UNSIGNED NOT NULL,
  `idlectura` int(10) UNSIGNED NOT NULL,
  `vuelta` int(10) UNSIGNED NOT NULL,
  `idparticipante` int(10) UNSIGNED NOT NULL,
  `nombre` varchar(150) NOT NULL DEFAULT '',
  `tiempo_carrera` int(11) NOT NULL,
  `tiempo_etapa` int(11) NOT NULL,
  `tiempo_control` int(11) NOT NULL,
  `pos_general` smallint(5) UNSIGNED NOT NULL,
  `pos_sexo` smallint(5) UNSIGNED NOT NULL,
  `pos_categoria` smallint(5) UNSIGNED NOT NULL,
  `penas` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(191) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` text NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `social_profiles`
--

CREATE TABLE `social_profiles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `social_id` varchar(191) NOT NULL,
  `social_name` varchar(191) NOT NULL,
  `social_avatar` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_id` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tags`
--

CREATE TABLE `tags` (
  `idtag` int(10) UNSIGNED NOT NULL,
  `tagID` varchar(36) DEFAULT NULL,
  `codigo` char(10) DEFAULT NULL,
  `estado` tinyint(3) UNSIGNED NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `idinscripcion` int(10) UNSIGNED NOT NULL,
  `idorganizacion` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tiempos`
--

CREATE TABLE `tiempos` (
  `uuid` char(36) NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `idcarrera` int(10) UNSIGNED NOT NULL,
  `idetapa` int(10) UNSIGNED NOT NULL,
  `idcategoria` int(10) UNSIGNED NOT NULL,
  `idcontrol` int(10) UNSIGNED NOT NULL,
  `vuelta` int(10) UNSIGNED NOT NULL,
  `idparticipante` int(10) UNSIGNED NOT NULL,
  `nombre` varchar(150) NOT NULL DEFAULT '',
  `hora` datetime(3) NOT NULL,
  `tiempo_carrera` int(11) NOT NULL,
  `tiempo_etapa` int(11) NOT NULL,
  `tiempo_control` int(11) NOT NULL,
  `pos_general` smallint(5) UNSIGNED NOT NULL,
  `pos_sexo` smallint(5) UNSIGNED NOT NULL,
  `pos_categoria` smallint(5) UNSIGNED NOT NULL,
  `velocidad` decimal(5,2) NOT NULL,
  `distancia` int(11) NOT NULL,
  `penas` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `users`
--

CREATE TABLE `users` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(191) NOT NULL,
  `contacto` varchar(100) DEFAULT NULL,
  `email` varchar(191) NOT NULL,
  `tipo` enum('','organizador','cronometrador','auto','admin','local_admin','super_admin') NOT NULL DEFAULT '',
  `idpais` int(11) DEFAULT NULL,
  `descuento` tinyint(4) NOT NULL DEFAULT 0,
  `cc` tinyint(4) NOT NULL DEFAULT 0,
  `obsinterna` text DEFAULT NULL,
  `password` varchar(191) DEFAULT NULL,
  `two_factor_secret` text DEFAULT NULL,
  `two_factor_recovery_codes` text DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `avatar_url` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `videos`
--

CREATE TABLE `videos` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `idcontrol` int(10) UNSIGNED NOT NULL,
  `nombre` varchar(200) NOT NULL,
  `tipo` enum('youtube','drive','vimeo','onedrive','dropbox') NOT NULL DEFAULT 'youtube',
  `url` varchar(200) NOT NULL,
  `inicio` datetime(3) NOT NULL,
  `fin` datetime(3) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `vivos`
--

CREATE TABLE `vivos` (
  `uuid` char(36) NOT NULL,
  `idevento` int(10) UNSIGNED NOT NULL,
  `idcarrera` int(10) UNSIGNED NOT NULL,
  `idetapa` int(10) UNSIGNED NOT NULL,
  `idcategoria` int(10) UNSIGNED NOT NULL,
  `vuelta` int(10) UNSIGNED NOT NULL,
  `idparticipante` int(10) UNSIGNED NOT NULL,
  `nombre` varchar(150) NOT NULL,
  `largada_carrera` datetime(3) NOT NULL,
  `largada_etapa` datetime(3) NOT NULL,
  `pos_general` smallint(5) UNSIGNED NOT NULL,
  `pos_sexo` smallint(5) UNSIGNED NOT NULL,
  `pos_categoria` smallint(5) UNSIGNED NOT NULL,
  `velocidad` decimal(5,2) NOT NULL,
  `distancia` int(11) NOT NULL,
  `penas` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `carreras`
--
ALTER TABLE `carreras`
  ADD PRIMARY KEY (`idcarrera`),
  ADD KEY `idevento` (`idevento`);

--
-- Indices de la tabla `categorias`
--
ALTER TABLE `categorias`
  ADD PRIMARY KEY (`idcategoria`),
  ADD KEY `categorias_idevento_index` (`idevento`),
  ADD KEY `categorias_idcarrera_index` (`idcarrera`);

--
-- Indices de la tabla `categoriasxparticipantes`
--
ALTER TABLE `categoriasxparticipantes`
  ADD PRIMARY KEY (`idcategoriaxparticipante`);

--
-- Indices de la tabla `config_cronometraje`
--
ALTER TABLE `config_cronometraje`
  ADD UNIQUE KEY `idevento` (`idevento`);

--
-- Indices de la tabla `config_organizacion`
--
ALTER TABLE `config_organizacion`
  ADD UNIQUE KEY `idevento` (`idevento`);

--
-- Indices de la tabla `config_vivo`
--
ALTER TABLE `config_vivo`
  ADD UNIQUE KEY `idevento` (`idevento`);

--
-- Indices de la tabla `contactos`
--
ALTER TABLE `contactos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `contacts_relation_id_index` (`relation_id`);

--
-- Indices de la tabla `controles`
--
ALTER TABLE `controles`
  ADD UNIQUE KEY `idcontrol+idetapa` (`idcontrol`,`idetapa`),
  ADD KEY `idx_idetapa_tipo` (`idetapa`,`tipo`);

--
-- Indices de la tabla `datos`
--
ALTER TABLE `datos`
  ADD PRIMARY KEY (`iddato`);

--
-- Indices de la tabla `datosxeventos`
--
ALTER TABLE `datosxeventos`
  ADD PRIMARY KEY (`idevento`,`iddato`);

--
-- Indices de la tabla `datosxparticipantes`
--
ALTER TABLE `datosxparticipantes`
  ADD PRIMARY KEY (`iddato`,`idinscripcion`,`idevento`);

--
-- Indices de la tabla `disciplinas`
--
ALTER TABLE `disciplinas`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `etapas`
--
ALTER TABLE `etapas`
  ADD PRIMARY KEY (`idetapa`),
  ADD KEY `etapas_idevento_index` (`idevento`),
  ADD KEY `etapas_idcarrera_index` (`idcarrera`);

--
-- Indices de la tabla `eventos`
--
ALTER TABLE `eventos`
  ADD PRIMARY KEY (`idevento`),
  ADD KEY `codigo` (`codigo`),
  ADD KEY `idorganizacion` (`idorganizacion`),
  ADD KEY `idpais` (`idpais`),
  ADD KEY `eventos_idcronometrador_index` (`idcronometrador`),
  ADD KEY `eventos_iddisciplina_index` (`iddisciplina`),
  ADD KEY `idx_fecha_estado` (`fecha`,`estado`),
  ADD KEY `eventos_estado_pago_index` (`estado_pago`),
  ADD KEY `eventos_nombre_index` (`nombre`);

--
-- Indices de la tabla `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indices de la tabla `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indices de la tabla `lecturas`
--
ALTER TABLE `lecturas`
  ADD PRIMARY KEY (`idlectura`),
  ADD UNIQUE KEY `uuid` (`uuid`),
  ADD KEY `lecturas_idcontrol_index` (`idcontrol`),
  ADD KEY `estado` (`estado`),
  ADD KEY `idevento` (`idevento`);

--
-- Indices de la tabla `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`);

--
-- Indices de la tabla `organizaciones`
--
ALTER TABLE `organizaciones`
  ADD PRIMARY KEY (`idorganizacion`),
  ADD KEY `organizaciones_organiza_index` (`organiza`),
  ADD KEY `organizaciones_cronometra_index` (`cronometra`),
  ADD KEY `organizaciones_nombre_index` (`nombre`);

--
-- Indices de la tabla `pagos`
--
ALTER TABLE `pagos`
  ADD PRIMARY KEY (`idpago`),
  ADD KEY `pagos_idevento_index` (`idevento`),
  ADD KEY `pagos_idinscripcion_index` (`idinscripcion`),
  ADD KEY `pagos_idprecio_index` (`idprecio`);

--
-- Indices de la tabla `paises`
--
ALTER TABLE `paises`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `participantes`
--
ALTER TABLE `participantes`
  ADD PRIMARY KEY (`idinscripcion`),
  ADD KEY `participantes_idevento_index` (`idevento`),
  ADD KEY `participantes_idparticipante_index` (`idparticipante`),
  ADD KEY `participantes_estado_index` (`estado`),
  ADD KEY `idcategoria` (`idcategoria`),
  ADD KEY `equipo` (`equipo`);

--
-- Indices de la tabla `password_resets`
--
ALTER TABLE `password_resets`
  ADD KEY `password_resets_email_index` (`email`);

--
-- Indices de la tabla `penas`
--
ALTER TABLE `penas`
  ADD PRIMARY KEY (`idpena`),
  ADD KEY `idevento` (`idevento`);

--
-- Indices de la tabla `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indices de la tabla `plataformas`
--
ALTER TABLE `plataformas`
  ADD PRIMARY KEY (`idplataforma`);

--
-- Indices de la tabla `precios`
--
ALTER TABLE `precios`
  ADD PRIMARY KEY (`idprecio`);

--
-- Indices de la tabla `preciosxcarreras`
--
ALTER TABLE `preciosxcarreras`
  ADD PRIMARY KEY (`idprecioxcarrera`);

--
-- Indices de la tabla `resultados`
--
ALTER TABLE `resultados`
  ADD UNIQUE KEY `uuid` (`uuid`);

--
-- Indices de la tabla `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indices de la tabla `social_profiles`
--
ALTER TABLE `social_profiles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `social_profiles_social_id_unique` (`social_id`),
  ADD KEY `social_profiles_user_id_foreign` (`user_id`);

--
-- Indices de la tabla `tags`
--
ALTER TABLE `tags`
  ADD PRIMARY KEY (`idtag`),
  ADD KEY `tagID` (`tagID`),
  ADD KEY `idinscripcion` (`idinscripcion`);

--
-- Indices de la tabla `tiempos`
--
ALTER TABLE `tiempos`
  ADD PRIMARY KEY (`uuid`);

--
-- Indices de la tabla `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`),
  ADD KEY `users_email_index` (`email`);

--
-- Indices de la tabla `videos`
--
ALTER TABLE `videos`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `vivos`
--
ALTER TABLE `vivos`
  ADD UNIQUE KEY `uuid` (`uuid`),
  ADD KEY `vivos_idcarrera_index` (`idcarrera`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `carreras`
--
ALTER TABLE `carreras`
  MODIFY `idcarrera` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `categorias`
--
ALTER TABLE `categorias`
  MODIFY `idcategoria` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `categoriasxparticipantes`
--
ALTER TABLE `categoriasxparticipantes`
  MODIFY `idcategoriaxparticipante` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `contactos`
--
ALTER TABLE `contactos`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `disciplinas`
--
ALTER TABLE `disciplinas`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `etapas`
--
ALTER TABLE `etapas`
  MODIFY `idetapa` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `eventos`
--
ALTER TABLE `eventos`
  MODIFY `idevento` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `lecturas`
--
ALTER TABLE `lecturas`
  MODIFY `idlectura` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `organizaciones`
--
ALTER TABLE `organizaciones`
  MODIFY `idorganizacion` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `pagos`
--
ALTER TABLE `pagos`
  MODIFY `idpago` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `paises`
--
ALTER TABLE `paises`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `participantes`
--
ALTER TABLE `participantes`
  MODIFY `idinscripcion` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `penas`
--
ALTER TABLE `penas`
  MODIFY `idpena` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `plataformas`
--
ALTER TABLE `plataformas`
  MODIFY `idplataforma` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `precios`
--
ALTER TABLE `precios`
  MODIFY `idprecio` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `preciosxcarreras`
--
ALTER TABLE `preciosxcarreras`
  MODIFY `idprecioxcarrera` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `social_profiles`
--
ALTER TABLE `social_profiles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tags`
--
ALTER TABLE `tags`
  MODIFY `idtag` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `users`
--
ALTER TABLE `users`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `videos`
--
ALTER TABLE `videos`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `social_profiles`
--
ALTER TABLE `social_profiles`
  ADD CONSTRAINT `social_profiles_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
