# MEJORES QUERYS

- En mysql usar UNION en lugar de OR
- En mysql se usa sólo un index por tabla en los where
- En mysql con index compuesto, el orden es importante que el primero sea el que más reduce resultados
- The HAVING clause should be only used with the GROUP BY clause, other than that we should always use the WHERE clause for filtering, as it uses Indexing. WHERE is always executed before GROUP BY, and HAVING is executed afterwards