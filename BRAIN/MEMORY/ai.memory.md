# AI FRAMEWORKS

- Lo más importante es el mindset: Cada tarea intentar primero con AI
- Genius como primera opción, después ChatGPT u otros en el navegador si necesito más contexto o subir archivos (yo ya se cuál usar)
- Pasar los Docs al LLM junto con el proyecto, antes de empezar a integrar un código con una API
- No necesito resúmen de información en noticias, uso sólo Newsletter (Gmail ya tiene AI) y Youtube (lo uso como entretenimiento)
- Audio a texto (en móvil y PC) con contacto Zapia en Whatsapp
- Herramientas de generación de vídeo incluyendo audio en marcadores para probar cual es mejor
- Types of Automation. **Traditional automation** is designed to execute repetitive tasks based on explicit, predefined rules determined by humans and does not learn or evolve. **AI automation** uses artificial intelligence capabilities like machine learning and natural language processing (NLP) to enable machines to learn from data and experiences, recognize patterns, and make human-like decisions. AI automation tools do adapt and evolve over time. **AI Agents** Similar to AI automation, AI agents are designed to make human-like decisions. However, unlike AI automation tools, AI agents take autonomous actions (i.e., without needing any human input).

# AI DATA

- We have a network of neurons (100 billions) connected to each other (10 000 connections per neuron), reacting to context, learning from experience, and generating an appropriate (but often hard to predict exactly) answer. In other words, apart from the fact that our algorithm is chemical rather than digital, the structure is similar.
- 3 orders of magnitude in complexity: The human brain has 1000 times more connections than GPT-4 has parameters. As a result, it can handle more complex situations.
- Ongoing learning: The brain keeps learning, including during a conversation, whereas GPT has finished its training long before the start of the conversation.
- Limited input: The only thing GPT knows about the conversation is the text. Up to 60% of human communication is nonverbal: the tone of voice, the rhythm of the voice, the facial expression, even some subconscious factors like smell play a part. GPT misses all of that.
- GPT doesn’t have emotions: Human emotions involve a lot of glands and hormones that have complex interactions with the brain.

## PLAN OPENAI

El mapa hacia la superinteligencia:
- Nivel 1 (Actual): IA conversacional, como ChatGPT. Interactúa en lenguaje natural pero con limitaciones.
- Nivel 2 "Razonadores": IA capaz de resolver problemas complejos al nivel de alguien con un doctorado, y sin herramientas externas.
- Nivel 3 "Agentes": Sistemas que pueden trabajar de forma autónoma durante días, gestionando varias tareas.
- Nivel 4 "Innovadores": IA que genera ideas originales y realiza descubrimientos científicos por sí misma.
- Nivel 5 "Organizaciones": IA con capacidad para dirigir operaciones complejas, como una empresa entera.
