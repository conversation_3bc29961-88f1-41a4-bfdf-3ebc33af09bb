# AFIPSDK

## PLAN GENERAL

Necesito un archivo a parte para llevar todo este trabajo que es grande. A nivel general los pasos son:

- [x] Generar posibilidad de distintas librerías [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)
- [x] Evaluar el plan de deploy, si va las librerías a BETA o ALFA
- [x] Pasar en BETA el plan C de AFIPSDK en funciones_wsfe (escribir plan y deployar a BETA)
- [x] [ARCA AFIPSDK en app](https://gitlab.com/saasargentina/app/-/issues/2159) funcionando en ALFA
- [x] Documentar el Lambda
- [x] Deploy de ALFA a BETA con test manual de a poco: 161,874,9589,10798,11166
- [x] Controlar los logs: 'rfce_mi_pyme.csv', 'afip_caido.csv'

- [ ] Estudiar todo lo que está dando vuelta en Google Groups
  - https://groups.google.com/g/pyafipws/c/Gc-QCxeQZ4s/m/XnD4qAb2BQAJ
  - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ
  - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ
  - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ
  - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ
  - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ
- [ ] Deploy BETA a PROD
- [ ] [ARCA Actualizar condiciones](https://gitlab.com/saasargentina/app/-/issues/2160)

- [ ] Agregar issue para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))
- [ ] Agregar QR también con AFIPSDK (https://afipsdk.com/blog/crear-qr-de-afip-en-php/) y buscar que otros métodos hay que migrar
- [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA
- [ ] Actualizar Pyafipws con Python 2.x como plan C



## Para pasar a otros issues o evaluar

- [ ] Agregar una ventana para poder ejecutar el antiafip manualmente y presente el informe todo dentro del módulo SaaS, con el mensaje de AFIP completo y toda la info necesaria para saber que está pasando.
- [ ] Evaluar si conviene cambiar los procesos de generación de certificados
- Tener en cuenta que hoy da Error de Razón Social con Ñ


## Acentar

Condiciones de IVA:
1   IVA Responsable Inscripto
2   IVA Responsable no Inscripto
3   IVA no Responsable
4   IVA Sujeto Exento
5   Consumidor Final
6   Responsable Monotributo
7   Sujeto no Categorizado
8   Proveedor del Exterior
9   Cliente del Exterior
10  IVA Liberado – Ley Nº 19.640
11  IVA Responsable Inscripto – Agente de Percepción
12  Pequeño Contribuyente Eventual
13  Monotributista Social
14  Pequeño Contribuyente Eventual Social

## PROMPTS

Estoy migrando un proceso de aprobación de ventas que son facturas electrónicas de Argentina. Voy a darte todo el contexto y los pasos que quiero realizar. Te pido que analices todo lo que vamos a modificar, pero que vayamos de a un paso a la vez.

En prompts anteriores ya ejecutamos los pasos 1, 2, 3, 4 y 13

## Ten en cuenta los siguientes temas:

- El proceso anterior utiliza una función en php que llama con la función `exec` a una librería que está en Python llamada pyafipws. Puedes ver la función `rece1` y las subfunciones que llama en el archivo #file:funciones_wsfe.php  NO MODIFIQUES NADA DE ESTE ARCHIVO. Para futura referencia más abajo de este prompt, lo voy a llamar "RECE"
- El proceso nuevo es una función de AWS Lambda que llama a una API con el SDK AFIPSDK. Puedes ver esta función en el archivo #file:afipsdk.php  SI MODIFICA ESTE ARCHIVO. Para futura referencia más abajo de este prompt lo voy a llamar "LAMBDA".
- La función `rece1` tiene 3 parámetros: `$id` que en el proceso nuevo es el `idventa` y otros 2 que no los vamos a necesitar migrar.

## La función `rece1` tiene los siguientes pasos:

1- Obtiene información de la venta. Esto hay que replicarlo en el método que ya existe `obtenerVenta` en LAMBDA, sólo modificando la consulta mysql.
2- Verifica si corresponde intentar emitir y si hay una factura del mismo tipo esperando CAE. Hay que agregar estas verificaciones en un nuevo método `debeAprobar` en LAMBDA que tenga estas 2 validaciones y si corresponde actualiza el registro y termina el proceso con el log correspondiente.
3- Descarga y bloquea el certificado con la función `bloquear_wsfe` de RECE y ya tenemos esto funcionando en `descargarCertificados` de LAMBDA, así que esto no hay que modificarlo.
4- Buscamos el número que dice ARCA que fue el último. Este proceso en RECE se hace con el método `rece1_ultimonumero` y hay que aplicarlo en un método de LAMBDA llamada `ultimoNumero` que se conecte con la API de AFIPSDK según la documentación que puedes ver en https://docs.afipsdk.com/siguientes-pasos/web-services/factura-electronica . En este mismo método, quiero aplicar también lo que se realiza en RECE `analizar_salida_ultimonumero` que pueden ser respuestas que vengan de AFIPSDK o no. También hay que agregar todas las verificaciones que tenemos en la función `rece1_verificar`. Hazlo simple.
5- Sigue actualizar número de venta. Esto es delicado, por lo que quiero que analices la funcion `actualizar_numero_venta` de RECE y que lo apliques en un nuevo método `actualizarNumero` pero con mucho cuidado de que funcione igual. Puedes revisar la consulta para ver si se puede mejorar su performance, pero es impresindible que esta función quede bien. Puedes meter ahí también el próximo paso que es verificar que la anterior esté ok
6- El próximo paso es Verifico que no tenga No Aplica si es RI y hay que replicarlo igual, este es fácil.
7- Hay un proceso que es `sin_control_numeracion` que su código es `return in_array($idempresa, [8905, 8980, 11597, 11854]);` . Este hay que migrarlo igual a un método `sinControlNumeracion` y aplicarlo en los mismos lugares.
8- Verifica y actualiza la fecha. Este proceso también hay que replicarlo en un método `actualizarFecha`
9- Luego tenemos el llamado a `generar_entrada_wsfe` que es el `prepararComprobante` en LAMBDA. Por el momento no toquemos este método.
10- Ahí si tenemos el llamado con el `exec` que ya está en el método `enviarFacturaAfip` que por ahora no lo toquemos.
11- La función `leer_salida_wsfe` de RECE es simplemente lo que devuelve `enviarFacturaAfip` así que no la necesitamos. Pero si la función `analizar_salida_wsfe` la tenemos que pasar con mucho cuidado de que funcione bien y quede bien registrada la devolución. Ahí hay un retorno de la función al array `$return` que quiero evitar, tiene que quedar más prolijo.
12- La función `desbloquear_wsfe` no tiene que pasarse porque ya limpiamos los certificados descargados y no hay que desbloquear nada.
13- Luego tenemos un par de líneas que analizan y actualizan la venta. Eso creo que puedes dejarlo bien prolijo en LAMBDA en el método `actualizarVentaExitosa`, que ya que estás cambia el nombre a `actualizarVenta` y que pueda actualizarse el estadocae a `rechazado` cuando corresponde. También hay que corregir los nombres de los campos que están mal y agregar `obscae` que es importante.
14- Por último quiero que se envíe el mail de error que está en ErrorHandler cuando la venta es rechazada o hay algún error.

