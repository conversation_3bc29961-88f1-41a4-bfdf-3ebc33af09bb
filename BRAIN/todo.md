# START 🏁

- [Hoy va a ser un buen día](TODO/buendia.md)


*******************************************************************************

# DOING 📆

- [x] Soporte SaaS
- [x] Responder mail a contadora
- [x] Ver presupuesto Chile

- [x] Hacer pedido a China Feibot

- [x] Revisar SaaS AFIPSDK
- [ ] Error que me pasó Gaby hoy
- [ ] Agregar códigos de descuentos a Smart Hyrox

- [ ] Actualizar Deploy SaaS
- [ ] MailZero SaaS
- [ ] Configurar Tría Cross
- [ ] Continuar con ARCA

---

- [ ] Agregar issues de las Consultas de ayuda y del Whatsapp API Datos Extras
- [ ] Enviar newsletter del Tría Cross por inscripciones y por votación
- [ ] Comprar BTC en OKX
- [ ] Tengo los logos de deportes

- [ ] Luz izq trasera


*******************************************************************************

# FLOW

**API ARCA**
- [ ] ISSUE https://gitlab.com/saasargentina/app/-/issues/2088

**Growth Crono**
- [ ] Actualizar novedades y recuperar newsletter
- [ ] Generar contenido de novedades y para que tenga Prisci, ya está en [CONTENIDO](./CONTENIDO.md)


# QUICK

@mobile
- [ ] Comprar sillas más chicas para crono
- [ ] Configurar Streamings con Meli+

@offline
- [ ] Colgar macetas en el patio
- [ ] Amurar escritorio Mati y sillón


@crono
- [ ] No me llega el botón de pago para el evento de Moxi 2718
- [ ] Cerrar eventos viejos y pasar deudores a Juli
- [ ] Enganchado
- [ ] Probar y ordenar cables


*******************************************************************************

# LATER
